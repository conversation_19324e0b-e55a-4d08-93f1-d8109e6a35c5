namespace JT.Content.Client.Mock;


public class MockTransferEndpoints(string basePath, ISerializer serializer, ILogger<BaseMockEndpoint> logger) : BaseMockEndpoint(serializer, logger)
{
    public async Task<string> HandleTransferRequestsRequest(HttpRequestMessage request)
    {
        var savedList = await LoadData<List<Guid>>("SavedTransferRequests.json") ?? [];
        var allTransferRequests = await LoadData<List<TransferRequestData>>("TransferRequests.json") ?? [];

        allTransferRequests.ForEach((_, r) => r.IsFavorite = savedList.Contains(r.Id ?? Guid.Empty));

        var path = request.RequestUri?.AbsolutePath ?? string.Empty;
        if (path.Contains("/api/TransferRequest/categories"))
        {
            return await HandleCategoriesRequest();
        }

        if (path.Contains("/api/TransferRequest/trending"))
        {
            return serializer.ToString(allTransferRequests.Take(10));
        }

        if (path.Contains("/api/TransferRequest/popular"))
        {
            return serializer.ToString(allTransferRequests.Take(10));
        }

        if (path.Contains("/api/TransferRequest/favorited"))
        {
            return serializer.ToString(allTransferRequests.Where(r => r.IsFavorite ?? false).ToList());
        }

        if (path.Contains("/steps"))
        {
            var segments = request.RequestUri?.Segments;
            var segmentId = segments?.Length >= 2 ? segments[^2] : string.Empty;
            return GetTransferSteps(allTransferRequests, segmentId);
        }

        if (path.Contains("/ingredients"))
        {
            var segments = request.RequestUri?.Segments;
            var segmentId = segments?.Length >= 2 ? segments[^2] : string.Empty;
            return GetTransferIngredients(allTransferRequests, segmentId);
        }

        if (path.Contains("/reviews"))
        {
            var segments = request.RequestUri?.Segments;
            var segmentId = segments?.Length >= 2 ? segments[^2] : string.Empty;
            return GetTransferReviews(allTransferRequests, segmentId);
        }

        if (request.Method == HttpMethod.Get && path == "/api/TransferRequest")
        {
            return serializer.ToString(allTransferRequests);
        }

        if (path.Contains("/api/TransferRequest/review/like"))
        {
            // Review functionality not applicable to transfer requests
            return "{}";
        }

        if (path.Contains("/api/TransferRequest/review/dislike"))
        {
            // Review functionality not applicable to transfer requests
            return "{}";
        }

        var lastSegment = request.RequestUri?.Segments?.LastOrDefault() ?? string.Empty;
        return GetTransferDetails(allTransferRequests, lastSegment);
    }

    private string GetTransferDetails(List<TransferRequestData> allTransferRequests, string transferId)
    {
        transferId = transferId.TrimEnd('/');
        if (Guid.TryParse(transferId, out var gid))
        {
            var transfer = allTransferRequests.FirstOrDefault(x => x.Id == gid);
            if (transfer != null)
            {
                return serializer.ToString(transfer);
            }
        }

        return "{}";
    }

    private async Task<string> HandleCategoriesRequest()
    {
        var allCategories = await LoadData<List<CategoryData>>("Categories.json") ?? new List<CategoryData>();
        return serializer.ToString(allCategories);
    }

    private static string GetTransferSteps(List<TransferRequestData> allTransferRequests, string transferId)
    {
        transferId = transferId.TrimEnd('/');

        if (Guid.TryParse(transferId, out var parsedId))
        {
            var transferRequest = allTransferRequests.FirstOrDefault(r => r.Id == parsedId);
            if (transferRequest != null && transferRequest.Steps != null)
            {
                return serializer.ToString(transferRequest.Steps);
            }
        }

        return "[]";
    }

    private static string GetTransferIngredients(List<TransferRequestData> allTransferRequests, string transferId)
    {
        // Transfer requests don't have ingredients - return empty array
        _ = allTransferRequests; // Suppress unused parameter warning
        _ = transferId; // Suppress unused parameter warning
        return "[]";
    }

    private string GetTransferReviews(List<TransferRequestData> allTransferRequests, string transferId)
    {
        transferId = transferId.TrimEnd('/');

        if (Guid.TryParse(transferId, out var parsedId))
        {
            var transferRequest = allTransferRequests.FirstOrDefault(r => r.Id == parsedId);
            if (transferRequest != null && transferRequest.Responses != null)
            {
                // Return employer responses as "reviews" for transfer requests
                return serializer.ToString(transferRequest.Responses);
            }
        }

        return "[]";
    }


}




//public class MockTransferEndpoints(string basePath, ISerializer serializer, ILogger<BaseMockEndpoint> logger) : BaseMockEndpoint(serializer, logger)
//{
//	public async Task<string> HandleTransferRequestsRequest(HttpRequestMessage request)
//	{
//		var savedList = await LoadData<List<Guid>>(MockApiConstants.SavedTransferRequestsFile) ?? [];
//		var allTransferRequests = await LoadData<List<TransferRequestData>>(MockApiConstants.TransferRequestsFile) ?? [];

//		allTransferRequests.ForEach((_, r) => r.IsFavorite = savedList.Contains(r.Id ?? Guid.Empty));

//		return await RouteRequest(request, allTransferRequests);
//	}

//	private async Task<string> RouteRequest(HttpRequestMessage request, List<TransferRequestData> allTransferRequests)
//	{
//		var path = request.RequestUri?.AbsolutePath ?? string.Empty;
//		var segments = request.RequestUri?.Segments;

//		if (path.Contains(MockApiConstants.JobCategoryPath)) return await HandleCategoriesRequest();
//		if (path.Contains(MockApiConstants.TrendingPath)) return serializer.ToString(allTransferRequests.Take(10));
//		if (path.Contains(MockApiConstants.PopularPath)) return serializer.ToString(allTransferRequests.Take(10));
//		if (path.Contains(MockApiConstants.FavoritedPath)) return serializer.ToString(allTransferRequests.Where(r => r.IsFavorite ?? false).ToList());

//		var segmentId = segments?.Length >= 2 ? segments[^2] : string.Empty;
//		if (path.Contains(MockApiConstants.StepsPath)) return GetTransferRequestSteps(allTransferRequests, segmentId);
//		if (path.Contains(MockApiConstants.IngredientsPath)) return GetTransferRequestIngredients(allTransferRequests, segmentId);
//		if (path.Contains(MockApiConstants.ReviewsPath)) return GetTransferRequestReviews(allTransferRequests, segmentId);

//		if (request.Method == HttpMethod.Get && path == MockApiConstants.TransferRequestBasePath) return serializer.ToString(allTransferRequests);

//		if (path.Contains(MockApiConstants.ReviewLikePath) || path.Contains(MockApiConstants.ReviewDislikePath)) return "{}";

//		var lastSegment = segments?.LastOrDefault() ?? string.Empty;
//		return GetTransferRequestDetails(allTransferRequests, lastSegment);
//	}

//	private string GetTransferRequestDetails(List<TransferRequestData> allTransferRequests, string transferRequestId)
//	{
//        transferRequestId = transferRequestId.TrimEnd('/');
//		if (Guid.TryParse(transferRequestId, out var gid))
//		{
//			var transfer = allTransferRequests.FirstOrDefault(x => x.Id == gid);
//			if (transfer != null)
//			{
//				return serializer.ToString(transfer);
//			}
//		}

//		return "{}";
//	}

//	private async Task<string> HandleCategoriesRequest()
//	{
//		var allCategories = await LoadData<List<CategoryData>>(MockApiConstants.JobCategoriesFile) ?? new List<CategoryData>();
//        return serializer.ToString(allCategories);
//	}

//    private string GetTransferRequestSteps(List<TransferRequestData> allTransfers, string transferRequestId)
//    {
//        transferRequestId = transferRequestId.TrimEnd('/');

//        if (Guid.TryParse(transferRequestId, out var parsedId))
//        {
//            var transferRequest = allTransfers.FirstOrDefault(r => r.Id == parsedId);
//            if (transferRequest != null && transferRequest.Steps != null)
//            {
//                return serializer.ToString(transferRequest.Steps);
//            }
//        }

//        return "[]";
//    }
//    private static string GetTransferRequestIngredients(List<TransferRequestData> allTransferRequests, string transferRequestId)
//	{
//		// Transfer requests don't have ingredients - return empty array
//		_ = allTransferRequests; // Suppress unused parameter warning
//		_ = transferRequestId; // Suppress unused parameter warning
//		return "[]";
//	}

//	private string GetTransferRequestReviews(List<TransferRequestData> allTransferRequests, string transferRequestId)
//	{
//        transferRequestId = transferRequestId.TrimEnd('/');

//		if (Guid.TryParse(transferRequestId, out var parsedId))
//		{
//			var transferRequest = allTransferRequests.FirstOrDefault(r => r.Id == parsedId);
//			if (transferRequest != null && transferRequest.Responses != null)
//			{
//				// Return employer responses as "reviews" for transfer requests
//				return serializer.ToString(transferRequest.Responses);
//			}
//		}

//		return "[]";
//	}


//}
