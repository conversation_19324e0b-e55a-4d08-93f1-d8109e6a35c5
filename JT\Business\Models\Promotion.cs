using PromotionData = JT.Client.Models.PromotionData;

namespace JT.Business.Models;

public partial record Promotion
{
	internal Promotion(PromotionData promotionData)
	{
		Id = promotionData.Id ?? string.Empty;
		CompanyId = promotionData.CompanyId ?? string.Empty;
		CompanyName = promotionData.CompanyName ?? string.Empty;
		Title = promotionData.Title ?? string.Empty;
		Description = promotionData.Description ?? string.Empty;
		Type = promotionData.Type ?? string.Empty;
		Category = promotionData.Category ?? string.Empty;
		ImageUrl = promotionData.ImageUrl;
		VideoUrl = promotionData.VideoUrl;
		WebsiteUrl = promotionData.WebsiteUrl ?? string.Empty;
		ContactEmail = promotionData.ContactEmail ?? string.Empty;
		ContactPhone = promotionData.ContactPhone ?? string.Empty;
		BudgetOMR = promotionData.BudgetOMR ?? 0;
		DurationDays = promotionData.DurationDays ?? 0;
		TargetAudience = promotionData.TargetAudience ?? string.Empty;
		Keywords = promotionData.Keywords?.ToImmutableList() ?? ImmutableList<string>.Empty;
		Status = promotionData.Status ?? "pending";
		Priority = promotionData.Priority ?? "normal";
		StartDate = promotionData.StartDate;
		EndDate = promotionData.EndDate;
		CreatedAt = promotionData.CreatedAt ?? DateTime.MinValue;
		ApprovedAt = promotionData.ApprovedAt;
		ApprovedBy = promotionData.ApprovedBy;
		RejectionReason = promotionData.RejectionReason;
		ViewCount = promotionData.ViewCount ?? 0;
		ClickCount = promotionData.ClickCount ?? 0;
		ConversionCount = promotionData.ConversionCount ?? 0;
		TotalSpent = promotionData.TotalSpent ?? 0;
		CostPerClick = promotionData.CostPerClick ?? 0;
		CostPerView = promotionData.CostPerView ?? 0;
	}

	public string Id { get; init; }
	public string CompanyId { get; init; }
	public string CompanyName { get; init; }
	public string Title { get; init; }
	public string Description { get; init; }
	public string Type { get; init; } // banner, sponsored_content, job_promotion, directory_listing, event
	public string Category { get; init; } // industry category
	public string? ImageUrl { get; init; }
	public string? VideoUrl { get; init; }
	public string WebsiteUrl { get; init; }
	public string ContactEmail { get; init; }
	public string ContactPhone { get; init; }
	public decimal BudgetOMR { get; init; }
	public int DurationDays { get; init; }
	public string TargetAudience { get; init; }
	public IImmutableList<string> Keywords { get; init; }
	public string Status { get; init; } // pending, approved, active, paused, completed, rejected
	public string Priority { get; init; } // low, normal, high, premium
	public DateTime? StartDate { get; init; }
	public DateTime? EndDate { get; init; }
	public DateTime CreatedAt { get; init; }
	public DateTime? ApprovedAt { get; init; }
	public string? ApprovedBy { get; init; }
	public string? RejectionReason { get; init; }
	public int ViewCount { get; init; }
	public int ClickCount { get; init; }
	public int ConversionCount { get; init; }
	public decimal TotalSpent { get; init; }
	public decimal CostPerClick { get; init; }
	public decimal CostPerView { get; init; }

	// Computed properties
	public string StatusColor => Status switch
	{
		"approved" => "#28A745",
		"active" => "#007BFF",
		"pending" => "#FFC107",
		"rejected" => "#DC3545",
		"paused" => "#6C757D",
		"completed" => "#17A2B8",
		_ => "#6C757D"
	};

	public string StatusDisplayName => Status switch
	{
		"pending" => "Pending Review",
		"approved" => "Approved",
		"active" => "Active",
		"paused" => "Paused",
		"completed" => "Completed",
		"rejected" => "Rejected",
		_ => Status
	};

	public string TypeDisplayName => Type switch
	{
		"banner" => "Banner Advertisement",
		"sponsored_content" => "Sponsored Content",
		"job_promotion" => "Job Promotion",
		"directory_listing" => "Directory Listing",
		"event" => "Event Promotion",
		_ => Type
	};

	public bool IsActive => Status == "active" && 
		(!EndDate.HasValue || DateTime.UtcNow <= EndDate.Value) &&
		(!StartDate.HasValue || DateTime.UtcNow >= StartDate.Value);

	public bool IsExpired => EndDate.HasValue && DateTime.UtcNow > EndDate.Value;
	public bool IsPending => Status == "pending";
	public bool IsApproved => Status == "approved";
	public bool IsRejected => Status == "rejected";

	public int DaysRemaining => EndDate.HasValue ? Math.Max(0, (int)(EndDate.Value - DateTime.UtcNow).TotalDays) : 0;
	public decimal ClickThroughRate => ViewCount > 0 ? (decimal)ClickCount / ViewCount * 100 : 0;
	public decimal ConversionRate => ClickCount > 0 ? (decimal)ConversionCount / ClickCount * 100 : 0;
	public decimal AverageEngagement => ViewCount > 0 ? (ClickCount + ConversionCount) / (decimal)ViewCount * 100 : 0;

	public string BudgetDisplay => $"{BudgetOMR:F2} OMR";
	public string DurationDisplay => DurationDays == 1 ? "1 day" : $"{DurationDays} days";
	public string TotalSpentDisplay => $"{TotalSpent:F2} OMR";

	public decimal BudgetRemaining => Math.Max(0, BudgetOMR - TotalSpent);
	public decimal BudgetUsedPercentage => BudgetOMR > 0 ? (TotalSpent / BudgetOMR) * 100 : 0;

	public string PerformanceRating => AverageEngagement switch
	{
		>= 10 => "Excellent",
		>= 5 => "Good",
		>= 2 => "Average",
		>= 1 => "Below Average",
		_ => "Poor"
	};

	internal PromotionData ToData() => new()
	{
		Id = Id,
		CompanyId = CompanyId,
		CompanyName = CompanyName,
		Title = Title,
		Description = Description,
		Type = Type,
		Category = Category,
		ImageUrl = ImageUrl,
		VideoUrl = VideoUrl,
		WebsiteUrl = WebsiteUrl,
		ContactEmail = ContactEmail,
		ContactPhone = ContactPhone,
		BudgetOMR = BudgetOMR,
		DurationDays = DurationDays,
		TargetAudience = TargetAudience,
		Keywords = Keywords.ToList(),
		Status = Status,
		Priority = Priority,
		StartDate = StartDate,
		EndDate = EndDate,
		CreatedAt = CreatedAt,
		ApprovedAt = ApprovedAt,
		ApprovedBy = ApprovedBy,
		RejectionReason = RejectionReason,
		ViewCount = ViewCount,
		ClickCount = ClickCount,
		ConversionCount = ConversionCount,
		TotalSpent = TotalSpent,
		CostPerClick = CostPerClick,
		CostPerView = CostPerView
	};
}
