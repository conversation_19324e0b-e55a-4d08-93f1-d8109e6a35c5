namespace JT.Server.Entities;

public class PaymentTransactionData
{
	public string? Id { get; set; }
	public Guid UserId { get; set; }
	public string? SubscriptionPlanId { get; set; }
	public decimal Amount { get; set; }
	public string? Currency { get; set; }
	public string? PaymentMethod { get; set; }
	public string? PaymentGateway { get; set; }
	public string? Status { get; set; }
	public string? TransactionId { get; set; }
	public object? GatewayResponse { get; set; }
	public decimal ServiceFee { get; set; }
	public decimal GatewayFee { get; set; }
	public decimal NetAmount { get; set; }
	public DateTime CreatedAt { get; set; }
	public DateTime? CompletedAt { get; set; }
	public DateTime? FailedAt { get; set; }
	public DateTime? ExpiresAt { get; set; }
	public string? ErrorMessage { get; set; }
}
