using PaymentTransactionData = JT.Client.Models.PaymentTransactionData;

namespace JT.Business.Models;

public partial record PaymentTransaction
{
	internal PaymentTransaction(PaymentTransactionData paymentTransactionData)
	{
		Id = paymentTransactionData.Id;
		UserId = paymentTransactionData.UserId ?? Guid.Empty;
		SubscriptionPlanId = paymentTransactionData.SubscriptionPlanId;
		Amount = paymentTransactionData.Amount ?? 0;
		Currency = paymentTransactionData.Currency;
		PaymentMethod = paymentTransactionData.PaymentMethod;
		PaymentGateway = paymentTransactionData.PaymentGateway;
		Status = paymentTransactionData.Status;
		TransactionId = paymentTransactionData.TransactionId;
		GatewayResponse = paymentTransactionData.GatewayResponse;
		ServiceFee = paymentTransactionData.ServiceFee ?? 0;
		GatewayFee = paymentTransactionData.GatewayFee ?? 0;
		NetAmount = paymentTransactionData.NetAmount ?? 0;
		CreatedAt = paymentTransactionData.CreatedAt ?? DateTime.MinValue;
		CompletedAt = paymentTransactionData.CompletedAt;
		FailedAt = paymentTransactionData.FailedAt;
		ExpiresAt = paymentTransactionData.ExpiresAt;
		ErrorMessage = paymentTransactionData.ErrorMessage;
	}

	public string? Id { get; init; }
	public Guid UserId { get; init; }
	public string? SubscriptionPlanId { get; init; }
	public decimal Amount { get; init; }
	public string? Currency { get; init; }
	public string? PaymentMethod { get; init; }
	public string? PaymentGateway { get; init; }
	public string? Status { get; init; }
	public string? TransactionId { get; init; }
	public object? GatewayResponse { get; init; }
	public decimal ServiceFee { get; init; }
	public decimal GatewayFee { get; init; }
	public decimal NetAmount { get; init; }
	public DateTime CreatedAt { get; init; }
	public DateTime? CompletedAt { get; init; }
	public DateTime? FailedAt { get; init; }
	public DateTime? ExpiresAt { get; init; }
	public string? ErrorMessage { get; init; }

	// Computed properties
	public string StatusDisplayName => Status switch
	{
		"completed" => "Completed",
		"pending" => "Pending",
		"failed" => "Failed",
		"cancelled" => "Cancelled",
		_ => Status ?? "Unknown"
	};

	public string StatusColor => Status switch
	{
		"completed" => "#28A745",
		"pending" => "#FFC107",
		"failed" => "#DC3545",
		"cancelled" => "#6C757D",
		_ => "#6C757D"
	};

	public string PaymentMethodDisplayName => PaymentMethod switch
	{
		"thawani" => "Thawani Pay",
		"zaincash" => "ZainCash",
		"card" => "Credit Card",
		_ => PaymentMethod ?? "Unknown"
	};

	public bool IsCompleted => Status == "completed";
	public bool IsPending => Status == "pending";
	public bool IsFailed => Status == "failed";
	public bool IsExpired => ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;

	public string AmountDisplay => $"{Amount:F2} {Currency}";
	public string NetAmountDisplay => $"{NetAmount:F2} {Currency}";
	public decimal TotalFees => ServiceFee + GatewayFee;

	internal PaymentTransactionData ToData() => new()
	{
		Id = Id,
		UserId = UserId,
		SubscriptionPlanId = SubscriptionPlanId,
		Amount = Amount,
		Currency = Currency,
		PaymentMethod = PaymentMethod,
		PaymentGateway = PaymentGateway,
		Status = Status,
		TransactionId = TransactionId,
		GatewayResponse = GatewayResponse,
		ServiceFee = ServiceFee,
		GatewayFee = GatewayFee,
		NetAmount = NetAmount,
		CreatedAt = CreatedAt,
		CompletedAt = CompletedAt,
		FailedAt = FailedAt,
		ExpiresAt = ExpiresAt,
		ErrorMessage = ErrorMessage
	};
}
