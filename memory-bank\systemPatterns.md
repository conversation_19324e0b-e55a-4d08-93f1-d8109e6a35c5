# System Patterns - JT Job Transfer Application

## Production System Architecture

### High-Level Architecture (Production-Ready)
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │   JT.Server     │    │   Data Storage  │
│                 │    │                 │    │                 │
│ • Windows       │◄──►│ • REST API      │◄──►│ • JSON Files    │
│ • Android       │    │ • Authentication│    │ • Token Cache   │
│ • iOS           │    │ • Token Economy │    │ • User Data     │
│ • WebAssembly   │    │ • Promotions    │    │ • Promotions    │
│ • Desktop       │    │ • Subscriptions │    │ • Analytics     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Production Project Structure
- **JT**: Cross-platform Uno Platform application (Production-ready UI/UX)
- **JT.Server**: Complete ASP.NET Core Web API (6 controllers, full CRUD)
- **JT.Identity**: Custom authentication service (Phone/password with JWT)
- **JT.DataContracts**: Comprehensive shared models (17 entities)
- **AppData**: Production-ready JSON data storage (17 data files)
- **Specs**: Complete OpenAPI 3.0.4 specifications

## Business Architecture Patterns

### 1. Commercial Promotion System
**Implementation**: Complete 8-revenue-stream system
**Components**:
- Reduces development and maintenance overhead
- Ensures UI consistency across platforms
- Leverages existing .NET and XAML knowledge
- Strong community and Microsoft backing

### 2. Architecture Pattern
**Decision**: MVVM with Reactive Extensions
**Rationale**:
- Clean separation of concerns
- Testable business logic
- Data binding support in XAML
- Reactive programming for async operations

### 3. Authentication Strategy
**Decision**: IdentityServer4 with custom authentication
**Current Issue**: IdentityServer4 is deprecated, needs migration to Duende
**Pattern**: OAuth 2.0 / OpenID Connect with JWT tokens

### 4. Data Access Pattern
**Decision**: HTTP client with generated API client (Kiota)
**Benefits**:
- Type-safe API calls
- Automatic serialization/deserialization
- Consistent error handling
- Mock-friendly for testing

## Design Patterns in Use

### 1. Model-View-ViewModel (MVVM)
```csharp
// ViewModels expose data and commands to Views
public partial record HomeModel
{
    public IListState<Recipe> TrendingNow => ListState
        .Async(this, _recipeService.GetTrending)
        .Observe(_messenger, r => r.Id);
}
```

### 2. Dependency Injection
```csharp
// Services registered in App.xaml.host.cs
services.AddSingleton<ICookbookService, CookbookService>()
        .AddSingleton<IRecipeService, RecipeService>()
        .AddSingleton<IUserService, UserService>();
```

### 3. Repository Pattern (Service Layer)
```csharp
public class RecipeService : IRecipeService
{
    public async ValueTask<IImmutableList<Recipe>> GetAll(CancellationToken ct)
    {
        var recipesData = await api.Api.Recipe.GetAsync(cancellationToken: ct);
        return recipesData?.Select(r => new Recipe(r)).ToImmutableList();
    }
}
```

### 4. Messaging Pattern
```csharp
// Using CommunityToolkit.Mvvm.Messaging for loose coupling
messenger.Send(new EntityMessage<Cookbook>(EntityChange.Created, cookbook));
```

### 5. Mock Pattern for Development
```csharp
// Comprehensive mocking system for offline development
#if USE_MOCKS
services.AddTransient<MockHttpMessageHandler>();
#endif
```

## Component Relationships

### Client-Side Architecture
```
┌─────────────┐
│    Views    │ (XAML Pages)
└─────┬───────┘
      │ Data Binding
┌─────▼───────┐
│ ViewModels  │ (Business Logic)
└─────┬───────┘
      │ Service Calls
┌─────▼───────┐
│  Services   │ (Data Access)
└─────┬───────┘
      │ HTTP Calls
┌─────▼───────┐
│ API Client  │ (Generated)
└─────────────┘
```

### Server-Side Architecture
```
┌─────────────┐
│ Controllers │ (API Endpoints)
└─────┬───────┘
      │
┌─────▼───────┐
│  Services   │ (Business Logic)
└─────┬───────┘
      │
┌─────▼───────┐
│ Data Layer  │ (JSON Files → Future DB)
└─────────────┘
```

## Critical Implementation Paths

### 1. Authentication Flow
1. User initiates login → Client redirects to JT.Identity
2. IdentityServer validates credentials → Issues JWT token
3. Client stores token → Includes in API requests
4. JT.Server validates token → Processes authenticated requests

### 2. Data Synchronization
1. Client makes API call → JT.Server processes request
2. Server updates data → Returns response
3. Client updates local state → UI reflects changes
4. Messaging system notifies other components

### 3. Cross-Platform Navigation
1. User action triggers navigation → ViewModel processes command
2. Navigation service routes request → Finds target view
3. View instantiated with data → ViewModel bound to view
4. Platform-specific rendering → Native UI displayed

## Architectural Strengths
- **Clean Separation**: Clear boundaries between layers
- **Testability**: Services and ViewModels are easily testable
- **Scalability**: Architecture supports growth and new features
- **Maintainability**: Consistent patterns throughout codebase

## Architectural Concerns
- **Service Registration**: Many services commented out, incomplete DI setup
- **Error Handling**: Inconsistent error handling patterns
- **Data Persistence**: Currently using JSON files, needs database integration
- **Authentication**: Deprecated IdentityServer4 needs modernization
- **Testing**: Architecture supports testing but tests are missing

## Future Architecture Considerations
- **Database Integration**: Replace JSON files with proper database
- **Caching Strategy**: Implement client and server-side caching
- **Offline Support**: Add offline-first capabilities
- **Microservices**: Consider splitting services as application grows
- **Event Sourcing**: For audit trails and data consistency
