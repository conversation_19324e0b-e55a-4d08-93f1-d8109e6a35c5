using JT.Business.Services.JobTransfers;
using JT.Business.Services.TransferRequests;
using JT.Presentation.Extensions;

namespace JT.Presentation;

public partial record CreateUpdateJobTransferModel
{
	const uint DefaultPageSize = 20;
	public IState<IImmutableList<TransferRequest>> SelectedTransfers { get; }

	private readonly INavigator _navigator;
    private readonly ITransferRequestService _transferRequestService;
    private readonly IJobTransferService _jobTransfersService;
	private readonly IMessenger _messenger;
	private readonly JobTransfer? _JobTransfer;

	public CreateUpdateJobTransferModel(
		INavigator navigator,
        ITransferRequestService transferRequestService,
        IJobTransferService jobTransfersService,
        JobTransfer? jobTransfer,
		IMessenger messenger)
	{
		_navigator = navigator;
        _transferRequestService = transferRequestService;
        _jobTransfersService = jobTransfersService;
		_messenger = messenger;

		if (jobTransfer is not null)
		{
            _JobTransfer = jobTransfer;
			Title = "Update JobTransfer";
			SubTitle = "Manage jobTransfer's transferRequests";
			SaveButtonContent = "Apply change";
			IsCreate = false;
		}
		else
		{
			Title = "Create JobTransfer";
			SubTitle = "Add transferRequests";
			SaveButtonContent = "Create jobTransfer";
			IsCreate = true;
		}
		SelectedTransfers = State.Value(this, () => _JobTransfer?.TransferRequests ?? ImmutableList<TransferRequest>.Empty);

	}
	public bool IsCreate { get; }

	public string Title { get; }

	public string SubTitle { get; }

	public string SaveButtonContent { get; }

	public IState<JobTransfer> JobTransfer => State
		.Value(this, () => _JobTransfer ?? new JobTransfer())
		.Observe(_messenger, cb => cb.Id);

	public IListFeed<TransferRequest> TransferRequests => ListFeed
		.PaginatedAsync(
			async (PageRequest pageRequest, CancellationToken ct) =>
				await _transferRequestService.GetFavoritedWithPagination(pageRequest.DesiredSize ?? DefaultPageSize, pageRequest.CurrentCount, ct)
		)
		.Selection(SelectedTransfers);

	public async ValueTask Submit(CancellationToken ct)
	{
		var selectedTransferRequests = await SelectedTransfers;
		var jobTransfer = await JobTransfer;

		if (selectedTransferRequests is { Count: > 0 } && jobTransfer is not null && jobTransfer.Name.HasValueTrimmed())
		{
			var response = IsCreate
				? await _jobTransfersService.Create(jobTransfer.Name!, selectedTransferRequests.ToImmutableList(), ct)
				: await _jobTransfersService.Update(jobTransfer, selectedTransferRequests, ct);

			if (IsCreate)
			{
				await _jobTransfersService.Save(response!, ct);
			}

			await _navigator.NavigateBackAsync(this);
		}
		else
		{
			await _navigator.ShowDialog(this, new DialogInfo("Error", "Please write a jobTransfer name and select one transferRequest."), ct);
		}
	}
}
