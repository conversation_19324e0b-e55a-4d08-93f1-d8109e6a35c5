using System.Text.Json.Serialization;

namespace JT.Business.Models;

public record AppConfig
{
    public string? Environment { get; init; }
    public string? Title { get; init; }
    public bool? IsDark { get; init; }
    public bool? Notification { get; init; }
    public string? AccentColor { get; init; }
    public AppTheme Theme { get; set; } = AppTheme.System;
}

[JsonSerializable(typeof(AppConfig))]
[JsonSerializable(typeof(Dictionary<string, AppConfig>))]
public partial class AppConfigContext : JsonSerializerContext
{
}
