<?xml version="1.0" encoding="utf-8" ?>
<resources>
	<style name="AppTheme" parent="Theme.MaterialComponents.Light">
		<!-- This removes the ActionBar -->
		<item name="windowActionBar">false</item>
		<item name="android:windowActionBar">false</item>
		<item name="windowNoTitle">true</item>
		<item name="android:windowNoTitle">true</item>
	</style>

	<style name="Theme.App.Starting" parent="Theme.SplashScreen">
		<!-- uno_splash_color and uno_splash_image are generated by Uno.Resizetizer -->
		<!-- This property is used for the splash screen -->
		<item name="android:windowSplashScreenBackground">@color/uno_splash_color</item>
		<item name="android:windowBackground">@drawable/uno_splash_image</item>
		<item name="android:windowSplashScreenAnimatedIcon">@drawable/uno_splash_image</item>

		<item name="postSplashScreenTheme">@style/AppTheme</item>
	</style>

	<style name="Theme.AppCompat.Translucent">
		<item name="android:windowIsTranslucent">true</item>
		<item name="android:windowAnimationStyle">@android:style/Animation</item>
	</style>
</resources>
