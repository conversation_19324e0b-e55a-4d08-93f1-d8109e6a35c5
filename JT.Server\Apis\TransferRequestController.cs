using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Apis;

/// <summary>
/// Controller for managing transfer requests.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class TransferRequestController : JTControllerBase
{
    private readonly string _transferRequestsFilePath = "TransferRequests.json";
    private readonly string _savedTransferRequestsFilePath = "SavedTransferRequests.json";
    private readonly string _CategoriesFilePath = "Categories.json";
    private readonly string _industriesFilePath = "Industries.json";


    /// <summary>
    /// Logger for the TransferRequestController.
    /// </summary>
    private readonly ILogger<TransferRequestController> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="TransferRequestController"/> class.
    /// </summary>
    /// <param name="logger">The logger instance for logging operations.</param>
    public TransferRequestController(ILogger<TransferRequestController> logger) => _logger = logger;


    /// <summary>
    /// Retrieves popular transfers.
    /// </summary>
    /// <returns>A list of popular transfers.</returns>
    [HttpGet("popular")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<TransferRequestData>), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<TransferRequestData>> GetPopular()
    {
        var transfers = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
        var popular = transfers.Take(15).ToImmutableList();
        return Ok(popular);
    }

    /// <summary>
    /// Retrieves all transfer requests.
    /// </summary>
    /// <returns>A collection of transfer requests.</returns>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<TransferRequestData>), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<TransferRequestData>> GetAll()
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            return Ok(transferRequests.ToImmutableList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transfer requests");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves a specific transfer request by its unique identifier.
    /// </summary>
    /// <param name="id">The unique identifier of the transfer request to retrieve.</param>
    /// <returns>The transfer request data if found, or a 404 Not Found response if not found.</returns>
    [HttpGet("{id:guid}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<TransferRequestData> GetTransferRequest(Guid id)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var transferRequest = transferRequests.FirstOrDefault(r => r.Id == id);

            if (transferRequest == null)
            {
                return NotFound();
            }

            return Ok(transferRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transfer request {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves the count of transfers for a specific user.
    /// </summary>
    /// <param name="userId">The user ID.</param>
    /// <returns>The count of transfers for the user.</returns>
    [HttpGet("count")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(int), 200)]
    [ProducesResponseType(404)]
    public ActionResult<int> GetCount([FromQuery] Guid userId)
    {
        var transfers = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
        var count = transfers.Count(r => r.UserId == userId);
        return Ok(count);
    }

    /// <summary>
    /// Retrieves all transfer categories.
    /// </summary>
    /// <returns>A list of categories.</returns>
    [HttpGet("categories")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<CategoryData>), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<CategoryData>> GetCategories()
    {
        var categories = LoadData<List<CategoryData>>(_CategoriesFilePath);
        return Ok(categories.ToImmutableList());
    }

    /// <summary>
    /// Creates a new transfer request.
    /// </summary>
    /// <param name="transferRequest">The transfer request data to create.</param>
    /// <returns>The created transfer request with its generated ID and metadata.</returns>
    [HttpPost("createtransferRequest")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<TransferRequestData>> CreateTransferRequest(TransferRequestData transferRequest)
    {
        try
        {
            transferRequest.Id = Guid.NewGuid();
            transferRequest.SubmissionDate = DateTime.UtcNow;
            transferRequest.Status = "Pending";
            transferRequest.ViewCount = 0;
            transferRequest.InterestedEmployers = 0;

            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            transferRequests.Add(transferRequest);
            await SaveMockData(_transferRequestsFilePath, transferRequests);

            return CreatedAtAction(nameof(GetTransferRequest), new { id = transferRequest.Id }, transferRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating transfer request");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Updates an existing transfer request.
    /// </summary>
    /// <param name="id">The unique identifier of the transfer request to update.</param>
    /// <param name="transferRequest">The updated transfer request data.</param>
    /// <returns>The updated transfer request if successful, or an appropriate error response.</returns>
    [HttpPut("{id:guid}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<TransferRequestData>> UpdateTransferRequest(Guid id, TransferRequestData transferRequest)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var existingIndex = transferRequests.FindIndex(r => r.Id == id);

            if (existingIndex == -1)
            {
                return NotFound();
            }

            transferRequest.Id = id;
            transferRequests[existingIndex] = transferRequest;
            await SaveMockData(_transferRequestsFilePath, transferRequests);

            return Ok(transferRequest);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating transfer request {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Deletes an existing transfer request.
    /// </summary>
    /// <param name="id">The unique identifier of the transfer request to delete.</param>
    /// <returns>No content if successful, or an appropriate error response.</returns>
    [HttpDelete("{id:guid}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> DeleteTransferRequest(Guid id)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var existingIndex = transferRequests.FindIndex(r => r.Id == id);

            if (existingIndex == -1)
            {
                return NotFound();
            }

            transferRequests.RemoveAt(existingIndex);
            await SaveMockData(_transferRequestsFilePath, transferRequests);

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting transfer request {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves all transfer requests associated with a specific user.
    /// </summary>
    /// <param name="userId">The unique identifier of the user whose transfer requests are to be retrieved.</param>
    /// <returns>A collection of transfer requests associated with the specified user.</returns>
    [HttpGet("user/{userId:guid}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<TransferRequestData>> GetUserTransferRequests(Guid userId)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var userRequests = transferRequests.Where(r => r.UserId == userId).ToList();
            return Ok(userRequests);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transfer requests for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves transfer requests filtered by their status.
    /// </summary>
    /// <param name="status">The status to filter transfer requests by (e.g., "Pending", "Approved").</param>
    /// <returns>A collection of transfer requests matching the specified status.</returns>
    [HttpGet("status/{status}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<TransferRequestData>> GetTransferRequestsByStatus(string status)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var filteredRequests = transferRequests.Where(r =>
                string.Equals(r.Status, status, StringComparison.OrdinalIgnoreCase)).ToList();
            return Ok(filteredRequests.ToImmutableList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transfer requests by status {Status}", status);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Increments the view count for a specific transfer request.
    /// </summary>
    /// <param name="id">The unique identifier of the transfer request.</param>
    /// <returns>An ActionResult indicating the result of the operation.</returns>
    [HttpPost("{id:guid}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TransferRequestData), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> IncrementViewCount(Guid id)
    {
        try
        {
            var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
            var existingIndex = transferRequests.FindIndex(r => r.Id == id);

            if (existingIndex == -1)
            {
                return NotFound();
            }

            transferRequests[existingIndex].ViewCount++;
            await SaveMockData(_transferRequestsFilePath, transferRequests);

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error incrementing view count for transfer request {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves favorited TransferRequest for a specific user.
    /// </summary>
    /// <param name="userId">The user ID.</param>
    /// <returns>A list of favorited TransferRequest.</returns>
    [HttpGet("favorited")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<TransferRequestData>), 200)]
    public ActionResult<IEnumerable<TransferRequestData>> GetFavorited([FromQuery] Guid userId)
    {
        var savedtransferRequests = LoadData<List<Guid>>(_savedTransferRequestsFilePath);

        var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
        var favorited = transferRequests
            .Where(r => savedtransferRequests.Contains(r.Id))
            .Select(r =>
            {
                r.IsFavorite = true;
                return r;
            })
            .ToImmutableList();

        return Ok(favorited);
    }

    /// <summary>
    /// Adds or removes a transfer from the user's favorites.
    /// </summary>
    /// <param name="transferRequestId">The ID of the transfer.</param>
    /// <param name="userId">The user ID.</param>
    /// <returns>No content.</returns>
    [HttpPost("favorited")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Guid), 200)]
    [ProducesResponseType(404)]
    public IActionResult ToggleFavorite([FromQuery] Guid transferRequestId, [FromQuery] Guid userId) =>
        // We do not persist the favorite state in this example.
        NoContent();

    /// <summary>
    /// Saves or unsaves a transfer for a specific user.
    /// </summary>
    /// <param name="transferRequest">The transfer data.</param>
    /// <param name="userId">The user ID.</param>
    /// <returns>No content.</returns>
    [HttpPost]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Guid), 200)]
    [ProducesResponseType(404)]
    public IActionResult Save([FromBody] TransferRequestData transferRequest, [FromQuery] Guid userId) =>
        // We do not persist the favorite state in this example.
        NoContent();

    /// <summary>
    /// Retrieves steps for a specific transferReques.
    /// </summary>
    /// <param name="transferRequestId">The transfer ID.</param>
    /// <returns>A list of steps.</returns>
    [HttpGet("{transferRequestId}/steps")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<StepData>), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<StepData>> GetSteps(Guid transferRequestId)
    {
        var transferRequests = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
        var transferRequest = transferRequests.FirstOrDefault(r => r.Id == transferRequestId);

        if (transferRequest != null)
        {
            return Ok(transferRequest.Steps.ToImmutableList());
        }
        else
        {
            return NotFound("TransferReques not found");
        }
    }

    /// <summary>
    /// Retrieves trending transferRequests.
    /// </summary>
    /// <returns>A list of trending transferRequests.</returns>
    [HttpGet("trending")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<TransferRequestData>), 200)]
    public ActionResult<IEnumerable<TransferRequestData>> GetTrending()
    {
        var transferRequest = LoadData<List<TransferRequestData>>(_transferRequestsFilePath);
        var trending = transferRequest.Take(10).ToImmutableList();
        return Ok(trending);
    }
}
