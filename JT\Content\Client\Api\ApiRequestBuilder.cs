// <auto-generated/>
#pragma warning disable CS0618
using JT.Content.Client.Api.JobCategory;
using JT.Content.Client.Api.JobTransfer;
using JT.Content.Client.Api.Notification;
using JT.Content.Client.Api.Skill;
using JT.Content.Client.Api.Subscription;
using JT.Content.Client.Api.Token;
using JT.Content.Client.Api.TransferRequest;
using JT.Content.Client.Api.User;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace JT.Content.Client.Api
{
    /// <summary>
    /// Builds and executes requests for operations under \api
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class ApiRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The JobCategory property</summary>
        public global::JT.Content.Client.Api.JobCategory.JobCategoryRequestBuilder JobCategory
        {
            get => new global::JT.Content.Client.Api.JobCategory.JobCategoryRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The JobTransfer property</summary>
        public global::JT.Content.Client.Api.JobTransfer.JobTransferRequestBuilder JobTransfer
        {
            get => new global::JT.Content.Client.Api.JobTransfer.JobTransferRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The Notification property</summary>
        public global::JT.Content.Client.Api.Notification.NotificationRequestBuilder Notification
        {
            get => new global::JT.Content.Client.Api.Notification.NotificationRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The Skill property</summary>
        public global::JT.Content.Client.Api.Skill.SkillRequestBuilder Skill
        {
            get => new global::JT.Content.Client.Api.Skill.SkillRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The Subscription property</summary>
        public global::JT.Content.Client.Api.Subscription.SubscriptionRequestBuilder Subscription
        {
            get => new global::JT.Content.Client.Api.Subscription.SubscriptionRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The Token property</summary>
        public global::JT.Content.Client.Api.Token.TokenRequestBuilder Token
        {
            get => new global::JT.Content.Client.Api.Token.TokenRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The TransferRequest property</summary>
        public global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder TransferRequest
        {
            get => new global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The User property</summary>
        public global::JT.Content.Client.Api.User.UserRequestBuilder User
        {
            get => new global::JT.Content.Client.Api.User.UserRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.ApiRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public ApiRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.ApiRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public ApiRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
