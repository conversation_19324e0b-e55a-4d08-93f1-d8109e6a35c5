// <auto-generated/>
#pragma warning disable CS0618
using JT.Content.Client.Api.Token.Transactions.TypeNamespace.Item;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace JT.Content.Client.Api.Token.Transactions.TypeNamespace
{
    /// <summary>
    /// Builds and executes requests for operations under \api\Token\transactions\type
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class TypeRequestBuilder : BaseRequestBuilder
    {
        /// <summary>Gets an item from the JT.Content.Client.api.Token.transactions.type.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::JT.Content.Client.Api.Token.Transactions.TypeNamespace.Item.WithTypeItemRequestBuilder"/></returns>
        public global::JT.Content.Client.Api.Token.Transactions.TypeNamespace.Item.WithTypeItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("type", position);
                return new global::JT.Content.Client.Api.Token.Transactions.TypeNamespace.Item.WithTypeItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.Token.Transactions.TypeNamespace.TypeRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public TypeRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Token/transactions/type", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.Token.Transactions.TypeNamespace.TypeRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public TypeRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Token/transactions/type", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
