﻿<Page x:Class="JT.Presentation.NotificationsPage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:local="usingJT.Presentation"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:win="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:uer="using:Uno.Extensions.Reactive.UI"
	  xmlns:ut="using:Uno.Themes"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  xmlns:not_mobile="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:mobile="http://platform.uno/mobile"
	  xmlns:converters="using:JT.Converters"
	  utu:StatusBar.Background="{ThemeResource SurfaceInverseBrush}"
	  utu:StatusBar.Foreground="AutoInverse"
	  Background="{ThemeResource BackgroundBrush}"
	  mc:Ignorable="d mobile">

	<Page.Resources>
		<DataTemplate x:Key="NotificationTemplate">
			<utu:AutoLayout Background="{Binding Read, Converter={StaticResource BoolToNotificationColor}}"
							CornerRadius="4"
							PrimaryAxisAlignment="Center"
							Orientation="Horizontal"
							Padding="16"
							Spacing="16">
				<PathIcon Data="{StaticResource Icon_Notifications_Active}"
						  Foreground="{ThemeResource PrimaryBrush}" />
				<utu:AutoLayout utu:AutoLayout.PrimaryAlignment="Stretch"
								Spacing="8"
								PrimaryAxisAlignment="Center">
					<TextBlock TextWrapping="Wrap"
							   Text="{Binding Title}"
							   Foreground="{ThemeResource OnSurfaceBrush}"
							   Style="{StaticResource TitleSmall}" />

					<TextBlock TextWrapping="Wrap"
							   Text="{Binding Description}"
							   Foreground="{ThemeResource OnSurfaceMediumBrush}" />
				</utu:AutoLayout>
			</utu:AutoLayout>
		</DataTemplate>
		<DataTemplate x:Key="EmptyTemplate">
			<utu:AutoLayout Spacing="24"
							PrimaryAxisAlignment="Center"
							CounterAxisAlignment="Center"
							Padding="32,0">
				<BitmapIcon UriSource="{ThemeResource Empty_Notification}"
							Width="72"
							Height="70" />
				<TextBlock TextAlignment="Center"
						   TextWrapping="Wrap"
						   Text="No Notifications Yet"
						   Foreground="{ThemeResource OnSurfaceBrush}"
						   Style="{StaticResource TitleLarge}" />
				<TextBlock TextAlignment="Center"
						   TextWrapping="Wrap"
						   Text="Notifications about your activity, updates, and community interactions will appear here. Stay tuned for transfer inspiration, comments, likes, and more as you engage with the community."
						   Foreground="{ThemeResource OnSurfaceBrush}"
						   Style="{StaticResource TitleMedium}" />
				<Button HorizontalContentAlignment="Center"
						VerticalContentAlignment="Center"
						Content="Close"
						Padding="16,10,24,10"
						Foreground="{ThemeResource OnSecondaryContainerBrush}"
						CornerRadius="4"
						Style="{StaticResource FilledTonalButtonStyle}"
						uen:Navigation.Request="-">
					<ut:ControlExtensions.Icon>
						<PathIcon Data="{StaticResource Icon_Close}"
								  Foreground="{ThemeResource OnSecondaryContainerBrush}" />
					</ut:ControlExtensions.Icon>
				</Button>
			</utu:AutoLayout>
		</DataTemplate>
	</Page.Resources>
	<utu:AutoLayout PrimaryAxisAlignment="Stretch">
		<utu:NavigationBar Content="Notifications"
						   Style="{StaticResource JTsModalNavigationBarStyle}"
						   utu:AutoLayout.PrimaryAlignment="Auto" />
		<utu:AutoLayout Background="{ThemeResource BackgroundBrush}"
						uen:Region.Attached="True"
						utu:AutoLayout.PrimaryAlignment="Stretch"
						Padding="16">
			<utu:TabBar Background="{ThemeResource BackgroundBrush}"
						uen:Region.Attached="True"
						Style="{StaticResource TopTabBarStyle}">
				<utu:TabBarItem Content="All"
								uen:Region.Name="AllTab"
								IsSelected="True" />
				<utu:TabBarItem Content="Unread"
								uen:Region.Name="UnreadTab"
								Foreground="{ThemeResource OnSurfaceMediumBrush}" />
				<utu:TabBarItem Content="Read"
								uen:Region.Name="ReadTab"
								Foreground="{ThemeResource OnSurfaceMediumBrush}" />
			</utu:TabBar>
			<ScrollViewer HorizontalScrollMode="Disabled"
						  utu:AutoLayout.PrimaryAlignment="Stretch"
						  VerticalContentAlignment="Center">
				<Grid uen:Region.Attached="True"
					  uen:Region.Navigator="Visibility">

					<!-- All Notifications -->
					<Grid Padding="0,16"
						  uen:Region.Name="AllTab"
						  Visibility="Visible">
						<uer:FeedView x:Name="AllFeed"
									  Source="{Binding Notifications}"
									  NoneTemplate="{StaticResource EmptyTemplate}">
							<DataTemplate>
								<utu:AutoLayout>
									<!-- Today -->
									<utu:AutoLayout Visibility="{Binding Data.HasTodayNotifications}"
													Padding="0,16"
													Spacing="16">
										<TextBlock TextWrapping="Wrap"
												   Text="Today"
												   Foreground="{ThemeResource OnSurfaceBrush}"
												   Style="{StaticResource LabelLarge}" />
										<muxc:ItemsRepeater ItemsSource="{Binding Data.Today}"
															ItemTemplate="{StaticResource NotificationTemplate}">
											<muxc:ItemsRepeater.Layout>
												<muxc:StackLayout Orientation="Vertical"
																  Spacing="2" />
											</muxc:ItemsRepeater.Layout>
										</muxc:ItemsRepeater>
									</utu:AutoLayout>

									<!-- Yesterday -->
									<utu:AutoLayout Visibility="{Binding Data.HasYesterdayNotifications}"
													Padding="0,16"
													Spacing="16">
										<TextBlock TextWrapping="Wrap"
												   Text="Yesterday"
												   Foreground="{ThemeResource OnSurfaceBrush}"
												   Style="{StaticResource LabelLarge}" />
										<muxc:ItemsRepeater ItemsSource="{Binding Data.Yesterday}"
															ItemTemplate="{StaticResource NotificationTemplate}">
											<muxc:ItemsRepeater.Layout>
												<muxc:StackLayout Orientation="Vertical"
																  Spacing="2" />
											</muxc:ItemsRepeater.Layout>
										</muxc:ItemsRepeater>
									</utu:AutoLayout>

									<!-- Older -->
									<utu:AutoLayout Visibility="{Binding Data.HasOlderNotifications}"
													Padding="0,16"
													Spacing="16">
										<TextBlock TextWrapping="Wrap"
												   Text="Older"
												   Foreground="{ThemeResource OnSurfaceBrush}"
												   Style="{StaticResource LabelLarge}" />
										<muxc:ItemsRepeater ItemsSource="{Binding Data.Older}"
															ItemTemplate="{StaticResource NotificationTemplate}">
											<muxc:ItemsRepeater.Layout>
												<muxc:StackLayout Orientation="Vertical"
																  Spacing="2" />
											</muxc:ItemsRepeater.Layout>
										</muxc:ItemsRepeater>
									</utu:AutoLayout>
								</utu:AutoLayout>
							</DataTemplate>
						</uer:FeedView>
					</Grid>

					<!-- Unread -->
					<Grid Padding="0,16"
						  uen:Region.Name="UnreadTab"
						  Visibility="Collapsed">
						<uer:FeedView x:Name="UnreadFeed"
									  Source="{Binding Unread}"
									  NoneTemplate="{StaticResource EmptyTemplate}">
							<DataTemplate>
								<utu:AutoLayout>
									<!-- Today -->
									<utu:AutoLayout Visibility="{Binding Data.HasTodayNotifications}"
													Padding="0,16"
													Spacing="16">
										<TextBlock TextWrapping="Wrap"
												   Text="Today"
												   Foreground="{ThemeResource OnSurfaceBrush}"
												   Style="{StaticResource LabelLarge}" />
										<muxc:ItemsRepeater ItemsSource="{Binding Data.Today}"
															ItemTemplate="{StaticResource NotificationTemplate}">
											<muxc:ItemsRepeater.Layout>
												<muxc:StackLayout Orientation="Vertical"
																  Spacing="2" />
											</muxc:ItemsRepeater.Layout>
										</muxc:ItemsRepeater>
									</utu:AutoLayout>

									<!-- Yesterday -->
									<utu:AutoLayout Visibility="{Binding Data.HasYesterdayNotifications}"
													Padding="0,16"
													Spacing="16">
										<TextBlock TextWrapping="Wrap"
												   Text="Yesterday"
												   Foreground="{ThemeResource OnSurfaceBrush}"
												   Style="{StaticResource LabelLarge}" />
										<muxc:ItemsRepeater ItemsSource="{Binding Data.Yesterday}"
															ItemTemplate="{StaticResource NotificationTemplate}">
											<muxc:ItemsRepeater.Layout>
												<muxc:StackLayout Orientation="Vertical"
																  Spacing="2" />
											</muxc:ItemsRepeater.Layout>
										</muxc:ItemsRepeater>
									</utu:AutoLayout>

									<!-- Older -->
									<utu:AutoLayout Visibility="{Binding Data.HasOlderNotifications}"
													Padding="0,16"
													Spacing="16">
										<TextBlock TextWrapping="Wrap"
												   Text="Older"
												   Foreground="{ThemeResource OnSurfaceBrush}"
												   Style="{StaticResource LabelLarge}" />
										<muxc:ItemsRepeater ItemsSource="{Binding Data.Older}"
															ItemTemplate="{StaticResource NotificationTemplate}">
											<muxc:ItemsRepeater.Layout>
												<muxc:StackLayout Orientation="Vertical"
																  Spacing="2" />
											</muxc:ItemsRepeater.Layout>
										</muxc:ItemsRepeater>
									</utu:AutoLayout>
								</utu:AutoLayout>
							</DataTemplate>
						</uer:FeedView>
					</Grid>

					<!-- Read -->
					<Grid Padding="0,16"
						  uen:Region.Name="ReadTab"
						  Visibility="Collapsed">
						<uer:FeedView x:Name="ReadFeed"
									  Source="{Binding Read}"
									  NoneTemplate="{StaticResource EmptyTemplate}">
							<DataTemplate>
								<utu:AutoLayout>
									<!-- Today -->
									<utu:AutoLayout Visibility="{Binding Data.HasTodayNotifications}"
													Padding="0,16"
													Spacing="16">
										<TextBlock TextWrapping="Wrap"
												   Text="Today"
												   Foreground="{ThemeResource OnSurfaceBrush}"
												   Style="{StaticResource LabelLarge}" />
										<muxc:ItemsRepeater ItemsSource="{Binding Data.Today}"
															ItemTemplate="{StaticResource NotificationTemplate}">
											<muxc:ItemsRepeater.Layout>
												<muxc:StackLayout Orientation="Vertical"
																  Spacing="2" />
											</muxc:ItemsRepeater.Layout>
										</muxc:ItemsRepeater>
									</utu:AutoLayout>

									<!-- Yesterday -->
									<utu:AutoLayout Visibility="{Binding Data.HasYesterdayNotifications}"
													Padding="0,16"
													Spacing="16">
										<TextBlock TextWrapping="Wrap"
												   Text="Yesterday"
												   Foreground="{ThemeResource OnSurfaceBrush}"
												   Style="{StaticResource LabelLarge}" />
										<muxc:ItemsRepeater ItemsSource="{Binding Data.Yesterday}"
															ItemTemplate="{StaticResource NotificationTemplate}">
											<muxc:ItemsRepeater.Layout>
												<muxc:StackLayout Orientation="Vertical"
																  Spacing="2" />
											</muxc:ItemsRepeater.Layout>
										</muxc:ItemsRepeater>
									</utu:AutoLayout>

									<!-- Older -->
									<utu:AutoLayout Visibility="{Binding Data.HasOlderNotifications}"
													Padding="0,16"
													Spacing="16">
										<TextBlock TextWrapping="Wrap"
												   Text="Older"
												   Foreground="{ThemeResource OnSurfaceBrush}"
												   Style="{StaticResource LabelLarge}" />
										<muxc:ItemsRepeater ItemsSource="{Binding Data.Older}"
															ItemTemplate="{StaticResource NotificationTemplate}">
											<muxc:ItemsRepeater.Layout>
												<muxc:StackLayout Orientation="Vertical"
																  Spacing="2" />
											</muxc:ItemsRepeater.Layout>
										</muxc:ItemsRepeater>
									</utu:AutoLayout>
								</utu:AutoLayout>
							</DataTemplate>
						</uer:FeedView>
					</Grid>
				</Grid>
			</ScrollViewer>
		</utu:AutoLayout>
	</utu:AutoLayout>
</Page>
