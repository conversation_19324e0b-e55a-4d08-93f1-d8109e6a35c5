namespace JT.Business.Services.JobTransfers;

public interface IJobTransferService
{
    /// <summary>
    /// Add jobTransfer created by the user
    /// </summary>
    /// <param name="name">Name of the jobTransfer to add</param>
    /// <param name="ct"></param>
    /// <returns></returns>
    ValueTask<JobTransfer> Create(string name, IImmutableList<TransferRequest> transfers, CancellationToken ct);

    /// <summary>
    /// Add jobTransfer created by the user
    /// </summary>
    /// <param name="jobTransfer">JobTransfer to add</param>
    /// <param name="ct"></param>
    /// <returns></returns>
    ValueTask<JobTransfer> Update(JobTransfer jobTransfer, IImmutableList<TransferRequest> transfers, CancellationToken ct);

    /// <summary>
    /// Add jobTransfer created by the user
    /// </summary>
    /// <param name="jobTransfer">JobTransfer to add</param>
    /// <param name="ct"></param>
    /// <returns></returns>
    ValueTask Update(JobTransfer jobTransfer, CancellationToken ct);

    /// <summary>
    /// Add jobTransfer that the user wants to save
    /// </summary>
    /// <param name="jobTransfer">JobTransfer to add</param>
    /// <param name="ct"></param>
    /// <returns></returns>
    ValueTask Save(JobTransfer jobTransfer, CancellationToken ct);

    /// <summary>
    /// JobTransfers saved from api
    /// </summary>
    /// <param name="ct"></param>
    /// <returns>
    /// Get each jobTransfer from api that was saved
    /// </returns>
    ValueTask<IImmutableList<JobTransfer>> GetSaved(CancellationToken ct);

    /// <summary>
    /// JobTransfers by user
    /// </summary>
    /// <param name="ct"></param>
    /// <returns>
    /// User's jobTransfers
    /// </returns>
    ValueTask<IImmutableList<JobTransfer>> GetByUser(Guid userId, CancellationToken ct);
}
