using JT.Content.Client.Models;
using StepData = JT.Content.Client.Models.StepData;

namespace JT.Business.Models;

public record Step
{
	public Step(StepData stepData)
	{
		Number = stepData.Number ?? 0;
		Name = stepData.Name;
        TransferTime = ToTimeSpan(stepData.TransferTime);
		Transferware = stepData.Transferware?.ToImmutableList() ?? ImmutableList<string>.Empty;
		Ingredients = stepData.Ingredients?.ToImmutableList() ?? ImmutableList<string>.Empty;
		Description = stepData.Description;
		UrlVideo = stepData.UrlVideo;
	}

	public int Number { get; init; }
	public string? Name { get; init; }
	public TimeSpan TransferTime { get; init; }
	public IImmutableList<string>? Transferware { get; init; }
	public IImmutableList<string>? Ingredients { get; init; }
	public string? Description { get; init; }
	public string? UrlVideo { get; init; }

	internal StepData ToData() => new()
	{
		Number = Number,
		Name = Name,
        TransferTime = new TimeSpanObject(),
        Transferware = Transferware?.ToList(),
		Ingredients = Ingredients?.ToList(),
		Description = Description,
		UrlVideo = UrlVideo
	};

	private static TimeSpan ToTimeSpan(TimeSpanObject? timeSpanObject)
	{
		return new TimeSpan(timeSpanObject?.Ticks ?? 0);
	}
}
