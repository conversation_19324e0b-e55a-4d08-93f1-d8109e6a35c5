using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PromotionPackageController : ChefsControllerBase
{
	private readonly ILogger<PromotionPackageController> _logger;

	public PromotionPackageController(ILogger<PromotionPackageController> logger)
	{
		_logger = logger;
	}

	[HttpGet]
	public async Task<ActionResult<IEnumerable<PromotionPackageData>>> GetPromotionPackages()
	{
		try
		{
			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			return Ok(packages);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting promotion packages");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("active")]
	public async Task<ActionResult<IEnumerable<PromotionPackageData>>> GetActivePackages()
	{
		try
		{
			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			var activePackages = packages.Where(p => p.IsActive == true).ToList();
			return Ok(activePackages);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting active promotion packages");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("popular")]
	public async Task<ActionResult<IEnumerable<PromotionPackageData>>> GetPopularPackages()
	{
		try
		{
			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			var popularPackages = packages.Where(p => p.IsPopular == true && p.IsActive == true).ToList();
			return Ok(popularPackages);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting popular promotion packages");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("{id}")]
	public async Task<ActionResult<PromotionPackageData>> GetPromotionPackage(string id)
	{
		try
		{
			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			var package = packages.FirstOrDefault(p => p.Id == id);
			
			if (package == null)
			{
				return NotFound();
			}

			return Ok(package);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting promotion package {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("type/{type}")]
	public async Task<ActionResult<IEnumerable<PromotionPackageData>>> GetPackagesByType(string type)
	{
		try
		{
			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			var typePackages = packages.Where(p => 
				string.Equals(p.Type, type, StringComparison.OrdinalIgnoreCase) && 
				p.IsActive == true).ToList();
			return Ok(typePackages);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting promotion packages by type {Type}", type);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("price-range")]
	public async Task<ActionResult<IEnumerable<PromotionPackageData>>> GetPackagesByPriceRange(
		[FromQuery] decimal minPrice, 
		[FromQuery] decimal maxPrice)
	{
		try
		{
			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			var priceRangePackages = packages.Where(p => 
				p.PriceOMR >= minPrice && 
				p.PriceOMR <= maxPrice && 
				p.IsActive == true).ToList();
			return Ok(priceRangePackages);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting promotion packages by price range {MinPrice}-{MaxPrice}", minPrice, maxPrice);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost]
	public async Task<ActionResult<PromotionPackageData>> CreatePromotionPackage(PromotionPackageData package)
	{
		try
		{
			package.Id = Guid.NewGuid().ToString();
			package.CreatedAt = DateTime.UtcNow;
			package.UpdatedAt = DateTime.UtcNow;
			package.IsActive = true;

			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			packages.Add(package);
			await SaveMockData("PromotionPackages.json", packages);

			return CreatedAtAction(nameof(GetPromotionPackage), new { id = package.Id }, package);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error creating promotion package");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPut("{id}")]
	public async Task<ActionResult<PromotionPackageData>> UpdatePromotionPackage(string id, PromotionPackageData package)
	{
		try
		{
			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			var existingIndex = packages.FindIndex(p => p.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			package.Id = id;
			package.UpdatedAt = DateTime.UtcNow;
			packages[existingIndex] = package;
			await SaveMockData("PromotionPackages.json", packages);

			return Ok(package);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error updating promotion package {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpDelete("{id}")]
	public async Task<ActionResult> DeletePromotionPackage(string id)
	{
		try
		{
			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			var existingIndex = packages.FindIndex(p => p.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			packages.RemoveAt(existingIndex);
			await SaveMockData("PromotionPackages.json", packages);

			return NoContent();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error deleting promotion package {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("{id}/activate")]
	public async Task<ActionResult> ActivatePackage(string id)
	{
		try
		{
			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			var existingIndex = packages.FindIndex(p => p.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			packages[existingIndex].IsActive = true;
			packages[existingIndex].UpdatedAt = DateTime.UtcNow;
			await SaveMockData("PromotionPackages.json", packages);

			return Ok();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error activating promotion package {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("{id}/deactivate")]
	public async Task<ActionResult> DeactivatePackage(string id)
	{
		try
		{
			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			var existingIndex = packages.FindIndex(p => p.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			packages[existingIndex].IsActive = false;
			packages[existingIndex].UpdatedAt = DateTime.UtcNow;
			await SaveMockData("PromotionPackages.json", packages);

			return Ok();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error deactivating promotion package {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("{id}/calculate-price")]
	public async Task<ActionResult<PriceCalculationResponse>> CalculatePrice(
		string id, 
		[FromBody] PriceCalculationRequest request)
	{
		try
		{
			var packages = await GetMockData<List<PromotionPackageData>>("PromotionPackages.json");
			var package = packages.FirstOrDefault(p => p.Id == id);
			
			if (package == null)
			{
				return NotFound();
			}

			var basePrice = package.PriceOMR ?? 0;
			var quantity = Math.Max(1, request.Quantity);
			var subtotal = basePrice * quantity;
			
			// Apply discount if provided
			var discountAmount = 0m;
			if (!string.IsNullOrEmpty(request.DiscountCode))
			{
				// Simple discount logic - in real implementation, this would check a discount table
				discountAmount = request.DiscountCode.ToLower() switch
				{
					"welcome10" => subtotal * 0.10m,
					"bulk20" when quantity >= 5 => subtotal * 0.20m,
					"newclient15" => subtotal * 0.15m,
					_ => 0m
				};
			}

			var total = subtotal - discountAmount;

			var response = new PriceCalculationResponse
			{
				PackageId = id,
				BasePrice = basePrice,
				Quantity = quantity,
				Subtotal = subtotal,
				DiscountCode = request.DiscountCode,
				DiscountAmount = discountAmount,
				Total = total,
				Currency = "OMR"
			};

			return Ok(response);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error calculating price for package {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}
}

public class PriceCalculationRequest
{
	public int Quantity { get; set; } = 1;
	public string? DiscountCode { get; set; }
}

public class PriceCalculationResponse
{
	public string PackageId { get; set; } = string.Empty;
	public decimal BasePrice { get; set; }
	public int Quantity { get; set; }
	public decimal Subtotal { get; set; }
	public string? DiscountCode { get; set; }
	public decimal DiscountAmount { get; set; }
	public decimal Total { get; set; }
	public string Currency { get; set; } = "OMR";
}
