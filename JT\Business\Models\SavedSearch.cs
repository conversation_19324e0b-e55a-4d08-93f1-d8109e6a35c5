using SavedSearchData = JT.Client.Models.SavedSearchData;

namespace JT.Business.Models;

public partial record SavedSearch
{
	internal SavedSearch(SavedSearchData savedSearchData)
	{
		Id = savedSearchData.Id;
		UserId = savedSearchData.UserId ?? Guid.Empty;
		Name = savedSearchData.Name;
		SearchCriteria = new SearchCriteria(savedSearchData.SearchCriteria);
		AlertsEnabled = savedSearchData.AlertsEnabled ?? false;
		CreatedAt = savedSearchData.CreatedAt ?? DateTime.MinValue;
		LastUsed = savedSearchData.LastUsed ?? DateTime.MinValue;
		ResultCount = savedSearchData.ResultCount ?? 0;
	}

	public string? Id { get; init; }
	public Guid UserId { get; init; }
	public string? Name { get; init; }
	public SearchCriteria SearchCriteria { get; init; }
	public bool AlertsEnabled { get; init; }
	public DateTime CreatedAt { get; init; }
	public DateTime LastUsed { get; init; }
	public int ResultCount { get; init; }

	// Computed properties
	public bool IsRecentlyUsed => (DateTime.UtcNow - LastUsed).TotalDays <= 7;
	public string LastUsedDisplay => LastUsed == DateTime.MinValue ? "Never" : 
		(DateTime.UtcNow - LastUsed).TotalDays switch
		{
			< 1 => "Today",
			< 2 => "Yesterday", 
			< 7 => $"{(int)(DateTime.UtcNow - LastUsed).TotalDays} days ago",
			< 30 => $"{(int)(DateTime.UtcNow - LastUsed).TotalDays / 7} weeks ago",
			_ => LastUsed.ToString("MMM dd, yyyy")
		};

	internal SavedSearchData ToData() => new()
	{
		Id = Id,
		UserId = UserId,
		Name = Name,
		SearchCriteria = SearchCriteria.ToData(),
		AlertsEnabled = AlertsEnabled,
		CreatedAt = CreatedAt,
		LastUsed = LastUsed,
		ResultCount = ResultCount
	};
}

public partial record SearchCriteria
{
	internal SearchCriteria(SearchCriteriaData? searchCriteriaData)
	{
		Keywords = searchCriteriaData?.Keywords;
		Location = searchCriteriaData?.Location;
		Industry = searchCriteriaData?.Industry;
		SalaryRange = new SalaryRange(searchCriteriaData?.SalaryRange);
		ExperienceLevel = searchCriteriaData?.ExperienceLevel;
		RemoteWork = searchCriteriaData?.RemoteWork ?? false;
	}

	public string? Keywords { get; init; }
	public string? Location { get; init; }
	public string? Industry { get; init; }
	public SalaryRange SalaryRange { get; init; }
	public string? ExperienceLevel { get; init; }
	public bool RemoteWork { get; init; }

	internal SearchCriteriaData ToData() => new()
	{
		Keywords = Keywords,
		Location = Location,
		Industry = Industry,
		SalaryRange = SalaryRange.ToData(),
		ExperienceLevel = ExperienceLevel,
		RemoteWork = RemoteWork
	};
}

public partial record SalaryRange
{
	internal SalaryRange(SalaryRangeData? salaryRangeData)
	{
		Min = salaryRangeData?.Min ?? 0;
		Max = salaryRangeData?.Max ?? 0;
		Currency = salaryRangeData?.Currency;
	}

	public decimal Min { get; init; }
	public decimal Max { get; init; }
	public string? Currency { get; init; }

	public string Display => Min > 0 && Max > 0 ? $"{Min:F0} - {Max:F0} {Currency}" : 
		Min > 0 ? $"{Min:F0}+ {Currency}" : 
		Max > 0 ? $"Up to {Max:F0} {Currency}" : "Any";

	internal SalaryRangeData ToData() => new()
	{
		Min = Min,
		Max = Max,
		Currency = Currency
	};
}
