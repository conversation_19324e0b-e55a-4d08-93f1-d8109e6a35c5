namespace JT.Business.Services.Skills;

public interface ISkillService
{
	ValueTask<IImmutableList<Skill>> GetAll(CancellationToken ct = default);
	ValueTask<Skill?> GetById(int id, CancellationToken ct = default);
	ValueTask<IImmutableList<Skill>> GetByCategory(string category, CancellationToken ct = default);
	ValueTask<IImmutableList<Skill>> GetByIndustry(string industry, CancellationToken ct = default);
	ValueTask<IImmutableList<Skill>> GetTrendingSkills(CancellationToken ct = default);
	ValueTask<IImmutableList<Skill>> GetPopularSkills(int count = 10, CancellationToken ct = default);
	ValueTask<IImmutableList<Skill>> SearchSkills(string query, CancellationToken ct = default);
	ValueTask<IImmutableList<string>> GetCategories(CancellationToken ct = default);
	ValueTask<IImmutableList<string>> GetIndustries(CancellationToken ct = default);
}
