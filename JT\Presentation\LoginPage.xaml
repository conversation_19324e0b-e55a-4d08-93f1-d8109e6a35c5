﻿<Page x:Class="JT.Presentation.LoginPage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:android="http://uno.ui/android"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:ios="http://uno.ui/ios"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:not_win="http://uno.ui/not_win"
	  xmlns:ut="using:Uno.Themes"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  utu:StatusBar.Background="{ThemeResource SurfaceBrush}"
	  utu:StatusBar.Foreground="Auto"
	  Background="{ThemeResource SurfaceBrush}"
	  mc:Ignorable="d android ios not_win">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <utu:AutoLayout Spacing="32"
						MaxWidth="500"
						PrimaryAxisAlignment="Center"
						Padding="32">
            <Image utu:AutoLayout.CounterAlignment="Center"
				   Width="160"
				   Height="90"
				   Source="{ThemeResource JTsLogoWithIcon}"
				   Stretch="Uniform" />
            <utu:AutoLayout Spacing="16"
							PrimaryAxisAlignment="Center">
                <TextBox PlaceholderText="Username"
						 x:Name="LoginUsername"
						 AutomationProperties.AutomationId="LoginUsername"
						 Style="{StaticResource JTsPrimaryTextBoxStyle}"
						 utu:InputExtensions.ReturnType="Next"
						 utu:InputExtensions.AutoFocusNextElement="{Binding ElementName=LoginPassword}"
						 IsSpellCheckEnabled="False"
						 Text="{Binding UserCredentials.Username, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                    <ut:ControlExtensions.Icon>
                        <PathIcon Data="{StaticResource Icon_Person_Outline}" />
                    </ut:ControlExtensions.Icon>
                </TextBox>
                <PasswordBox x:Name="LoginPassword"
							 AutomationProperties.AutomationId="LoginPassword"
							 utu:InputExtensions.ReturnType="Done"
							 utu:CommandExtensions.Command="{Binding Login}"
							 Password="{Binding UserCredentials.Password, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
							 PlaceholderText="Password"
							 Style="{StaticResource OutlinedPasswordBoxStyle}"
							 BorderBrush="{ThemeResource OutlineVariantBrush}">
                    <PasswordBox.Resources>
                        <ResourceDictionary>
                            <ResourceDictionary.ThemeDictionaries>
                                <ResourceDictionary x:Key="Light">
                                    <StaticResource x:Key="OutlinedPasswordBoxPlaceholderForeground" ResourceKey="OnSurfaceMediumBrush" />
                                    <x:String x:Key="WorkAroundDummy">WorkAroundDummy</x:String>
                                </ResourceDictionary>
                                <ResourceDictionary x:Key="Default">
                                    <StaticResource x:Key="OutlinedPasswordBoxPlaceholderForeground" ResourceKey="OnSurfaceMediumBrush" />
                                    <x:String x:Key="WorkAroundDummy">WorkAroundDummy</x:String>
                                </ResourceDictionary>
                            </ResourceDictionary.ThemeDictionaries>
                        </ResourceDictionary>
                    </PasswordBox.Resources>
                    <ut:ControlExtensions.Icon>
                        <PathIcon Data="{StaticResource Icon_Lock}" />
                    </ut:ControlExtensions.Icon>
                </PasswordBox>
                <utu:AutoLayout Spacing="24"
								Orientation="Horizontal"
								CounterAxisAlignment="Center"
								Justify="SpaceBetween"
								PrimaryAxisAlignment="Stretch">
                    <CheckBox Content="Remember me"
							  utu:AutoLayout.PrimaryAlignment="Auto"
							  IsChecked="{Binding UserCredentials.SaveCredentials, Mode=TwoWay}" />
                    <Button Content="Forgot password?"
							Style="{StaticResource TextButtonStyle}" />
                </utu:AutoLayout>
                <Button Content="Login"
						x:Name="LoginButton"
						AutomationProperties.AutomationId="LoginButton"
						Style="{StaticResource JTsPrimaryButtonStyle}"
						Command="{Binding Login}" />
            </utu:AutoLayout>

            <utu:Divider Style="{StaticResource DividerStyle}" />

            <utu:AutoLayout Spacing="8"
							PrimaryAxisAlignment="Center">
                <Button Content="Sign in with Apple"
						Command="{Binding LoginWithApple}"
						Style="{StaticResource JTsTonalButtonStyle}">
                    <ut:ControlExtensions.Icon>
                        <FontIcon Style="{StaticResource FontAwesomeBrandsFontIconStyle}"
								  Glyph="{StaticResource Icon_Apple_Brand}"
								  FontSize="18"
								  Foreground="{ThemeResource OnSurfaceBrush}" />
                    </ut:ControlExtensions.Icon>
                </Button>
                <Button Content="Sign in with Google"
						Command="{Binding LoginWithGoogle}"
						Style="{StaticResource JTsTonalButtonStyle}">
                    <ut:ControlExtensions.Icon>
                        <FontIcon Style="{StaticResource FontAwesomeBrandsFontIconStyle}"
								  Glyph="{StaticResource Icon_Google_Brand}"
								  FontSize="18"
								  Foreground="{ThemeResource OnSurfaceBrush}" />
                    </ut:ControlExtensions.Icon>
                </Button>
            </utu:AutoLayout>
            <utu:AutoLayout PrimaryAxisAlignment="Center"
							CounterAxisAlignment="Center"
							Orientation="Horizontal"
							Spacing="4">
                <TextBlock Text="Not a member?"
						   Foreground="{ThemeResource OnSurfaceBrush}"
						   Style="{StaticResource LabelLarge}" />
                <Button Content="Register Now"
						uen:Navigation.Request="-/Register"
						Style="{StaticResource TextButtonStyle}" />
            </utu:AutoLayout>
        </utu:AutoLayout>
    </ScrollViewer>
</Page>
