namespace JT.Server.Entities;

public class PromotionPackageData
{
	public string? Id { get; set; }
	public string? Name { get; set; }
	public string? DisplayName { get; set; }
	public string? Description { get; set; }
	public string? Type { get; set; } // banner, sponsored, directory, job_promotion, premium
	public decimal? PriceOMR { get; set; }
	public int? DurationDays { get; set; }
	public int? MaxPromotions { get; set; } // -1 for unlimited
	public int? MaxViews { get; set; } // -1 for unlimited
	public int? MaxClicks { get; set; } // -1 for unlimited
	public List<string>? Features { get; set; }
	public string? TargetAudience { get; set; }
	public string? Priority { get; set; } = "normal"; // low, normal, high, premium
	public bool? AnalyticsIncluded { get; set; }
	public string? SupportLevel { get; set; } = "basic"; // basic, standard, premium
	public bool? IsPopular { get; set; }
	public bool? IsActive { get; set; } = true;
	public DateTime? CreatedAt { get; set; }
	public DateTime? UpdatedAt { get; set; }
}
