using JT.Business.Services.TransferRequests;
using JT.Business.Services.Users;

namespace JT.Presentation;

public partial record ProfileModel
{
    private readonly ITransferRequestService _transferRequestService;
	private readonly INavigator _navigator;

	public ProfileModel(
		INavigator navigator,
        ITransferRequestService transferRequestService,
        IUserService userService,
		User? user)
	{
		_navigator = navigator;
        _transferRequestService = transferRequestService;

        Profile = user != null ? State.Value(this, () => user) : userService.User;
	}

	public IFeed<User> Profile { get; }

	public IListFeed<TransferRequest> TransferRequests => Profile
		.SelectAsync((user, ct) => _transferRequestService.GetByUser(user.Id, ct))
		.AsListFeed();
}
