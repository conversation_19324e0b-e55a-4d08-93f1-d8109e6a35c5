using SkillData = JT.Content.Client.Models.SkillData;

namespace JT.Business.Models;

public partial record Skill
{
	internal Skill(SkillData skillData)
	{
		Id = skillData.Id ?? 0;
		Name = skillData.Name;
		Category = skillData.Category;
		PopularityScore = skillData.PopularityScore ?? 0;
		Industry = skillData.Industry;
	}

	public int Id { get; init; }
	public string? Name { get; init; }
	public string? Category { get; init; }
	public int PopularityScore { get; init; }
	public string? Industry { get; init; }

	// Computed properties
	public string DisplayName => Name ?? "Unknown Skill";
	public string PopularityLevel => PopularityScore switch
	{
		>= 90 => "Very High",
		>= 80 => "High",
		>= 70 => "Medium",
		>= 60 => "Low",
		_ => "Very Low"
	};

	public string CategoryIcon => Category?.ToLower() switch
	{
		"technical" => "🔧",
		"management" => "👔",
		"healthcare" => "🏥",
		"language" => "🗣️",
		"marketing" => "📈",
		"soft skills" => "🤝",
		_ => "💼"
	};

	public bool IsInDemand => PopularityScore >= 80;
	public bool IsSpecialized => !string.IsNullOrEmpty(Industry) && Industry != "General";

	internal SkillData ToData() => new()
	{
		Id = Id,
		Name = Name,
		Category = Category,
		PopularityScore = PopularityScore,
		Industry = Industry
	};
}
