﻿<Page x:Class="JT.Presentation.FavoriteTransferRequestPage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:local="using:JT.Presentation"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:models="using:JT.Business.Models"
	  xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:uer="using:Uno.Extensions.Reactive.UI"
	  xmlns:ut="using:Uno.Themes"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  xmlns:win="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  utu:StatusBar.Background="{ThemeResource SurfaceInverseBrush}"
	  utu:StatusBar.Foreground="AutoInverse"
	  NavigationCacheMode="Enabled"
	  mc:Ignorable="d"
	  Background="{ThemeResource BackgroundBrush}">
    <Page.Resources>
        <muxc:UniformGridLayout x:Key="ResponsiveGridLayout"
								ItemsStretch="Fill"
								MaximumRowsOrColumns="{utu:Responsive Normal=2,
																	  Wide=7}"
								MinColumnSpacing="{utu:Responsive Normal=8,
																  Wide=15}"
								MinItemWidth="{utu:Responsive Normal=155,
															  Wide=240}"
								MinRowSpacing="{utu:Responsive Normal=8,
															   Wide=15}" />

        <DataTemplate x:Key="JobTransferEmptyTemplate">
            <utu:AutoLayout Padding="32,0"
							PrimaryAxisAlignment="Center"
							CounterAxisAlignment="Center"
							Spacing="24">
                <BitmapIcon Width="56"
							Height="72"
							UriSource="{ThemeResource Empty_Box}" />
                <TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
						   Style="{StaticResource TitleLarge}"
						   Text="No JobTransfers Created"
						   TextAlignment="Center"
						   TextWrapping="Wrap" />
                <TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
						   Style="{StaticResource TitleMedium}"
						   Text="It looks like you haven't created any JobTransfers yet. JobTransfers are a great way to organize your transfers into collections. "
						   TextAlignment="Center"
						   TextWrapping="Wrap" />
                <Button HorizontalContentAlignment="Center"
						VerticalContentAlignment="Center"
						uen:Navigation.Request="!CreateJobTransfer"
						Content="Create JobTransfer"
						Foreground="{ThemeResource PrimaryBrush}"
						Padding="12,10,16,10"
						CornerRadius="20"
						Style="{StaticResource TextButtonStyle}">
                    <ut:ControlExtensions.Icon>
                        <PathIcon Data="{StaticResource Icon_Add}"
								  Foreground="{ThemeResource PrimaryBrush}" />
                    </ut:ControlExtensions.Icon>
                </Button>
            </utu:AutoLayout>
        </DataTemplate>

        <DataTemplate x:Key="JobTransferTemplate">
            <utu:CardContentControl x:Name="JobTransferTemplateCard"
									HorizontalAlignment="Stretch"
									VerticalAlignment="Stretch"
									Background="{ThemeResource SurfaceBrush}"
									CornerRadius="12"
									Style="{StaticResource FilledCardContentControlStyle}">
                <utu:CardContentControl.ContentTemplate>
                    <DataTemplate>
                        <utu:AutoLayout Margin="4">
                            <utu:AutoLayout Height="144"
											utu:AutoLayout.CounterAlignment="Stretch"
											Orientation="Horizontal"
											Spacing="4">
                                <Border utu:AutoLayout.PrimaryAlignment="Stretch"
										CornerRadius="12">
                                    <Image HorizontalAlignment="Center"
										   VerticalAlignment="Center"
										   Source="{Binding JobTransferImages.FirstImage}"
										   Stretch="UniformToFill" />
                                </Border>

                                <Grid RowSpacing="4"
									  utu:AutoLayout.PrimaryAlignment="Stretch">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="*" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>
                                    <Border utu:AutoLayout.PrimaryAlignment="Stretch"
											Background="{ThemeResource BackgroundBrush}"
											CornerRadius="12">
                                        <Image HorizontalAlignment="Center"
											   VerticalAlignment="Center"
											   Source="{Binding JobTransferImages.SecondImage}"
											   Stretch="UniformToFill"
											   Visibility="{Binding JobTransferImages.SecondImage, Converter={StaticResource NullToCollapsed}}" />
                                    </Border>

                                    <Grid Grid.Row="1"
										  utu:AutoLayout.PrimaryAlignment="Stretch"
										  CornerRadius="12">
                                        <Image HorizontalAlignment="Center"
											   VerticalAlignment="Center"
											   Source="{Binding JobTransferImages.ThirdImage}"
											   Stretch="UniformToFill"
											   Visibility="{Binding JobTransferImages.ThirdImage, Converter={StaticResource NullToCollapsed}}" />

                                        <Button MinHeight="57"
												HorizontalAlignment="Stretch"
												VerticalAlignment="Stretch"
												uen:Navigation.Data="{Binding}"
												uen:Navigation.Request="CreateJobTransfer"
												Background="{ThemeResource BackgroundBrush}"
												CornerRadius="20"
												Style="{StaticResource TextButtonStyle}"
												Visibility="{Binding JobTransferImages.ThirdImage, Converter={StaticResource NullToVisible}}">
                                            <Button.Content>
                                                <PathIcon Data="{StaticResource Icon_Add}"
														  Foreground="{ThemeResource OnSurfaceBrush}" />
                                            </Button.Content>
                                        </Button>
                                    </Grid>
                                </Grid>
                            </utu:AutoLayout>
                            <utu:AutoLayout Padding="12"
											PrimaryAxisAlignment="Center"
											Spacing="2">
                                <TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
										   Style="{StaticResource TitleSmall}"
										   Text="{Binding Name}"
										   TextWrapping="NoWrap" />
                                <TextBlock Foreground="{ThemeResource OnSurfaceMediumBrush}"
										   Style="{StaticResource CaptionMedium}"
										   Text="{Binding PinsNumber, Converter={StaticResource StringFormatter}, ConverterParameter='{}{0} transfers'}"
										   TextWrapping="NoWrap" />
                            </utu:AutoLayout>
                        </utu:AutoLayout>
                    </DataTemplate>
                </utu:CardContentControl.ContentTemplate>
            </utu:CardContentControl>
        </DataTemplate>

        <DataTemplate x:Key="TransferEmptyTemplate">
            <utu:AutoLayout Margin="32,0"
							PrimaryAxisAlignment="Center"
							CounterAxisAlignment="Center"
							Spacing="24">
                <BitmapIcon Width="64"
							Height="72"
							UriSource="{ThemeResource Empty_Box}" />
                <TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
						   Style="{StaticResource TitleLarge}"
						   Text="No Favorites Yet"
						   TextAlignment="Center"
						   TextWrapping="Wrap" />
                <TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
						   Text="You haven't added any transfers to your favorites. Start exploring and find delicious transfers to try and love! Once you've found a transfer you enjoy, click on the heart icon to add it to your favorites for easy access later."
						   TextAlignment="Center"
						   TextWrapping="Wrap" />
                <Button HorizontalContentAlignment="Center"
						VerticalContentAlignment="Center"
						Content="See popular transfers"
						CornerRadius="20"
						Foreground="{ThemeResource PrimaryBrush}"
						Style="{StaticResource TextButtonStyle}">
                    <ut:ControlExtensions.Icon>
                        <PathIcon Data="{StaticResource Icon_Star_Outline}"
								  Foreground="{ThemeResource PrimaryBrush}" />
                    </ut:ControlExtensions.Icon>
                </Button>
            </utu:AutoLayout>
        </DataTemplate>
    </Page.Resources>

    <utu:AutoLayout PrimaryAxisAlignment="Stretch">
        <utu:NavigationBar>
            <utu:NavigationBar.Content>
                <Grid>
                    <Image Source="{ThemeResource JTsAppSignature}"
						   HorizontalAlignment="Left"
						   Width="128"
						   Height="40" />
                </Grid>
            </utu:NavigationBar.Content>
            <utu:NavigationBar.PrimaryCommands>
                <AppBarButton uen:Navigation.Request="!Profile">
                    <AppBarButton.Icon>
                        <PathIcon Data="{StaticResource Icon_Person_Outline}" />
                    </AppBarButton.Icon>
                </AppBarButton>
                <AppBarButton uen:Navigation.Request="!Notifications">
                    <AppBarButton.Icon>
                        <PathIcon Data="{StaticResource Icon_Notification_Bell}" />
                    </AppBarButton.Icon>
                </AppBarButton>
            </utu:NavigationBar.PrimaryCommands>
        </utu:NavigationBar>
        <utu:AutoLayout Padding="16"
						uen:Region.Attached="True"
						utu:AutoLayout.PrimaryAlignment="Stretch"
						Spacing="16">
            <utu:TabBar uen:Region.Attached="True"
						Background="{ThemeResource BackgroundBrush}"
						Style="{StaticResource TopTabBarStyle}">
                <utu:TabBarItem x:Name="TabBarTransferItem"
								uen:Region.Name="MyTransfers"
								Content="All Transfers"
								Foreground="{ThemeResource OnSurfaceMediumBrush}"
								IsSelected="True" />
                <utu:TabBarItem uen:Region.Name="JobTransfers"
								Content="My JobTransfers"
								Foreground="{ThemeResource OnSurfaceMediumBrush}" />
            </utu:TabBar>
            <Grid uen:Region.Attached="True"
				  uen:Region.Navigator="Visibility"
				  utu:AutoLayout.CounterAlignment="Stretch"
				  utu:AutoLayout.PrimaryAlignment="Stretch">
                <Grid uen:Region.Name="MyTransfers">
                    <uer:FeedView utu:AutoLayout.CounterAlignment="Stretch"
								  utu:AutoLayout.PrimaryAlignment="Stretch"
								  NoneTemplate="{StaticResource TransferEmptyTemplate}"
								  Source="{Binding FavoriteTransferRequest}">
                        <DataTemplate>
                            <!-- It is necessary to disable the HorizontalScrollMode because of this issue: https://github.com/unoplatform/uno/issues/12871 -->
                            <ScrollViewer x:Name="TransfersScrollViewer"
										  utu:AutoLayout.PrimaryAlignment="Stretch"
										  HorizontalScrollMode="Disabled"
										  VerticalScrollBarVisibility="Hidden">
                                <utu:AutoLayout>
                                    <TextBlock utu:AutoLayout.CounterAlignment="Start"
											   Margin="0,0,0,10"
											   Style="{StaticResource BodyLarge}">
										<Run Text="{Binding Data.Count}" />
										<Run Text="results" />
                                    </TextBlock>
                                    <muxc:ItemsRepeater uen:Navigation.Request="TransferRequestDetails"
														ItemTemplate="{StaticResource TransferTemplate}"
														ItemsSource="{Binding Data}"
														Layout="{StaticResource ResponsiveGridLayout}" />
                                </utu:AutoLayout>
                            </ScrollViewer>
                        </DataTemplate>
                    </uer:FeedView>
                </Grid>

                <Grid uen:Region.Name="JobTransfers"
					  Visibility="Collapsed">
                    <!-- It is necessary to disable the HorizontalScrollMode because of this issue: https://github.com/unoplatform/uno/issues/12871 -->

                    <uer:FeedView utu:AutoLayout.CounterAlignment="Stretch"
								  utu:AutoLayout.PrimaryAlignment="Stretch"
								  NoneTemplate="{StaticResource JobTransferEmptyTemplate}"
								  Source="{Binding SavedJobTransfers}">
                        <DataTemplate>
							<ScrollViewer x:Name="TransfersScrollViewer"
										  HorizontalScrollMode="Disabled"
										  VerticalScrollBarVisibility="Hidden">
								<muxc:ItemsRepeater Margin="0,0,0,16"
													uen:Navigation.Request="JobTransferDetails"
													ItemTemplate="{StaticResource JobTransferTemplate}"
													ItemsSource="{Binding Data}"
													Layout="{StaticResource ResponsiveGridLayout}" />
							</ScrollViewer>
						</DataTemplate>
                    </uer:FeedView>
                    <Button HorizontalAlignment="Right"
							VerticalAlignment="Bottom"
							uen:Navigation.Request="CreateJobTransfer"
							Content="Add"
							Style="{StaticResource JTsFabButtonStyle}">
                        <ut:ControlExtensions.Icon>
                            <PathIcon Data="{StaticResource Icon_Add}" />
                        </ut:ControlExtensions.Icon>
                    </Button>
                </Grid>
            </Grid>
        </utu:AutoLayout>
    </utu:AutoLayout>
</Page>
