using JT.Business.Services.Users;
using JT.Content.Client;

namespace JT.Business.Services.JobTransfers;

public class JobTransferService(JTApiClient client, IMessenger messenger, IUserService userService)
	: IJobTransferService
{
	public async ValueTask<JobTransfer> <PERSON>reate(string name, IImmutableList<TransferRequest> transferRequests, CancellationToken ct)
	{
		var currentUser = await userService.GetCurrent(ct);
		var jobTransferData = JobTransfer.CreateData(currentUser.Id, name, transferRequests);

		await client.Api.JobTransfer.PostAsync(jobTransferData, cancellationToken: ct);

		return new JobTransfer(jobTransferData);
	}

	public async ValueTask<JobTransfer> Update(JobTransfer jobTransfer, IImmutableList<TransferRequest> transferRequests, CancellationToken ct)
	{
		var updatedJobTransferData = jobTransfer.ToData(transferRequests);
		await client.Api.JobTransfer.PutAsync(updatedJobTransferData, cancellationToken: ct);

		var newJobTransfer= new JobTransfer(updatedJobTransferData);
		messenger.Send(new EntityMessage<JobTransfer>(EntityChange.Updated, newJobTransfer));
		return newJobTransfer;
	}

	public async ValueTask Update(JobTransfer jobTransfer, CancellationToken ct)
	{
		var JobTransferData = jobTransfer.ToData();

		await client.Api.JobTransfer.PutAsync(JobTransferData, cancellationToken: ct);
		messenger.Send(new EntityMessage<JobTransfer>(EntityChange.Updated, jobTransfer));
	}

	public async ValueTask Save(JobTransfer jobTransfer, CancellationToken ct)
	{
		var currentUser = await userService.GetCurrent(ct);
		var jobTransferData = jobTransfer.ToData();

		await client.Api.JobTransfer.Save.PostAsync(
            jobTransferData,
			config => config.QueryParameters.UserId = currentUser.Id,
			cancellationToken: ct
		);
		messenger.Send(new EntityMessage<JobTransfer>(EntityChange.Created, jobTransfer));
	}

	public async ValueTask<IImmutableList<JobTransfer>> GetSaved(CancellationToken ct)
	{
		var currentUser = await userService.GetCurrent(ct);
		var savedJobTransfersData = await client.Api.JobTransfer.Saved.GetAsync(config => config.QueryParameters.UserId = currentUser.Id, cancellationToken: ct);
		return savedJobTransfersData?.Select(c => new JobTransfer(c)).ToImmutableList() ?? ImmutableList<JobTransfer>.Empty;
	}

	public async ValueTask<IImmutableList<JobTransfer>> GetByUser(Guid userId, CancellationToken ct)
	{
		var allJobTransfersData = await client.Api.JobTransfer.GetAsync(cancellationToken: ct);
		return allJobTransfersData?.Where(r => r.UserId == userId).Select(x => new JobTransfer(x)).ToImmutableList() ?? ImmutableList<JobTransfer>.Empty;
	}
}
