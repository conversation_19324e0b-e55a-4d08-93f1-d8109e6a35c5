{"version": "2.0.0", "tasks": [{"label": "build-wasm", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/JT/JT.csproj", "/property:GenerateFullPaths=true", "/property:TargetFramework=net9.0-browserwasm", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish-wasm", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/JT/JT.csproj", "/property:GenerateFullPaths=true", "/property:TargetFramework=net9.0-browserwasm", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-desktop", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/JT/JT.csproj", "/property:GenerateFullPaths=true", "/property:TargetFramework=net9.0-desktop", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish-desktop", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/JT/JT.csproj", "/property:GenerateFullPaths=true", "/property:TargetFramework=net9.0-desktop", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}]}