using JT.Business.Models;
using JT.Content.Client;

namespace JT.Business.Services.Users;

public class UserService(
	JTApiClient client,
	IWritableOptions<Credentials> credentialOptions)
	: IUserService
{
	private readonly IWritableOptions<Credentials> _credentialOptions = credentialOptions;

	private IState<User> _user => State.Async(this, GetCurrent);

	public IFeed<User> User => _user;

	public async ValueTask<IImmutableList<User>> GetPopularCreators(CancellationToken ct)
	{
		var popularCreatorsData = await client.Api.User.PopularCreators.GetAsync(cancellationToken: ct);
		return popularCreatorsData?.Select(data => new User(data)).ToImmutableList() ?? ImmutableList<User>.Empty;
	}

	public async ValueTask<User> GetCurrent(CancellationToken ct)
	{
		var currentUserData = await client.Api.User.Current.GetAsync(cancellationToken: ct);
		if (currentUserData == null)
		{
			throw new InvalidOperationException("Current user not found");
		}
		return new User(currentUserData);
	}

	public async ValueTask<User> GetById(Guid userId, CancellationToken ct)
	{
		var userData = await client.Api.User[userId].GetAsync(cancellationToken: ct);
		if (userData == null)
		{
			throw new InvalidOperationException($"User with ID {userId} not found");
		}
		return new User(userData);
	}

	public async ValueTask Update(User user, CancellationToken ct)
	{
		// Use the generated Update endpoint for user updates
		await client.Api.User.Update.PutAsync(user.ToData(), cancellationToken: ct);
		await _user.UpdateAsync(_ => user, ct);
	}

	//public async ValueTask<User> Register(User user, CancellationToken ct = default)
	//{
	//	var userData = await client.Api.User.Register.PostAsync(user.ToData(), cancellationToken: ct);
	//	return new User(userData);
	//}

	//public async ValueTask<bool> Authenticate(string email, string password, CancellationToken ct = default)
	//{
	//	try
	//	{
	//		var userId = await client.Api.User.Authenticate.PostAsync(
	//			new LoginRequest { Email = email, Password = password }, 
	//			cancellationToken: ct);
			
	//		if (userId != null)
	//		{
	//			await _credentialOptions.UpdateAsync(_ => new Credentials
	//			{
	//				Email = email,
	//				SaveCredentials = true
	//			});
	//			return true;
	//		}
	//		return false;
	//	}
	//	catch
	//	{
	//		return false;
	//	}
	//}

	//public async ValueTask<bool> UpdateSubscription(Guid userId, string subscriptionTier, CancellationToken ct = default)
	//{
	//	try
	//	{
	//		var userData = await client.Api.User[userId].GetAsync(cancellationToken: ct);
	//		if (userData != null)
	//		{
	//			userData.SubscriptionTier = subscriptionTier;
	//			await client.Api.User[userId].PutAsync(userData, cancellationToken: ct);
	//			return true;
	//		}
	//		return false;
	//	}
	//	catch
	//	{
	//		return false;
	//	}
	//}

	//public async ValueTask<bool> AddTokens(Guid userId, int tokens, CancellationToken ct = default)
	//{
	//	try
	//	{
	//		var userData = await client.Api.User[userId].GetAsync(cancellationToken: ct);
	//		if (userData != null)
	//		{
	//			userData.TokenBalance = (userData.TokenBalance ?? 0) + tokens;
	//			await client.Api.User[userId].PutAsync(userData, cancellationToken: ct);
	//			return true;
	//		}
	//		return false;
	//	}
	//	catch
	//	{
	//		return false;
	//	}
	//}

	//public async ValueTask<bool> DeductTokens(Guid userId, int tokens, CancellationToken ct = default)
	//{
	//	try
	//	{
	//		var userData = await client.Api.User[userId].GetAsync(cancellationToken: ct);
	//		if (userData != null && (userData.TokenBalance ?? 0) >= tokens)
	//		{
	//			userData.TokenBalance = userData.TokenBalance - tokens;
	//			await client.Api.User[userId].PutAsync(userData, cancellationToken: ct);
	//			return true;
	//		}
	//		return false;
	//	}
	//	catch
	//	{
	//		return false;
	//	}
	//}

	//public async ValueTask<string> GenerateReferralCode(Guid userId, CancellationToken ct = default)
	//{
	//	// Generate a unique referral code based on user ID
	//	string referralCode = $"JT-{userId.ToString().Substring(0, 8).ToUpper()}";
		
	//	// Store the referral code in the user data
	//	try
	//	{
	//		var userData = await client.Api.User[userId].GetAsync(cancellationToken: ct);
	//		if (userData != null)
	//		{
	//			userData.ReferralCode = referralCode;
	//			await client.Api.User[userId].PutAsync(userData, cancellationToken: ct);
	//		}
	//	}
	//	catch
	//	{
	//		// Handle error
	//	}
		
	//	return referralCode;
	//}

	//public async ValueTask<bool> ProcessReferral(string referralCode, Guid newUserId, CancellationToken ct = default)
	//{
	//	try
	//	{
	//		// Find the referring user by referral code
	//		var users = await client.Api.User.GetAsync(cancellationToken: ct);
	//		var referrer = users?.FirstOrDefault(u => u.ReferralCode == referralCode);
			
	//		if (referrer != null)
	//		{
	//			// Add bonus tokens to referrer (typically handled by TokenService)
	//			referrer.TokenBalance = (referrer.TokenBalance ?? 0) + 25; // Standard bonus
	//			await client.Api.User[referrer.Id].PutAsync(referrer, cancellationToken: ct);
				
	//			// Record the referral relationship
	//			// This would typically be handled by a dedicated ReferralService
				
	//			return true;
	//		}
	//		return false;
	//	}
	//	catch
	//	{
	//		return false;
	//	}
	//}

	  //public async ValueTask<IImmutableList<User>> GetBySubscriptionTier(string tier, CancellationToken ct = default)
   // {
   //     var users = await client.Api.User.GetAsync(cancellationToken: ct);
   //     return users?
   //         .Where(u => u.SubscriptionTier?.Name == tier) // Compare the 'Name' property of SubscriptionTier
   //         .Select(data => new User(data))
   //         .ToImmutableList() ?? ImmutableList<User>.Empty;
   // }

	public async ValueTask<IImmutableList<User>> GetByLocation(string location, CancellationToken ct = default)
	{
		var users = await client.Api.User.GetAsync(cancellationToken: ct);
		return users?
			.Where(u => 
				u.CurrentEmployerLocation?.Contains(location, StringComparison.OrdinalIgnoreCase) == true ||
				u.CurrentEmployerCity?.Contains(location, StringComparison.OrdinalIgnoreCase) == true ||
				u.CurrentEmployerState?.Contains(location, StringComparison.OrdinalIgnoreCase) == true)
			.Select(data => new User(data))
			.ToImmutableList() ?? ImmutableList<User>.Empty;
	}

    //In case we need to add auth
    //public async ValueTask<bool> BasicAuthenticate(string email, string password, CancellationToken ct)
    //{
    //    var autentication = await _userEndpoint.Authenticate(email, password, ct);
    //    if (autentication)
    //    {
    //        await _credentialOptions.UpdateAsync(_ => new Credentials()
    //        {
    //            Email = email,
    //            SaveCredentials = true
    //        });

    //        return true;
    //    }

    //    return false;
    //}
}


