namespace JT.Business.Services.TransferRequests;

public interface ITransferRequestService
{
	ValueTask<IImmutableList<TransferRequest>> GetAll(CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> GetByUser(Guid userId, CancellationToken ct = default);
	ValueTask<TransferRequest?> GetById(Guid id, CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> GetByStatus(string status, CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> GetByLocation(string location, CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> GetByIndustry(string industry, CancellationToken ct = default);
    //ValueTask<IImmutableList<TransferRequest>> Search(string query, CancellationToken ct = default);
    ValueTask<TransferRequest> Create(TransferRequest transferRequest, CancellationToken ct = default);
	ValueTask<TransferRequest> Update(TransferRequest transferRequest, CancellationToken ct = default);
	ValueTask<bool> Delete(Guid id, CancellationToken ct = default);
	ValueTask<bool> UpdateStatus(Guid id, string status, CancellationToken ct = default);
	ValueTask<bool> IncrementViewCount(Guid id, CancellationToken ct = default);
    ValueTask<IImmutableList<TransferRequest>> GetTrending(CancellationToken ct);
    //ValueTask<IImmutableList<TransferRequest>> GetTrending(CancellationToken ct = default);
	ValueTask<IImmutableList<TransferRequest>> GetExpiringSoon(CancellationToken ct = default);
    ValueTask<IImmutableList<TransferRequest>> GetByCategory(int categoryId, CancellationToken ct);
    ValueTask<IImmutableList<Category>> GetCategories(CancellationToken ct = default);
    ValueTask<IImmutableList<CategoryWithCount>> GetCategoriesWithCount(CancellationToken ct);
    /// <summary>
    /// TransferRequests favorited by the current user, supports pagination
    /// </summary>
    /// <param name="pageSize">number of items to display per page</param>
    /// <param name="firstItemIndex">index of the first item on the requested page</param>
    /// <param name="ct"></param>
    /// <returns>
    /// Current user's transferRequests within the requested page
    /// </returns>
    ValueTask<IImmutableList<TransferRequest>> GetFavoritedWithPagination(uint pageSize, uint firstItemIndex, CancellationToken ct);
    
    /// <summary>
    /// Favorited transferRequests.
    /// </summary>
    IListState<TransferRequest> FavoritedTransferRequests { get; }

    /// <summary>
    /// Save transferRequest 
    /// </summary>
    /// <param name="transferRequest"> transferRequest to save </param>
    /// <param name="ct"></param>
    /// <returns>
    /// </returns>
    ValueTask Favorite(TransferRequest transferRequest, CancellationToken ct);
    /// <summary>
    /// Transfers recently added
    /// </summary>
    /// <param name="ct"></param>
    /// <returns>
    /// Get recent transferRequests or new transferRequests
    /// </returns>
    ValueTask<IImmutableList<TransferRequest>> GetRecent(CancellationToken ct);
    /// <summary>
    /// Get review's steps
    /// </summary>
    /// <param name="transferRequestId">id from the transfer</param>
    /// <param name="ct"></param>
    /// <returns>
    /// Transfer's steps
    /// </returns>
    ValueTask<IImmutableList<Step>> GetSteps(Guid transferRequestId, CancellationToken ct);

    /// <summary>
    /// Filter transfers from api
    /// </summary>
    /// <param name="term">The search term</param>
    /// <param name="ct"></param>
    /// <returns>
    /// Get transfers filter by different options selected by the user
    /// </returns>
    ValueTask<IImmutableList<TransferRequest>> Search(string term, SearchFilter filter, CancellationToken ct);

    /// <summary>
    /// Popular Transfers
    /// </summary>
    /// <param name="ct"></param>
    /// <returns>
    /// Get popular transfers filter 
    /// </returns>
    ValueTask<IImmutableList<TransferRequest>> GetPopular(CancellationToken ct);
    ValueTask<IImmutableList<TransferRequest>> GetRecommended(CancellationToken ct);

    ValueTask<IImmutableList<TransferRequest>> GetFromJTs(CancellationToken ct);
    IImmutableList<string> GetSearchHistory();
}
