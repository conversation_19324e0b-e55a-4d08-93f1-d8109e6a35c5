using JT.Content.Client;

namespace JT.Business.Services.Skills;

public class SkillService : ISkillService
{
	private readonly JTApiClient api;

	public SkillService(JTApiClient api)
	{
		this.api = api;
	}

	public async ValueTask<IImmutableList<Skill>> GetAll(CancellationToken ct = default)
	{
		try
		{
			var skillsData = await api.Api.Skill.GetAsync(cancellationToken: ct);
			return skillsData?.Select(s => new Skill(s)).ToImmutableList() ?? ImmutableList<Skill>.Empty;
		}
		catch
		{
			return ImmutableList<Skill>.Empty;
		}
	}

	public async ValueTask<Skill?> GetById(int id, CancellationToken ct = default)
	{
		try
		{
			var skillData = await api.Api.Skill[id].GetAsync(cancellationToken: ct);
			return skillData != null ? new Skill(skillData) : null;
		}
		catch
		{
			return null;
		}
	}

	public async ValueTask<IImmutableList<Skill>> GetByCategory(string category, CancellationToken ct = default)
	{
		var allSkills = await GetAll(ct);
		return allSkills.Where(s => string.Equals(s.Category, category, StringComparison.OrdinalIgnoreCase)).ToImmutableList();
	}

	public async ValueTask<IImmutableList<Skill>> GetByIndustry(string industry, CancellationToken ct = default)
	{
		var allSkills = await GetAll(ct);
		return allSkills.Where(s => string.Equals(s.Industry, industry, StringComparison.OrdinalIgnoreCase)).ToImmutableList();
	}

	public async ValueTask<IImmutableList<Skill>> GetTrendingSkills(CancellationToken ct = default)
	{
		var allSkills = await GetAll(ct);
		return allSkills
			.Where(s => s.PopularityScore >= 80)
			.OrderByDescending(s => s.PopularityScore)
			.Take(10)
			.ToImmutableList();
	}

	public async ValueTask<IImmutableList<Skill>> GetPopularSkills(int count = 10, CancellationToken ct = default)
	{
		var allSkills = await GetAll(ct);
		return allSkills
			.OrderByDescending(s => s.PopularityScore)
			.Take(count)
			.ToImmutableList();
	}

	public async ValueTask<IImmutableList<Skill>> SearchSkills(string query, CancellationToken ct = default)
	{
		var allSkills = await GetAll(ct);
		return allSkills.Where(s => 
			s.Name?.Contains(query, StringComparison.OrdinalIgnoreCase) == true ||
			s.Category?.Contains(query, StringComparison.OrdinalIgnoreCase) == true ||
			s.Industry?.Contains(query, StringComparison.OrdinalIgnoreCase) == true
		).ToImmutableList();
	}

	public async ValueTask<IImmutableList<string>> GetCategories(CancellationToken ct = default)
	{
		var allSkills = await GetAll(ct);
		return allSkills
			.Where(s => !string.IsNullOrEmpty(s.Category))
			.Select(s => s.Category!)
			.Distinct()
			.OrderBy(c => c)
			.ToImmutableList();
	}

	public async ValueTask<IImmutableList<string>> GetIndustries(CancellationToken ct = default)
	{
		var allSkills = await GetAll(ct);
		return allSkills
			.Where(s => !string.IsNullOrEmpty(s.Industry))
			.Select(s => s.Industry!)
			.Distinct()
			.OrderBy(i => i)
			.ToImmutableList();
	}
}
