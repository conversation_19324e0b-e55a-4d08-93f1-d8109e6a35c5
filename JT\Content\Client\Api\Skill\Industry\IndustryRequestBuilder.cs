// <auto-generated/>
#pragma warning disable CS0618
using JT.Content.Client.Api.Skill.Industry.Item;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace JT.Content.Client.Api.Skill.Industry
{
    /// <summary>
    /// Builds and executes requests for operations under \api\Skill\industry
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class IndustryRequestBuilder : BaseRequestBuilder
    {
        /// <summary>Gets an item from the JT.Content.Client.api.Skill.industry.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::JT.Content.Client.Api.Skill.Industry.Item.WithIndustryItemRequestBuilder"/></returns>
        public global::JT.Content.Client.Api.Skill.Industry.Item.WithIndustryItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("industry", position);
                return new global::JT.Content.Client.Api.Skill.Industry.Item.WithIndustryItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.Skill.Industry.IndustryRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public IndustryRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Skill/industry", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.Skill.Industry.IndustryRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public IndustryRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Skill/industry", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
