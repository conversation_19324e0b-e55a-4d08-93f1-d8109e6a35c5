using JT.Content.Client;

namespace JT.Business.Services.Subscriptions;

public class SubscriptionService : ISubscriptionService
{
	private readonly JTApiClient api;

	public SubscriptionService(JTApiClient api)
	{
		this.api = api;
	}

	public async ValueTask<IImmutableList<SubscriptionPlan>> GetAllPlans(CancellationToken ct = default)
	{
		try
		{
			var plansData = await api.Api.Subscription.Plans.GetAsync(cancellationToken: ct);
			return plansData?.Select(p => new SubscriptionPlan(p)).ToImmutableList() ?? ImmutableList<SubscriptionPlan>.Empty;
		}
		catch
		{
			return ImmutableList<SubscriptionPlan>.Empty;
		}
	}

	public async ValueTask<SubscriptionPlan?> GetPlanById(string planId, CancellationToken ct = default)
	{
		try
		{
			var planDataList = await api.Api.Subscription.Plans[planId].GetAsync(cancellationToken: ct);
			var planData = planDataList?.FirstOrDefault();
			return planData != null ? new SubscriptionPlan(planData) : null;
		}
		catch
		{
			return null;
		}
	}

	public async ValueTask<SubscriptionPlan?> GetUserCurrentPlan(Guid userId, CancellationToken ct = default)
	{
		try
		{
			var userData = await api.Api.User[userId].GetAsync(cancellationToken: ct);
			if (userData?.SubscriptionTier?.Id != null)
			{
				return await GetPlanById(userData.SubscriptionTier.Id.ToLower(), ct);
			}
			return null;
		}
		catch
		{
			return null;
		}
	}

	public async ValueTask<bool> UpgradeUserSubscription(Guid userId, string newPlanId, CancellationToken ct = default)
	{
		try
		{
			// This would typically involve payment processing
			// For now, just update the user's subscription tier
			var userData = await api.Api.User[userId].GetAsync(cancellationToken: ct);
			if (userData != null)
			{
				// Get the new plan data to assign to the user
				var newPlanDataList = await api.Api.Subscription.Plans[newPlanId].GetAsync(cancellationToken: ct);
				var newPlanData = newPlanDataList?.FirstOrDefault();
				if (newPlanData != null)
				{
					userData.SubscriptionTier = newPlanData;
					await api.Api.User.Update.PutAsync(userData, cancellationToken: ct);
					return true;
				}
			}
			return false;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> IsFeatureAvailable(Guid userId, string feature, CancellationToken ct = default)
	{
		var plan = await GetUserCurrentPlan(userId, ct);
		if (plan == null) return false;

		return feature.ToLower() switch
		{
			"whatsapp_alerts" => plan.WhatsAppAlerts,
			"sms_alerts" => plan.SMSAlerts,
			"priority_listing" => plan.PriorityListing,
			"ad_free" => plan.AdFreeExperience,
			_ => false
		};
	}

	public async ValueTask<int> GetRemainingDestinations(Guid userId, CancellationToken ct = default)
	{
		var plan = await GetUserCurrentPlan(userId, ct);
		return plan?.MaxDestinations ?? 1;
	}

	public async ValueTask<int> GetRemainingInvites(Guid userId, CancellationToken ct = default)
	{
		var plan = await GetUserCurrentPlan(userId, ct);
		if (plan?.InviteLimit == -1) return int.MaxValue; // Unlimited
		return plan?.InviteLimit ?? 3;
	}

	public async ValueTask<bool> CanSendWhatsAppAlerts(Guid userId, CancellationToken ct = default)
	{
		return await IsFeatureAvailable(userId, "whatsapp_alerts", ct);
	}

	public async ValueTask<bool> CanSendSMSAlerts(Guid userId, CancellationToken ct = default)
	{
		return await IsFeatureAvailable(userId, "sms_alerts", ct);
	}

	public async ValueTask<double> GetTokenMultiplier(Guid userId, CancellationToken ct = default)
	{
		var plan = await GetUserCurrentPlan(userId, ct);
		return plan?.TokenMultiplier ?? 1.0;
	}

	public ValueTask<DateTime?> GetSubscriptionExpiry(Guid userId, CancellationToken ct = default)
	{
		// This would typically be stored in a subscription table
		// For now, return a default expiry date
		return ValueTask.FromResult<DateTime?>(DateTime.UtcNow.AddMonths(1));
	}

	public async ValueTask<bool> IsSubscriptionExpired(Guid userId, CancellationToken ct = default)
	{
		var expiry = await GetSubscriptionExpiry(userId, ct);
		return expiry.HasValue && DateTime.UtcNow > expiry.Value;
	}
}
