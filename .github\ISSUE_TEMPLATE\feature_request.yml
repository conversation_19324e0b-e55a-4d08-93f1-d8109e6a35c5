name: Feature Request
description: Suggest an idea for this project
labels: ["enhancement"]
assignees: []
body:
  - type: markdown
    attributes:
      value: |
        Fill in this template with some detail. A detailed description, perhaps supported with some screenshots, mockups, and a (public) API design in pseudo-code.
  - type: textarea
    id: description
    attributes:
      label: Description
      description: Please give us a detailed description of the feature that you envision. Focus on _what_ this feature does, over the _why_ you want this feature. What would be the end-result when implemented? Feel free to add any diagrams, (mocked up) screenshots, or other materials that support your story.
      placeholder: I would love to have a button that I can make shiny. This is something that is supported on all the underlaying platforms.
    validations:
      required: true
  - type: textarea
    id: api-changes
    attributes:
      label: API Changes
      description: Include a list of all API changes, additions, subtractions as would be required by your proposal. These APIs should be considered placeholders, so the naming is not as important as getting the concepts correct. If possible you should include some example (pseudo-)code of usage of your new API.
      placeholder: |
        ```csharp
        var button = new Button ();
        button.MakeShiny = true; // new API
        ```

        The MakeShiny API works even if the button is already visible.
    validations:
      required: true
  - type: textarea
    id: use-case
    attributes:
      label: Intended Use-Case
      description: Provide a detailed example of where your proposal would be used and for what purpose. Focus on _why_ you want this feature instead of _what_ the feature does.
      placeholder: I have a situation where I would really want a shiny button to make it stand out from the rest of the plain and boring buttons.
    validations:
      required: true