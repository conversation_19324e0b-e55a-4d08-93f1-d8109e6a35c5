using JT.Business.Services.Promotions;
using JT.Business.Services.Users;

namespace JT.Presentation;

public partial record PromotionModel
{
	private readonly INavigator _navigator;
	private readonly IPromotionService _promotionService;
	private readonly IPromotionPackageService _promotionPackageService;
	private readonly IUserService _userService;
	private readonly IMessenger _messenger;

	public PromotionModel(
		INavigator navigator,
		IPromotionService promotionService,
		IPromotionPackageService promotionPackageService,
		IUserService userService,
		IMessenger messenger)
	{
		_navigator = navigator;
		_promotionService = promotionService;
		_promotionPackageService = promotionPackageService;
		_userService = userService;
		_messenger = messenger;
	}

	// Active promotions for display
	public IListFeed<Promotion> ActivePromotions => ListFeed.Async(_promotionService.GetActivePromotions);

	// Promotion packages for purchase
	public IListFeed<PromotionPackage> AvailablePackages => ListFeed.Async(_promotionPackageService.GetActivePackages);

	// Popular packages
	public IListFeed<PromotionPackage> PopularPackages => ListFeed.Async(_promotionPackageService.GetPopularPackages);

	// Current user
	public IFeed<User> CurrentUser => _userService.User;

	// Promotions by category
	public IListFeed<Promotion> BannerPromotions => ListFeed.Async(GetBannerPromotions);
	public IListFeed<Promotion> SponsoredContent => ListFeed.Async(GetSponsoredContent);
	public IListFeed<Promotion> JobPromotions => ListFeed.Async(GetJobPromotions);

	[ObservableProperty]
	private string _selectedCategory = "all";

	[ObservableProperty]
	private string _selectedPackageType = "all";

	[ObservableProperty]
	private bool _isLoading;

	private async ValueTask<IImmutableList<Promotion>> GetBannerPromotions(CancellationToken ct)
	{
		return await _promotionService.GetPromotionsByType("banner", ct);
	}

	private async ValueTask<IImmutableList<Promotion>> GetSponsoredContent(CancellationToken ct)
	{
		return await _promotionService.GetPromotionsByType("sponsored_content", ct);
	}

	private async ValueTask<IImmutableList<Promotion>> GetJobPromotions(CancellationToken ct)
	{
		return await _promotionService.GetPromotionsByType("job_promotion", ct);
	}

	public async ValueTask ViewPromotion(Promotion promotion, CancellationToken ct)
	{
		try
		{
			var user = await _userService.GetCurrent(ct);
			await _promotionService.TrackView(promotion.Id, user?.Id.ToString(), ct);
			
			// Navigate to promotion details or external website
			if (!string.IsNullOrEmpty(promotion.WebsiteUrl))
			{
				await _navigator.NavigateToUrlAsync(this, promotion.WebsiteUrl, cancellation: ct);
			}
		}
		catch (Exception ex)
		{
			await _messenger.Send(new ErrorMessage($"Failed to view promotion: {ex.Message}"));
		}
	}

	public async ValueTask ClickPromotion(Promotion promotion, CancellationToken ct)
	{
		try
		{
			var user = await _userService.GetCurrent(ct);
			await _promotionService.TrackClick(promotion.Id, user?.Id.ToString(), ct);
			
			// Navigate to promotion website
			if (!string.IsNullOrEmpty(promotion.WebsiteUrl))
			{
				await _navigator.NavigateToUrlAsync(this, promotion.WebsiteUrl, cancellation: ct);
			}
		}
		catch (Exception ex)
		{
			await _messenger.Send(new ErrorMessage($"Failed to track click: {ex.Message}"));
		}
	}

	public async ValueTask PurchasePackage(PromotionPackage package, CancellationToken ct)
	{
		try
		{
			IsLoading = true;
			
			var user = await _userService.GetCurrent(ct);
			if (user == null)
			{
				await _messenger.Send(new ErrorMessage("Please log in to purchase promotion packages"));
				return;
			}

			// Navigate to payment flow
			await _navigator.NavigateRouteAsync(this, 
				route: "/Main/-/PromotionPayment", 
				data: new { PackageId = package.Id, PackageName = package.DisplayName, Price = package.PriceOMR }, 
				cancellation: ct);
		}
		catch (Exception ex)
		{
			await _messenger.Send(new ErrorMessage($"Failed to initiate purchase: {ex.Message}"));
		}
		finally
		{
			IsLoading = false;
		}
	}

	public async ValueTask FilterByCategory(string category, CancellationToken ct)
	{
		SelectedCategory = category;
		// Trigger refresh of filtered promotions
		await _messenger.Send(new CategoryFilterChanged(category));
	}

	public async ValueTask FilterPackagesByType(string type, CancellationToken ct)
	{
		SelectedPackageType = type;
		// Trigger refresh of filtered packages
		await _messenger.Send(new PackageTypeFilterChanged(type));
	}

	public async ValueTask ViewPromotionAnalytics(Promotion promotion, CancellationToken ct)
	{
		try
		{
			var analytics = await _promotionService.GetPromotionAnalytics(promotion.Id, null, ct);
			if (analytics != null)
			{
				await _navigator.NavigateRouteAsync(this, 
					route: "/Main/-/PromotionAnalytics", 
					data: new { PromotionId = promotion.Id, Analytics = analytics }, 
					cancellation: ct);
			}
		}
		catch (Exception ex)
		{
			await _messenger.Send(new ErrorMessage($"Failed to load analytics: {ex.Message}"));
		}
	}

	public async ValueTask CreatePromotion(CancellationToken ct)
	{
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/CreatePromotion", cancellation: ct);
	}

	public async ValueTask ViewMyPromotions(CancellationToken ct)
	{
		var user = await _userService.GetCurrent(ct);
		if (user != null)
		{
			await _navigator.NavigateRouteAsync(this, 
				route: "/Main/-/MyPromotions", 
				data: new { UserId = user.Id }, 
				cancellation: ct);
		}
	}

	public async ValueTask ContactSupport(CancellationToken ct)
	{
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Support", cancellation: ct);
	}

	public string GetPackageRecommendation(PromotionPackage package)
	{
		return package.Type switch
		{
			"banner" => "Perfect for brand awareness and visibility",
			"sponsored" => "Ideal for thought leadership and content marketing",
			"directory" => "Essential for online business presence",
			"job_promotion" => "Best for recruitment and talent acquisition",
			"event" => "Great for promoting events and gatherings",
			"premium" => "Ultimate solution for large enterprises",
			_ => "Comprehensive promotion solution"
		};
	}

	public string GetPromotionTypeIcon(string type)
	{
		return type switch
		{
			"banner" => "🎯",
			"sponsored_content" => "📝",
			"job_promotion" => "💼",
			"directory_listing" => "📋",
			"event" => "📅",
			_ => "📢"
		};
	}
}

// Message types for communication
public record CategoryFilterChanged(string Category);
public record PackageTypeFilterChanged(string Type);
public record PromotionViewed(string PromotionId);
public record PromotionClicked(string PromotionId);
