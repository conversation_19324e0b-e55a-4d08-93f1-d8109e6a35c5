// <auto-generated/>
#pragma warning disable CS0618
using JT.Content.Client.Api.JobCategory.Categories;
using JT.Content.Client.Api.JobCategory.Item;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace JT.Content.Client.Api.JobCategory
{
    /// <summary>
    /// Builds and executes requests for operations under \api\JobCategory
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class JobCategoryRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The categories property</summary>
        public global::JT.Content.Client.Api.JobCategory.Categories.CategoriesRequestBuilder Categories
        {
            get => new global::JT.Content.Client.Api.JobCategory.Categories.CategoriesRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>Gets an item from the JT.Content.Client.api.JobCategory.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::JT.Content.Client.Api.JobCategory.Item.JobCategoryItemRequestBuilder"/></returns>
        public global::JT.Content.Client.Api.JobCategory.Item.JobCategoryItemRequestBuilder this[int position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("id", position);
                return new global::JT.Content.Client.Api.JobCategory.Item.JobCategoryItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>Gets an item from the JT.Content.Client.api.JobCategory.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::JT.Content.Client.Api.JobCategory.Item.JobCategoryItemRequestBuilder"/></returns>
        [Obsolete("This indexer is deprecated and will be removed in the next major version. Use the one with the typed parameter instead.")]
        public global::JT.Content.Client.Api.JobCategory.Item.JobCategoryItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                if (!string.IsNullOrWhiteSpace(position)) urlTplParams.Add("id", position);
                return new global::JT.Content.Client.Api.JobCategory.Item.JobCategoryItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.JobCategory.JobCategoryRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public JobCategoryRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/JobCategory", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.JobCategory.JobCategoryRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public JobCategoryRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/JobCategory", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
