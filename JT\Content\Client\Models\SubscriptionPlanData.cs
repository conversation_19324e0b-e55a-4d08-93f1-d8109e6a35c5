// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace JT.Content.Client.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class SubscriptionPlanData : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The adFreeExperience property</summary>
        public bool? AdFreeExperience { get; set; }
        /// <summary>The color property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Color { get; set; }
#nullable restore
#else
        public string Color { get; set; }
#endif
        /// <summary>The description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
        /// <summary>The displayName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? DisplayName { get; set; }
#nullable restore
#else
        public string DisplayName { get; set; }
#endif
        /// <summary>The duration property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Duration { get; set; }
#nullable restore
#else
        public string Duration { get; set; }
#endif
        /// <summary>The emailAlerts property</summary>
        public bool? EmailAlerts { get; set; }
        /// <summary>The features property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? Features { get; set; }
#nullable restore
#else
        public List<string> Features { get; set; }
#endif
        /// <summary>The iconUrl property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? IconUrl { get; set; }
#nullable restore
#else
        public string IconUrl { get; set; }
#endif
        /// <summary>The id property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Id { get; set; }
#nullable restore
#else
        public string Id { get; set; }
#endif
        /// <summary>The inviteLimit property</summary>
        public int? InviteLimit { get; set; }
        /// <summary>The inviteLimitDisplay property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? InviteLimitDisplay { get; set; }
#nullable restore
#else
        public string InviteLimitDisplay { get; set; }
#endif
        /// <summary>The isPopular property</summary>
        public bool? IsPopular { get; set; }
        /// <summary>The maxDestinations property</summary>
        public int? MaxDestinations { get; set; }
        /// <summary>The name property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Name { get; set; }
#nullable restore
#else
        public string Name { get; set; }
#endif
        /// <summary>The paymentGatewayFeePercentage property</summary>
        public double? PaymentGatewayFeePercentage { get; set; }
        /// <summary>The priceDisplay property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? PriceDisplay { get; set; }
#nullable restore
#else
        public string PriceDisplay { get; set; }
#endif
        /// <summary>The priceOMR property</summary>
        public double? PriceOMR { get; set; }
        /// <summary>The priorityFrequency property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? PriorityFrequency { get; set; }
#nullable restore
#else
        public string PriorityFrequency { get; set; }
#endif
        /// <summary>The priorityListing property</summary>
        public bool? PriorityListing { get; set; }
        /// <summary>The serviceFeePercentage property</summary>
        public double? ServiceFeePercentage { get; set; }
        /// <summary>The smsAlerts property</summary>
        public bool? SmsAlerts { get; set; }
        /// <summary>The tokenMultiplier property</summary>
        public double? TokenMultiplier { get; set; }
        /// <summary>The totalFeePercentage property</summary>
        public double? TotalFeePercentage { get; set; }
        /// <summary>The whatsAppAlerts property</summary>
        public bool? WhatsAppAlerts { get; set; }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::JT.Content.Client.Models.SubscriptionPlanData"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::JT.Content.Client.Models.SubscriptionPlanData CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::JT.Content.Client.Models.SubscriptionPlanData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "adFreeExperience", n => { AdFreeExperience = n.GetBoolValue(); } },
                { "color", n => { Color = n.GetStringValue(); } },
                { "description", n => { Description = n.GetStringValue(); } },
                { "displayName", n => { DisplayName = n.GetStringValue(); } },
                { "duration", n => { Duration = n.GetStringValue(); } },
                { "emailAlerts", n => { EmailAlerts = n.GetBoolValue(); } },
                { "features", n => { Features = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
                { "iconUrl", n => { IconUrl = n.GetStringValue(); } },
                { "id", n => { Id = n.GetStringValue(); } },
                { "inviteLimit", n => { InviteLimit = n.GetIntValue(); } },
                { "inviteLimitDisplay", n => { InviteLimitDisplay = n.GetStringValue(); } },
                { "isPopular", n => { IsPopular = n.GetBoolValue(); } },
                { "maxDestinations", n => { MaxDestinations = n.GetIntValue(); } },
                { "name", n => { Name = n.GetStringValue(); } },
                { "paymentGatewayFeePercentage", n => { PaymentGatewayFeePercentage = n.GetDoubleValue(); } },
                { "priceDisplay", n => { PriceDisplay = n.GetStringValue(); } },
                { "priceOMR", n => { PriceOMR = n.GetDoubleValue(); } },
                { "priorityFrequency", n => { PriorityFrequency = n.GetStringValue(); } },
                { "priorityListing", n => { PriorityListing = n.GetBoolValue(); } },
                { "serviceFeePercentage", n => { ServiceFeePercentage = n.GetDoubleValue(); } },
                { "smsAlerts", n => { SmsAlerts = n.GetBoolValue(); } },
                { "tokenMultiplier", n => { TokenMultiplier = n.GetDoubleValue(); } },
                { "totalFeePercentage", n => { TotalFeePercentage = n.GetDoubleValue(); } },
                { "whatsAppAlerts", n => { WhatsAppAlerts = n.GetBoolValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteBoolValue("adFreeExperience", AdFreeExperience);
            writer.WriteStringValue("color", Color);
            writer.WriteStringValue("description", Description);
            writer.WriteStringValue("displayName", DisplayName);
            writer.WriteStringValue("duration", Duration);
            writer.WriteBoolValue("emailAlerts", EmailAlerts);
            writer.WriteCollectionOfPrimitiveValues<string>("features", Features);
            writer.WriteStringValue("iconUrl", IconUrl);
            writer.WriteStringValue("id", Id);
            writer.WriteIntValue("inviteLimit", InviteLimit);
            writer.WriteStringValue("inviteLimitDisplay", InviteLimitDisplay);
            writer.WriteBoolValue("isPopular", IsPopular);
            writer.WriteIntValue("maxDestinations", MaxDestinations);
            writer.WriteStringValue("name", Name);
            writer.WriteDoubleValue("paymentGatewayFeePercentage", PaymentGatewayFeePercentage);
            writer.WriteStringValue("priceDisplay", PriceDisplay);
            writer.WriteDoubleValue("priceOMR", PriceOMR);
            writer.WriteStringValue("priorityFrequency", PriorityFrequency);
            writer.WriteBoolValue("priorityListing", PriorityListing);
            writer.WriteDoubleValue("serviceFeePercentage", ServiceFeePercentage);
            writer.WriteBoolValue("smsAlerts", SmsAlerts);
            writer.WriteDoubleValue("tokenMultiplier", TokenMultiplier);
            writer.WriteDoubleValue("totalFeePercentage", TotalFeePercentage);
            writer.WriteBoolValue("whatsAppAlerts", WhatsAppAlerts);
        }
    }
}
#pragma warning restore CS0618
