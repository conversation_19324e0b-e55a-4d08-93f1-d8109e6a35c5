using EmployeeProfileData = JT.Client.Models.EmployeeProfileData;

namespace JT.Business.Models;

public partial record EmployeeProfile
{
	internal EmployeeProfile(EmployeeProfileData employeeProfileData)
	{
		Id = employeeProfileData.Id ?? string.Empty;
		UserId = employeeProfileData.UserId ?? Guid.Empty;
		FullName = employeeProfileData.FullName ?? string.Empty;
		CurrentPosition = employeeProfileData.CurrentPosition ?? string.Empty;
		CurrentEmployer = employeeProfileData.CurrentEmployer ?? string.Empty;
		CurrentLocation = employeeProfileData.CurrentLocation ?? string.Empty;
		Education = employeeProfileData.Education ?? string.Empty;
		Skills = employeeProfileData.Skills ?? string.Empty;
		YearsOfExperience = employeeProfileData.YearsOfExperience ?? 0;
		SalaryGrade = employeeProfileData.SalaryGrade ?? 0;
		ResumeUrl = employeeProfileData.ResumeUrl;
		LinkedInProfile = employeeProfileData.LinkedInProfile;
		Portfolio = employeeProfileData.Portfolio;
		Languages = employeeProfileData.Languages ?? string.Empty;
		Certifications = employeeProfileData.Certifications ?? string.Empty;
		PreferredWorkType = employeeProfileData.PreferredWorkType ?? "office";
		IsAvailableForTransfer = employeeProfileData.IsAvailableForTransfer ?? true;
		CreatedAt = employeeProfileData.CreatedAt ?? DateTime.MinValue;
		UpdatedAt = employeeProfileData.UpdatedAt ?? DateTime.MinValue;
	}

	public string Id { get; init; }
	public Guid UserId { get; init; }
	public string FullName { get; init; }
	public string CurrentPosition { get; init; }
	public string CurrentEmployer { get; init; }
	public string CurrentLocation { get; init; }
	public string Education { get; init; }
	public string Skills { get; init; } // Comma-separated skills
	public int YearsOfExperience { get; init; }
	public int SalaryGrade { get; init; }
	public string? ResumeUrl { get; init; }
	public string? LinkedInProfile { get; init; }
	public string? Portfolio { get; init; }
	public string Languages { get; init; } // Comma-separated languages
	public string Certifications { get; init; }
	public string PreferredWorkType { get; init; } // office, remote, hybrid
	public bool IsAvailableForTransfer { get; init; }
	public DateTime CreatedAt { get; init; }
	public DateTime UpdatedAt { get; init; }

	// Computed properties
	public IImmutableList<string> SkillsList => 
		Skills.Split(',', StringSplitOptions.RemoveEmptyEntries)
			.Select(s => s.Trim())
			.ToImmutableList();

	public IImmutableList<string> LanguagesList => 
		Languages.Split(',', StringSplitOptions.RemoveEmptyEntries)
			.Select(l => l.Trim())
			.ToImmutableList();

	public IImmutableList<string> CertificationsList => 
		Certifications.Split(',', StringSplitOptions.RemoveEmptyEntries)
			.Select(c => c.Trim())
			.ToImmutableList();

	public string ExperienceLevel => YearsOfExperience switch
	{
		< 2 => "Entry Level",
		< 5 => "Mid Level",
		< 10 => "Senior Level",
		_ => "Expert Level"
	};

	public string WorkTypeDisplayName => PreferredWorkType switch
	{
		"office" => "Office",
		"remote" => "Remote",
		"hybrid" => "Hybrid",
		_ => "Not Specified"
	};

	public bool HasLinkedIn => !string.IsNullOrEmpty(LinkedInProfile);
	public bool HasPortfolio => !string.IsNullOrEmpty(Portfolio);
	public bool HasResume => !string.IsNullOrEmpty(ResumeUrl);

	internal EmployeeProfileData ToData() => new()
	{
		Id = Id,
		UserId = UserId,
		FullName = FullName,
		CurrentPosition = CurrentPosition,
		CurrentEmployer = CurrentEmployer,
		CurrentLocation = CurrentLocation,
		Education = Education,
		Skills = Skills,
		YearsOfExperience = YearsOfExperience,
		SalaryGrade = SalaryGrade,
		ResumeUrl = ResumeUrl,
		LinkedInProfile = LinkedInProfile,
		Portfolio = Portfolio,
		Languages = Languages,
		Certifications = Certifications,
		PreferredWorkType = PreferredWorkType,
		IsAvailableForTransfer = IsAvailableForTransfer,
		CreatedAt = CreatedAt,
		UpdatedAt = UpdatedAt
	};
}
