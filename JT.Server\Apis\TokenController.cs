using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Caching.Memory;

namespace JT.Server.Apis;

/// <summary>
/// Controller for managing token-related operations such as transactions, balances, and token updates.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class TokenController : JTControllerBase
{
    private readonly string _tokenTransactionsPlansFilePath = "TokenTransactions.json";
    private readonly string _usersFilePath = "Users.json";
    private readonly string _transferRequestsFilePath = "TransferRequests.json";

    /// <summary>
    /// Logger for the TokenController.
    /// </summary>
    private readonly ILogger<TokenController> _logger;

    /// <summary>
    /// Memory cache for frequently accessed data.
    /// </summary>
    private readonly IMemoryCache _cache;

    /// <summary>
    /// Initializes a new instance of the <see cref="TokenController"/> class.
    /// </summary>
    /// <param name="logger">The logger instance for logging operations.</param>
    /// <param name="cache">The memory cache instance for caching operations.</param>
    public TokenController(ILogger<TokenController> logger, IMemoryCache cache)
    {
        _logger = logger;
        _cache = cache;
    }

    /// <summary>
    /// Retrieves the token transactions for a specific user with pagination and filtering.
    /// </summary>
    /// <param name="userId">The unique identifier of the user.</param>
    /// <param name="type">Optional filter by transaction type.</param>
    /// <param name="page">Page number (1-based).</param>
    /// <param name="pageSize">Number of items per page (max 100).</param>
    /// <param name="sortBy">Sort field (createdAt, amount, type).</param>
    /// <param name="sortOrder">Sort order (asc, desc).</param>
    /// <returns>A paginated list of token transactions for the user.</returns>
    [HttpGet("user/{userId:guid}/transactions")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(PaginatedResponse<TokenTransactionData>), 200)]
    [ProducesResponseType(404)]
    public ActionResult<PaginatedResponse<TokenTransactionData>> GetUserTokenTransactions(
        Guid userId,
        [FromQuery] string? type = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string sortBy = "createdAt",
        [FromQuery] string sortOrder = "desc")
    {
        try
        {
            _logger.LogInformation("Getting token transactions for user {UserId} with filters: type={Type}, page={Page}, pageSize={PageSize}",
                userId, type, page, pageSize);

            // Validate pagination parameters
            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 20;

            var transactions = LoadData<List<TokenTransactionData>>(_tokenTransactionsPlansFilePath);
            var userTransactions = transactions.Where(t => t.UserId == userId);

            // Apply type filter if specified
            if (!string.IsNullOrEmpty(type))
            {
                userTransactions = userTransactions.Where(t =>
                    string.Equals(t.Type, type, StringComparison.OrdinalIgnoreCase));
            }

            // Apply sorting
            userTransactions = sortBy.ToLower() switch
            {
                "amount" => sortOrder.ToLower() == "asc"
                    ? userTransactions.OrderBy(t => t.Amount)
                    : userTransactions.OrderByDescending(t => t.Amount),
                "type" => sortOrder.ToLower() == "asc"
                    ? userTransactions.OrderBy(t => t.Type)
                    : userTransactions.OrderByDescending(t => t.Type),
                _ => sortOrder.ToLower() == "asc"
                    ? userTransactions.OrderBy(t => t.CreatedAt)
                    : userTransactions.OrderByDescending(t => t.CreatedAt)
            };

            var totalCount = userTransactions.Count();
            var pagedTransactions = userTransactions
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var response = new PaginatedResponse<TokenTransactionData>
            {
                Data = pagedTransactions,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            _logger.LogInformation("Retrieved {Count} transactions for user {UserId} (page {Page} of {TotalPages})",
                pagedTransactions.Count, userId, page, response.TotalPages);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting token transactions for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves the token balance for a specific user with caching.
    /// </summary>
    /// <param name="userId">The unique identifier of the user.</param>
    /// <returns>The token balance of the user.</returns>
    [HttpGet("user/{userId:guid}/balance")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TokenBalanceResponse), 200)]
    [ProducesResponseType(404)]
    public ActionResult<TokenBalanceResponse> GetUserTokenBalance(Guid userId)
    {
        try
        {
            _logger.LogInformation("Getting token balance for user {UserId}", userId);

            var cacheKey = $"user_balance_{userId}";

            if (_cache.TryGetValue(cacheKey, out TokenBalanceResponse? cachedBalance))
            {
                _logger.LogDebug("Retrieved cached balance for user {UserId}: {Balance}", userId, cachedBalance?.Balance);
                return Ok(cachedBalance);
            }

            var users = LoadData<List<UserData>>(_usersFilePath);
            var user = users.FirstOrDefault(u => u.Id == userId);

            if (user == null)
            {
                _logger.LogWarning("User {UserId} not found", userId);
                return NotFound();
            }

            var response = new TokenBalanceResponse
            {
                UserId = userId,
                Balance = user.TokenBalance ?? 0,
                LastUpdated = DateTime.UtcNow
            };

            // Cache for 5 minutes
            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5),
                SlidingExpiration = TimeSpan.FromMinutes(2)
            };
            _cache.Set(cacheKey, response, cacheOptions);

            _logger.LogInformation("Retrieved balance for user {UserId}: {Balance}", userId, response.Balance);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting token balance for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Adds tokens to a user's account with validation and cache invalidation.
    /// </summary>
    /// <param name="userId">The unique identifier of the user.</param>
    /// <param name="request">The request containing token addition details.</param>
    /// <returns>The details of the token transaction.</returns>
    [HttpPost("user/{userId:guid}/add")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TokenTransactionData), 200)]
    [ProducesResponseType(typeof(ValidationProblemDetails), 400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<TokenTransactionData>> AddTokens(Guid userId, [FromBody] AddTokensRequest request)
    {
        try
        {
            _logger.LogInformation("Adding {Amount} tokens to user {UserId} with type {Type}",
                request.Amount, userId, request.Type);

            // Validate request
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid request for adding tokens to user {UserId}: {Errors}",
                    userId, string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return BadRequest(ModelState);
            }

            var transaction = new TokenTransactionData
            {
                Id = Guid.NewGuid().ToString(),
                UserId = userId,
                Amount = request.Amount,
                Type = request.Type,
                Description = request.Description,
                ReferenceId = request.ReferenceId,
                CreatedAt = DateTime.UtcNow
            };

            // Update user balance
            var users = LoadData<List<UserData>>(_usersFilePath);
            var userIndex = users.FindIndex(u => u.Id == userId);
            if (userIndex == -1)
            {
                _logger.LogWarning("User {UserId} not found when adding tokens", userId);
                return NotFound("User not found");
            }

            var oldBalance = users[userIndex].TokenBalance ?? 0;
            users[userIndex].TokenBalance = oldBalance + request.Amount;
            transaction.BalanceAfter = users[userIndex].TokenBalance ?? 0;
            await SaveMockData(_usersFilePath, users);

            // Invalidate cache
            var cacheKey = $"user_balance_{userId}";
            _cache.Remove(cacheKey);

            // Add transaction
            var transactions = LoadData<List<TokenTransactionData>>(_tokenTransactionsPlansFilePath);
            var updatedTransactions = transactions.Append(transaction).ToList();
            await SaveMockData(_tokenTransactionsPlansFilePath, updatedTransactions);

            _logger.LogInformation("Successfully added {Amount} tokens to user {UserId}. Balance: {OldBalance} -> {NewBalance}",
                request.Amount, userId, oldBalance, transaction.BalanceAfter);

            return Ok(transaction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding tokens for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Deducts tokens from a user's account with validation and cache invalidation.
    /// </summary>
    /// <param name="userId">The unique identifier of the user.</param>
    /// <param name="request">The request containing token deduction details.</param>
    /// <returns>The details of the token transaction.</returns>
    [HttpPost("user/{userId:guid}/deduct")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TokenTransactionData), 200)]
    [ProducesResponseType(typeof(ValidationProblemDetails), 400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<TokenTransactionData>> DeductTokens(Guid userId, [FromBody] DeductTokensRequest request)
    {
        try
        {
            _logger.LogInformation("Deducting {Amount} tokens from user {UserId} with type {Type}",
                request.Amount, userId, request.Type);

            // Validate request
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid request for deducting tokens from user {UserId}: {Errors}",
                    userId, string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
                return BadRequest(ModelState);
            }

            // Check balance first
            var users = LoadData<List<UserData>>(_usersFilePath);
            var userIndex = users.FindIndex(u => u.Id == userId);
            if (userIndex == -1)
            {
                _logger.LogWarning("User {UserId} not found when deducting tokens", userId);
                return NotFound("User not found");
            }

            var currentBalance = users[userIndex].TokenBalance ?? 0;
            if (currentBalance < request.Amount)
            {
                _logger.LogWarning("Insufficient balance for user {UserId}. Current: {CurrentBalance}, Required: {RequiredAmount}",
                    userId, currentBalance, request.Amount);
                return BadRequest($"Insufficient token balance. Current balance: {currentBalance}, Required: {request.Amount}");
            }

            var transaction = new TokenTransactionData
            {
                Id = Guid.NewGuid().ToString(),
                UserId = userId,
                Amount = -request.Amount,
                Type = request.Type,
                Description = request.Description,
                ReferenceId = request.ReferenceId,
                CreatedAt = DateTime.UtcNow
            };

            // Update user balance
            users[userIndex].TokenBalance = currentBalance - request.Amount;
            transaction.BalanceAfter = users[userIndex].TokenBalance ?? 0;
            await SaveMockData(_usersFilePath, users);

            // Invalidate cache
            var cacheKey = $"user_balance_{userId}";
            _cache.Remove(cacheKey);

            // Add transaction
            var transactions = LoadData<List<TokenTransactionData>>(_tokenTransactionsPlansFilePath);
            var updatedTransactions = transactions.Append(transaction).ToList();
            await SaveMockData(_tokenTransactionsPlansFilePath, updatedTransactions);

            _logger.LogInformation("Successfully deducted {Amount} tokens from user {UserId}. Balance: {OldBalance} -> {NewBalance}",
                request.Amount, userId, currentBalance, transaction.BalanceAfter);

            return Ok(transaction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deducting tokens for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves transactions by type with pagination and filtering.
    /// </summary>
    /// <param name="type">The transaction type to filter by.</param>
    /// <param name="page">Page number (1-based).</param>
    /// <param name="pageSize">Number of items per page (max 100).</param>
    /// <param name="sortBy">Sort field (createdAt, amount, userId).</param>
    /// <param name="sortOrder">Sort order (asc, desc).</param>
    /// <returns>A paginated list of transactions filtered by type.</returns>
    [HttpGet("transactions/type/{type}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(PaginatedResponse<TokenTransactionData>), 200)]
    public ActionResult<PaginatedResponse<TokenTransactionData>> GetTransactionsByType(
        string type,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string sortBy = "createdAt",
        [FromQuery] string sortOrder = "desc")
    {
        try
        {
            _logger.LogInformation("Getting transactions by type {Type} with pagination: page={Page}, pageSize={PageSize}",
                type, page, pageSize);

            // Validate pagination parameters
            if (page < 1) page = 1;
            if (pageSize < 1 || pageSize > 100) pageSize = 20;

            var transactions = LoadData<List<TokenTransactionData>>(_tokenTransactionsPlansFilePath);
            var filteredTransactions = transactions.Where(t =>
                string.Equals(t.Type, type, StringComparison.OrdinalIgnoreCase));

            // Apply sorting
            filteredTransactions = sortBy.ToLower() switch
            {
                "amount" => sortOrder.ToLower() == "asc"
                    ? filteredTransactions.OrderBy(t => t.Amount)
                    : filteredTransactions.OrderByDescending(t => t.Amount),
                "userid" => sortOrder.ToLower() == "asc"
                    ? filteredTransactions.OrderBy(t => t.UserId)
                    : filteredTransactions.OrderByDescending(t => t.UserId),
                _ => sortOrder.ToLower() == "asc"
                    ? filteredTransactions.OrderBy(t => t.CreatedAt)
                    : filteredTransactions.OrderByDescending(t => t.CreatedAt)
            };

            var totalCount = filteredTransactions.Count();
            var pagedTransactions = filteredTransactions
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            var response = new PaginatedResponse<TokenTransactionData>
            {
                Data = pagedTransactions,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
            };

            _logger.LogInformation("Retrieved {Count} transactions of type {Type} (page {Page} of {TotalPages})",
                pagedTransactions.Count, type, page, response.TotalPages);

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transactions by type {Type}", type);
            return StatusCode(500, "Internal server error");
        }
    }
}

/// <summary>
/// Request model for adding tokens to a user's account.
/// </summary>
public class AddTokensRequest
{
    /// <summary>
    /// The amount of tokens to add.
    /// </summary>
    [Required]
    [Range(1, 10000, ErrorMessage = "Amount must be between 1 and 10,000 tokens")]
    public int Amount { get; set; }

    /// <summary>
    /// The type of the token transaction.
    /// </summary>
    [Required]
    [StringLength(50, ErrorMessage = "Type must not exceed 50 characters")]
    [RegularExpression(@"^[a-zA-Z_]+$", ErrorMessage = "Type can only contain letters and underscores")]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// A description of the token transaction.
    /// </summary>
    [Required]
    [StringLength(500, ErrorMessage = "Description must not exceed 500 characters")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// An optional reference ID for the token transaction.
    /// </summary>
    [StringLength(100, ErrorMessage = "Reference ID must not exceed 100 characters")]
    public string? ReferenceId { get; set; }
}

/// <summary>
/// Request model for deducting tokens from a user's account.
/// </summary>
public class DeductTokensRequest
{
    /// <summary>
    /// The amount of tokens to deduct.
    /// </summary>
    [Required]
    [Range(1, 10000, ErrorMessage = "Amount must be between 1 and 10,000 tokens")]
    public int Amount { get; set; }

    /// <summary>
    /// The type of the token transaction.
    /// </summary>
    [Required]
    [StringLength(50, ErrorMessage = "Type must not exceed 50 characters")]
    [RegularExpression(@"^[a-zA-Z_]+$", ErrorMessage = "Type can only contain letters and underscores")]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// A description of the token transaction.
    /// </summary>
    [Required]
    [StringLength(500, ErrorMessage = "Description must not exceed 500 characters")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// An optional reference ID for the token transaction.
    /// </summary>
    [StringLength(100, ErrorMessage = "Reference ID must not exceed 100 characters")]
    public string? ReferenceId { get; set; }
}

/// <summary>
/// Response model for paginated data.
/// </summary>
/// <typeparam name="T">The type of data being paginated.</typeparam>
public class PaginatedResponse<T>
{
    /// <summary>
    /// The data items for the current page.
    /// </summary>
    public List<T> Data { get; set; } = new();

    /// <summary>
    /// The current page number (1-based).
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// The number of items per page.
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// The total number of items across all pages.
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// The total number of pages.
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// Indicates if there is a previous page.
    /// </summary>
    public bool HasPreviousPage => Page > 1;

    /// <summary>
    /// Indicates if there is a next page.
    /// </summary>
    public bool HasNextPage => Page < TotalPages;
}

/// <summary>
/// Response model for token balance information.
/// </summary>
public class TokenBalanceResponse
{
    /// <summary>
    /// The user ID.
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// The current token balance.
    /// </summary>
    public int Balance { get; set; }

    /// <summary>
    /// When the balance was last updated.
    /// </summary>
    public DateTime LastUpdated { get; set; }
}
