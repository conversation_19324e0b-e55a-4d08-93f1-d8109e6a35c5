﻿<Page x:Class="JT.Presentation.LiveJobTransferingPage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:local="using:JT.Presentation"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
	  xmlns:not_mobile="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:not_win="http://uno.ui/not_win"
	  xmlns:skia="http://uno.ui/skia#using:Uno.Toolkit.UI"
	  xmlns:not_skia="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:toolkit="using:Uno.UI.Toolkit"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:uer="using:Uno.Extensions.Reactive.UI"
	  xmlns:converters="using:JT.Converters"
	  xmlns:ut="using:Uno.Themes"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  xmlns:win="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  utu:StatusBar.Background="{ThemeResource SurfaceInverseBrush}"
	  utu:StatusBar.Foreground="AutoInverse"
	  Background="{ThemeResource BackgroundBrush}"
	  mc:Ignorable="d not_win skia">

	<Page.Resources>

		<converters:StringToMediaPlayBackSourceConverter x:Key="StringToMediaPlaybackSource" />

		<DataTemplate x:Key="StepTemplate">
			<!-- Workaround: Removed the ScrollViewer that was previously wrapping the content here: https://github.com/unoplatform/uno/issues/19917 -->
			<utu:AutoLayout Spacing="24"
							PrimaryAxisAlignment="Start">
				<TextBlock FontSize="22"
						   FontWeight="SemiBold"
						   Foreground="{ThemeResource OnSurfaceBrush}"
						   Style="{StaticResource TitleLarge}"
						   TextAlignment="Center"
						   TextWrapping="Wrap"
						   utu:AutoLayout.CounterAlignment="Center">
					<Run Text="{Binding Number}" />
					<Run Text=" - " />
					<Run Text="{Binding Name}" />
				</TextBlock>
				<utu:AutoLayout Padding="16"
								Background="{ThemeResource BackgroundBrush}"
								CornerRadius="8"
								Spacing="16"
								CounterAxisAlignment="Center">
					<TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
							   Style="{StaticResource CaptionLarge}"
							   Text="Ingredients:"
							   TextAlignment="Center"
							   TextWrapping="Wrap" />
					<muxc:ItemsRepeater ItemsSource="{Binding Ingredients}"
										utu:AutoLayout.PrimaryAlignment="Stretch">
						<muxc:ItemsRepeater.Layout>
							<win:LinedFlowLayout ItemsJustification="Center"
												 MinItemSpacing="16" />

							<not_win:FlowLayout LineAlignment="Center"
												MinColumnSpacing="16"
												MinRowSpacing="12" />
						</muxc:ItemsRepeater.Layout>
						<muxc:ItemsRepeater.ItemTemplate>
							<DataTemplate>
								<utu:AutoLayout Spacing="4"
												PrimaryAxisAlignment="Center"
												CounterAxisAlignment="Center"
												Orientation="Horizontal">
									<PathIcon Data="{StaticResource Icon_Check_Circle}"
											  Foreground="{ThemeResource OnSurfaceMediumBrush}" />
									<TextBlock Text="{Binding}"
											   TextWrapping="NoWrap"
											   Foreground="{ThemeResource OnSurfaceBrush}" />
								</utu:AutoLayout>
							</DataTemplate>
						</muxc:ItemsRepeater.ItemTemplate>
					</muxc:ItemsRepeater>
				</utu:AutoLayout>
				<TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
						   Style="{StaticResource TitleMedium}"
						   Text="{Binding Description}"
						   TextAlignment="Center"
						   TextWrapping="Wrap" />
			</utu:AutoLayout>
		</DataTemplate>
	</Page.Resources>

	<utu:AutoLayout x:Name="MainLayout">
		<utu:NavigationBar x:Name="NavBar"
						   utu:AutoLayout.PrimaryAlignment="Auto"
						   Style="{StaticResource JTsNavigationBarStyle}"
						   Content="{Binding TransferRequest.Name}" />

		<utu:AutoLayout utu:AutoLayout.PrimaryAlignment="Stretch"
						Orientation="{utu:Responsive Normal=Vertical,
													 Wide=Horizontal}"
						Background="{ThemeResource SurfaceBrush}"
						Visibility="{Binding Completed, Converter={StaticResource TrueToCollapsed}}">
			<utu:AutoLayout Background="{ThemeResource BrandBlackBrush}"
							utu:AutoLayout.PrimaryAlignment="{utu:Responsive Normal=Auto,
																			 Wide=Stretch}"
							PrimaryAxisAlignment="{utu:Responsive Normal=Start,
																  Wide=Center}">
				<MediaPlayerElement AreTransportControlsEnabled="True"
									AutoPlay="True"
									Source="ms-appx:///Assets/Videos/CookingVideo.mp4">
					<MediaPlayerElement.TransportControls>
						<MediaTransportControls IsCompact="True" />
					</MediaPlayerElement.TransportControls>
				</MediaPlayerElement>

			</utu:AutoLayout>

			<!--
				WORKAROUND
				Binding to the Steps.Value property instead of directly on the Steps entity until
				https://github.com/unoplatform/uno.extensions/issues/2391 is fixed
			-->
			<utu:AutoLayout utu:AutoLayout.PrimaryAlignment="Stretch"
							Justify="SpaceBetween"
							Padding="{utu:Responsive Normal=18,
													 Wide=40}">
				<FlipView x:Name="StepsFlipView"
						  utu:SelectorExtensions.PipsPager="{Binding ElementName=pipsPager}"
						  Background="Transparent"
						  utu:AutoLayout.PrimaryAlignment="Stretch"
						  ItemTemplate="{StaticResource StepTemplate}"
						  ItemsSource="{Binding Steps.Value.Items}"
						  SelectedIndex="{Binding Steps.CurrentIndex, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
				<utu:AutoLayout CornerRadius="8"
								PrimaryAxisAlignment="Stretch"
								Padding="40,0">
					<muxc:PipsPager x:Name="pipsPager"
									Margin="0,0,0,16"
									utu:AutoLayout.PrimaryAlignment="Auto"
									utu:AutoLayout.CounterAlignment="Center"
									Style="{StaticResource PipsPagerStyle}" />
					<utu:AutoLayout Height="59"
									Orientation="Horizontal"
									PrimaryAxisAlignment="Stretch"
									CounterAxisAlignment="End"
									utu:AutoLayout.PrimaryAlignment="Stretch"
									utu:AutoLayout.CounterAlignment="Stretch"
									Spacing="16">
						<Button Content="Back"
								utu:AutoLayout.PrimaryAlignment="Stretch"
								utu:FlipViewExtensions.Previous="{Binding ElementName=StepsFlipView}"
								Style="{StaticResource JTsSecondaryButtonStyle}"
								Visibility="{Binding Steps.Value.CanMovePrevious, FallbackValue=Collapsed, TargetNullValue=Collapsed}" />
						<Button Content="Next"
								utu:AutoLayout.PrimaryAlignment="Stretch"
								utu:FlipViewExtensions.Next="{Binding ElementName=StepsFlipView}"
								Visibility="{Binding Steps.Value.CanMoveNext}"
								Style="{StaticResource JTsPrimaryButtonStyle}" />
						<Button Command="{Binding Complete}"
								Content="Done!"
								utu:AutoLayout.PrimaryAlignment="Stretch"
								Visibility="{Binding Steps.Value.CanMoveNext, Converter={StaticResource InvertBool}, FallbackValue=Collapsed, TargetNullValue=Collapsed}"
								Style="{StaticResource JTsPrimaryButtonStyle}" />
					</utu:AutoLayout>
				</utu:AutoLayout>
			</utu:AutoLayout>
		</utu:AutoLayout>

		<utu:AutoLayout utu:AutoLayout.PrimaryAlignment="Stretch"
						Background="{ThemeResource SurfaceBrush}"
						Visibility="{Binding Completed}"
						Orientation="{utu:Responsive Normal=Vertical,
													 Wide=Horizontal}">
			<Image Source="{Binding Transfer.ImageUrl}"
				   Stretch="UniformToFill"
				   Visibility="{utu:Responsive Normal=Collapsed,
											   Wide=Visible}"
				   utu:AutoLayout.PrimaryAlignment="Stretch" />
			<utu:AutoLayout PrimaryAxisAlignment="Center"
							PrimaryAlignment="Stretch">
				<utu:AutoLayout PrimaryAxisAlignment="Center"
								CounterAxisAlignment="Center"
								Margin="{utu:Responsive Normal=32,
														Wide=40}">
					<StackPanel Padding="{utu:Responsive Normal=0,
														 Wide=48}"
								Spacing="24">
                        <BitmapIcon UriSource="{ThemeResource LiveTransfering_Success}"
									Width="72"
									Height="72" />
						<TextBlock FontSize="22"
								   FontWeight="SemiBold"
								   Foreground="{ThemeResource OnSurfaceBrush}"
								   Style="{StaticResource TitleLarge}"
								   Text="Hurray!"
								   TextAlignment="Center"
								   HorizontalAlignment="Center" />
						<TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
								   Style="{StaticResource TitleMedium}"
								   Text="All steps are completed. Now it’s time to enjoy your meal!"
								   TextAlignment="Center"
								   TextWrapping="Wrap"
								   HorizontalAlignment="Center" />
						<utu:AutoLayout Padding="16,32"
										Background="{ThemeResource BackgroundBrush}"
										CornerRadius="8"
										Spacing="16">
							<TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
									   Text="Tell us how was your cooking experience!"
									   TextAlignment="Center"
									   TextWrapping="Wrap" />
							<!-- TODO -->
							<!-- Save rating -->
							<muxc:RatingControl utu:AutoLayout.CounterAlignment="Center"
												MaxRating="5"
												Style="{StaticResource RatingControlStyle}" />
						</utu:AutoLayout>
					</StackPanel>
				</utu:AutoLayout>
				<utu:AutoLayout utu:AutoLayout.PrimaryAlignment="Stretch"
								CounterAxisAlignment="End"
								PrimaryAxisAlignment="Stretch"
								Orientation="Horizontal"
								Spacing="16"
								Margin="{utu:Responsive Normal=0,
														Wide=48}">
					<Button utu:AutoLayout.PrimaryAlignment="Stretch"
							Command="{Binding BackToLastStep}"
							Content="Previous"
							Style="{StaticResource JTsSecondaryButtonStyle}" />
					<Button utu:AutoLayout.PrimaryAlignment="Stretch"
							Height="59"
							Command="{Binding Favorite}"
							Content="Favorite"
							CornerRadius="4">
						<ut:ControlExtensions.Icon>
							<PathIcon Data="{StaticResource Icon_Heart}" />
						</ut:ControlExtensions.Icon>
					</Button>
				</utu:AutoLayout>
			</utu:AutoLayout>
		</utu:AutoLayout>
	</utu:AutoLayout>
</Page>
