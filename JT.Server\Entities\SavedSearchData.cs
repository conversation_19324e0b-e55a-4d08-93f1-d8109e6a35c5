namespace JT.Server.Entities;

public class SavedSearchData
{
	public string? Id { get; set; }
	public Guid? UserId { get; set; }
	public string? Name { get; set; }
	public SearchCriteriaData? SearchCriteria { get; set; }
	public bool? AlertsEnabled { get; set; }
	public DateTime? CreatedAt { get; set; }
	public DateTime? LastUsed { get; set; }
	public int? ResultCount { get; set; }
}

public class SearchCriteriaData
{
	public string? Keywords { get; set; }
	public string? Location { get; set; }
	public string? Industry { get; set; }
	public SalaryRangeData? SalaryRange { get; set; }
	public string? ExperienceLevel { get; set; }
	public bool? RemoteWork { get; set; }
}

public class SalaryRangeData
{
	public decimal? Min { get; set; }
	public decimal? Max { get; set; }
	public string? Currency { get; set; }
}
