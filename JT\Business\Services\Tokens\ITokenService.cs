namespace JT.Business.Services.Tokens;

public interface ITokenService
{
	ValueTask<IImmutableList<TokenTransaction>> GetUserTransactions(Guid userId, CancellationToken ct = default);
	ValueTask<int> GetUserBalance(Guid userId, CancellationToken ct = default);
	ValueTask<TokenTransaction> AddTokens(Guid userId, int amount, string type, string description, string? referenceId = null, CancellationToken ct = default);
	ValueTask<TokenTransaction> DeductTokens(Guid userId, int amount, string type, string description, string? referenceId = null, CancellationToken ct = default);
	ValueTask<bool> HasSufficientBalance(Guid userId, int requiredAmount, CancellationToken ct = default);
	ValueTask<TokenTransaction> ProcessReferralBonus(Guid referrerId, Guid referredUserId, CancellationToken ct = default);
	ValueTask<TokenTransaction> ProcessWelcomeBonus(Guid userId, CancellationToken ct = default);
	ValueTask<TokenTransaction> ProcessPurchaseBonus(Guid userId, string subscriptionTier, CancellationToken ct = default);
	ValueTask<IImmutableList<TokenTransaction>> GetTransactionsByType(string type, CancellationToken ct = default);
}
