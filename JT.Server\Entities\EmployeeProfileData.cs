namespace JT.Server.Entities;

public class EmployeeProfileData
{
	public string? Id { get; set; }
	public Guid? UserId { get; set; }
	public string? FullName { get; set; }
	public string? CurrentPosition { get; set; }
	public string? CurrentEmployer { get; set; }
	public string? CurrentLocation { get; set; }
	public string? Education { get; set; }
	public string? Skills { get; set; } // Comma-separated skills
	public int? YearsOfExperience { get; set; }
	public int? SalaryGrade { get; set; }
	public string? ResumeUrl { get; set; }
	public string? LinkedInProfile { get; set; }
	public string? Portfolio { get; set; }
	public string? Languages { get; set; } // Comma-separated languages
	public string? Certifications { get; set; }
	public string? PreferredWorkType { get; set; } = "office"; // office, remote, hybrid
	public bool? IsAvailableForTransfer { get; set; } = true;
	public DateTime? CreatedAt { get; set; }
	public DateTime? UpdatedAt { get; set; }
}
