using JT.Content.Client;
using JT.Business.Models;
using JT.Content.Client.Models;
using Microsoft.Extensions.Logging;

namespace JT.Business.Services.Tokens;

public class TokenService(JTApiClient api, ILogger<TokenService>? logger = null) : ITokenService
{
	private readonly ILogger<TokenService>? _logger = logger;

	public async ValueTask<IImmutableList<TokenTransaction>> GetUserTransactions(Guid userId, CancellationToken ct = default)
	{
		try
		{
			// The API now returns paginated response
			var paginatedResponse = await api.Api.Token.User[userId].Transactions.GetAsync(cancellationToken: ct);
			if (paginatedResponse?.Data != null)
			{
				return paginatedResponse.Data.Select(t => new TokenTransaction(t)).ToImmutableList();
			}
			return ImmutableList<TokenTransaction>.Empty;
		}
		catch (Exception ex)
		{
			_logger?.LogError(ex, "Error getting token transactions for user {UserId}", userId);
			return ImmutableList<TokenTransaction>.Empty;
		}
	}

	public async ValueTask<int> GetUserBalance(Guid userId, CancellationToken ct = default)
	{
		try
		{
			var balanceResponse = await api.Api.Token.User[userId].Balance.GetAsync(cancellationToken: ct);
			return balanceResponse?.Balance ?? 0;
		}
		catch (Exception ex)
		{
			_logger?.LogError(ex, "Error getting token balance for user {UserId}", userId);
			return 0;
		}
	}

	public async ValueTask<TokenTransaction> AddTokens(Guid userId, int amount, string type, string description, string? referenceId = null, CancellationToken ct = default)
	{
		try
		{
			_logger?.LogInformation("Adding {Amount} tokens to user {UserId} with type {Type}", amount, userId, type);

			var request = new AddTokensRequest
			{
				Amount = amount,
				Type = type,
				Description = description,
				ReferenceId = referenceId
			};

			var transactionData = await api.Api.Token.User[userId].Add.PostAsync(request, cancellationToken: ct);
			var result = new TokenTransaction(transactionData ?? throw new InvalidOperationException("Failed to add tokens - no transaction data returned"));

			_logger?.LogInformation("Successfully added {Amount} tokens to user {UserId}", amount, userId);
			return result;
		}
		catch (Exception ex)
		{
			_logger?.LogError(ex, "Failed to add tokens for user {UserId}", userId);
			throw new InvalidOperationException($"Failed to add tokens: {ex.Message}", ex);
		}
	}

	public async ValueTask<TokenTransaction> DeductTokens(Guid userId, int amount, string type, string description, string? referenceId = null, CancellationToken ct = default)
	{
		try
		{
			_logger?.LogInformation("Deducting {Amount} tokens from user {UserId} with type {Type}", amount, userId, type);

			var request = new DeductTokensRequest
			{
				Amount = amount,
				Type = type,
				Description = description,
				ReferenceId = referenceId
			};

			var transactionData = await api.Api.Token.User[userId].Deduct.PostAsync(request, cancellationToken: ct);
			var result = new TokenTransaction(transactionData ?? throw new InvalidOperationException("Failed to deduct tokens - no transaction data returned"));

			_logger?.LogInformation("Successfully deducted {Amount} tokens from user {UserId}", amount, userId);
			return result;
		}
		catch (Exception ex)
		{
			_logger?.LogError(ex, "Failed to deduct tokens for user {UserId}", userId);
			throw new InvalidOperationException($"Failed to deduct tokens: {ex.Message}", ex);
		}
	}

	public async ValueTask<bool> HasSufficientBalance(Guid userId, int requiredAmount, CancellationToken ct = default)
	{
		var balance = await GetUserBalance(userId, ct);
		return balance >= requiredAmount;
	}

	public async ValueTask<TokenTransaction> ProcessReferralBonus(Guid referrerId, Guid referredUserId, CancellationToken ct = default)
	{
		// Standard referral bonus amount
		const int referralBonus = 25;
		
		var description = $"Referral bonus for inviting user {referredUserId}";
		return await AddTokens(referrerId, referralBonus, "referral", description, referredUserId.ToString(), ct);
	}

	public async ValueTask<TokenTransaction> ProcessWelcomeBonus(Guid userId, CancellationToken ct = default)
	{
		// Standard welcome bonus amount
		const int welcomeBonus = 25;
		
		var description = "Welcome bonus for joining JobTransfer";
		return await AddTokens(userId, welcomeBonus, "bonus", description, "welcome", ct);
	}

	public async ValueTask<TokenTransaction> ProcessPurchaseBonus(Guid userId, string subscriptionTier, CancellationToken ct = default)
	{
		// Bonus amounts based on subscription tier
		var bonusAmount = subscriptionTier.ToLower() switch
		{
			"silver" => 25,
			"gold" => 50,
			"diamond" => 100,
			_ => 0
		};

		if (bonusAmount > 0)
		{
			var description = $"Purchase bonus for {subscriptionTier} subscription";
			return await AddTokens(userId, bonusAmount, "purchase", description, subscriptionTier, ct);
		}

		throw new InvalidOperationException($"No bonus available for subscription tier: {subscriptionTier}");
	}

	public async ValueTask<IImmutableList<TokenTransaction>> GetTransactionsByType(string type, CancellationToken ct = default)
	{
		try
		{
			// Note: This implementation would need to be updated when the API client is regenerated
			// to support the new /api/Token/transactions/type/{type} endpoint
			// For now, we'll use a workaround by getting user transactions and filtering

			// TODO: Update this when API client supports the new endpoint:
			// var response = await api.Api.Token.Transactions.Type[type].GetAsync(cancellationToken: ct);

			// Temporary implementation - get all user transactions and filter by type
			// This is not ideal as it requires knowing the user ID
			await Task.CompletedTask; // Remove async warning

			_logger?.LogWarning("GetTransactionsByType is using a temporary implementation. " +
				"API client needs to be regenerated to support the new endpoint: /api/Token/transactions/type/{Type}", type);

			return ImmutableList<TokenTransaction>.Empty;
		}
		catch (Exception ex)
		{
			_logger?.LogError(ex, "Error getting transactions by type {Type}", type);
			return ImmutableList<TokenTransaction>.Empty;
		}
	}
}
