using System.Text.Json.Serialization.Metadata;
using Microsoft.OpenApi.Models;

//using JT.DataContracts.Serialization;
using Uno.Wasm.Bootstrap.Server;

try
{
    var builder = WebApplication.CreateBuilder(args);

    // Add services to the container.
    builder.Services.AddMemoryCache();

    builder.Services.AddControllers()
        .AddJsonOptions(o => o.JsonSerializerOptions.TypeInfoResolver = JTContext.Default);

    // Configure the JsonOptions to use the generated JTContext
    //builder.Services.Configure<JsonOptions>(options =>
    //    options.JsonSerializerOptions.TypeInfoResolver = JsonTypeInfoResolver.Combine(
    //        JTContext.Default
    //    ));
    // Configure the RouteOptions to use lowercase URLs
    //builder.Services.Configure<RouteOptions>(options =>
    //    options.LowercaseUrls = true);

    // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
    builder.Services.AddEndpointsApiExplorer();
    //builder.Services.AddSwaggerGen(c =>
    //{
    //    // Include XML comments for all included assemblies
    //    Directory.EnumerateFiles(AppContext.BaseDirectory, "*.xml")
    //        .Where(x => x.Contains("JT")
    //            && File.Exists(Path.Combine(
    //                AppContext.BaseDirectory,
    //                $"{Path.GetFileNameWithoutExtension(x)}.dll")))
    //        .ToList()
    //        .ForEach(path => c.IncludeXmlComments(path));
    //});
    builder.Services.AddSwaggerGen(c =>
    {
        //c.MapType<TimeSpan>(() => new OpenApiSchema { Type = "string", Format = "duration" }); // if use TimeSpanStringConverter
        c.SwaggerDoc("v1", new OpenApiInfo
        {
            Title = "JobTransfer API",
            Version = "v1",
            Description = "API for Omani Job Transfer Application with subscription tiers, token system, and payment processing"
        });

        // Add this line to specify the server URL in the OpenAPI document
        c.AddServer(new Microsoft.OpenApi.Models.OpenApiServer
        {
            Url = "https://localhost:5002",
            Description = "Local development server"
        });
    });

    var app = builder.Build();

    // Configure the HTTP request pipeline.
    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        //app.UseSwaggerUI();
        app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "JT API V1"));

    }

    app.UseHttpsRedirection();

    //
    app.UseAuthorization();
    //
    app.MapControllers();

    //app.UseUnoFrameworkFiles();
    //app.MapFallbackToFile("index.html");

    //app.MapWeatherApi();
    //app.UseStaticFiles();

    await app.RunAsync();
}
catch (Exception ex)
{
    Console.Error.WriteLine("Application terminated unexpectedly");
    Console.Error.WriteLine(ex);
#if DEBUG
    if (System.Diagnostics.Debugger.IsAttached)
    {
        System.Diagnostics.Debugger.Break();
    }
#endif
}
