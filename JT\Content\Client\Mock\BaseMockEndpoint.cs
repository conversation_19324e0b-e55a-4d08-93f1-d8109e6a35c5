namespace JT.Content.Client.Mock;

public abstract class BaseMockEndpoint(ISerializer serializer, ILogger<BaseMockEndpoint> _logger)
{
	protected async Task<T?> LoadData<T>(string fileName)
	{
		try
		{
			var file = await StorageFile.GetFileFromApplicationUriAsync(new Uri($"ms-appx:///AppData/{fileName}"));
			var json = await FileIO.ReadTextAsync(file);
			System.Diagnostics.Debug.WriteLine($"JSON string before deserialization: {json}");
            var result = serializer.FromString<T>(json);
            System.Diagnostics.Debug.WriteLine($"Deserialization result: {result}");
			return result;
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Failed to load {FileName}", fileName);
			return default;
		}
	}
}
