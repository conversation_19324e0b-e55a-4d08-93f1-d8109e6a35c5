{
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:8080",
      "sslPort": 0
    }
  },
  "profiles": {
    // This profile is first in order for dotnet run to pick it up by default
    "JT (WebAssembly)": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "applicationUrl": "http://localhost:5000",
      "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    "JT (WebAssembly IIS Express)": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    // Note: In order to select this profile, you'll need to comment the `Packaged` profile below until this is fixed: https://aka.platform.uno/wasdk-maui-debug-profile-issue
    "JT (WinAppSDK Unpackaged)": {
      "commandName": "Project",
      "compatibleTargetFramework": "windows"
    },
    "JT (WinAppSDK Packaged)": {
      "commandName": "MsixPackage",
      "compatibleTargetFramework": "windows"
    },
    "JT (Desktop)": {
      "commandName": "Project",
      "compatibleTargetFramework": "desktop"
    },
    "JT (Desktop WSL2)": {
      "commandName": "WSL2",
      "commandLineArgs": "{ProjectDir}/bin/Debug/net9.0-desktop/JT.dll",
      "distributionName": "",
      "compatibleTargetFramework": "desktop"
    }
  }
}
