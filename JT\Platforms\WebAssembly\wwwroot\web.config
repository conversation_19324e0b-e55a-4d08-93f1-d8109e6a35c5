<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.web>
    <customErrors mode="Off"/>
  </system.web>

  <system.webServer>

    <!-- Disable compression as we're doing it through pre-compressed files -->
    <urlCompression doStaticCompression="false" doDynamicCompression="false" dynamicCompressionBeforeCache="false" />

    <staticContent>
      <remove fileExtension=".dll" />
      <remove fileExtension=".wasm" />
      <remove fileExtension=".woff" />
      <remove fileExtension=".woff2" />
      <mimeMap fileExtension=".wasm" mimeType="application/wasm" />
      <mimeMap fileExtension=".clr" mimeType="application/octet-stream" />
      <mimeMap fileExtension=".pdb" mimeType="application/octet-stream" />
      <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
      <mimeMap fileExtension=".woff2" mimeType="application/font-woff" />
      <mimeMap fileExtension=".dat" mimeType="application/octet-stream" />
      <!-- Required for PWAs -->
      <mimeMap fileExtension=".json" mimeType="application/octet-stream" />
    </staticContent>

    <rewrite>
      <rules>
        <rule name="Lookup for pre-compressed brotli file" stopProcessing="true">
          <match url="(.*)$"/>
          <conditions>
            <!-- Match brotli requests -->
            <add input="{HTTP_ACCEPT_ENCODING}" pattern="br" />
            
            <!-- Match all but pre-compressed files -->
            <add input="{REQUEST_URI}" pattern="^(?!/_compressed_br/)(.*)$" />

            <!-- Check if the pre-compressed file exists on the disk -->
            <add input="{DOCUMENT_ROOT}/_compressed_br/{C:0}" matchType="IsFile" negate="false" />
          </conditions>
          <action type="Rewrite" url="/_compressed_br{C:0}" />
        </rule>

        <rule name="Lookup for pre-compressed gzip file" stopProcessing="true">
          <match url="(.*)$"/>
          <conditions>
            <!-- Match gzip requests -->
            <add input="{HTTP_ACCEPT_ENCODING}" pattern="gzip" />
            
            <!-- Match all but pre-compressed files -->
            <add input="{REQUEST_URI}" pattern="^(?!/_compressed_gz/)(.*)$" />
            
            <!-- Check if the pre-compressed file exists on the disk -->
            <add input="{DOCUMENT_ROOT}/_compressed_gz/{C:0}" matchType="IsFile" negate="false" />
          </conditions>
          <action type="Rewrite" url="/_compressed_gz{C:0}" />
        </rule>
      </rules>

      <outboundRules>
        <rule name="Adjust content encoding for gzip pre-compressed files" enabled="true" stopProcessing="true">
          <match serverVariable="RESPONSE_CONTENT_ENCODING" pattern="" />
          <conditions>
            <add input="{REQUEST_URI}" pattern="/_compressed_gz/.*$" />
          </conditions>
          <action type="Rewrite" value="gzip"/>
        </rule>
        <rule name="Adjust content encoding for brotli pre-compressed files" enabled="true" stopProcessing="true">
          <match serverVariable="RESPONSE_CONTENT_ENCODING" pattern="" />
          <conditions>
            <add input="{REQUEST_URI}" pattern="/_compressed_br/.*$" />
          </conditions>
          <action type="Rewrite" value="br"/>
        </rule>
      </outboundRules>
    </rewrite>
  </system.webServer>
</configuration>
