using JT.Server.Types;

namespace JT.Server.Entities;

/// <summary>
/// Represents a single step in a Transfer or process with associated data such as ingredients, Transferware, and instructions.
/// </summary>
public class StepData
{
	/// <summary>
	/// Gets or sets the URL to a video demonstrating this step.
	/// </summary>
	public string? UrlVideo { get; set; }

	/// <summary>
	/// Gets or sets the name or title of this step.
	/// </summary>
	public string? Name { get; set; }

	/// <summary>
	/// Gets or sets the sequential number of this step in the process.
	/// </summary>
	public int Number { get; set; }

    /// <summary>
    /// Gets or sets the time required to Transfer this step.
    /// </summary>
    //[JsonConverter(typeof(TimeSpanObjectConverter))]
    public TimeSpanObject TransferTime { get; set; }
    //public TimeSpan TransferTime { get; set; }

    /// <summary>
    /// Gets or sets the list of Transferware or tools required for this step.
    /// </summary>
    public List<string>? Transferware { get; set; }

	/// <summary>
	/// Gets or sets the list of ingredients used in this step.
	/// </summary>
	public List<string>? Ingredients { get; set; }

	/// <summary>
	/// Gets or sets the detailed description or instructions for this step.
	/// </summary>
	public string? Description { get; set; }
}
