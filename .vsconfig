{"version": "1.0", "components": ["Microsoft.VisualStudio.Component.CoreEditor", "Microsoft.VisualStudio.Workload.CoreEditor", "Microsoft.NetCore.Component.SDK", "Microsoft.NetCore.Component.DevelopmentTools", "Microsoft.Net.ComponentGroup.DevelopmentPrerequisites", "Microsoft.VisualStudio.Component.TextTemplating", "Microsoft.VisualStudio.ComponentGroup.WebToolsExtensions", "Microsoft.NetCore.Component.Web", "Microsoft.VisualStudio.Component.IISExpress", "Component.Microsoft.Web.LibraryManager", "Microsoft.VisualStudio.ComponentGroup.Web", "Microsoft.VisualStudio.Component.Web", "Microsoft.VisualStudio.ComponentGroup.Web.Client", "Microsoft.VisualStudio.Workload.NetWeb", "Microsoft.VisualStudio.ComponentGroup.WebToolsExtensions.TemplateEngine", "Microsoft.VisualStudio.ComponentGroup.MSIX.Packaging", "Microsoft.VisualStudio.Component.ManagedDesktop.Prerequisites", "Microsoft.VisualStudio.Component.Debugger.JustInTime", "Microsoft.VisualStudio.Workload.ManagedDesktop", "Component.Xamarin.RemotedSimulator", "Microsoft.VisualStudio.Component.MonoDebugger", "Microsoft.VisualStudio.ComponentGroup.Maui.All", "Component.Android.SDK34", "Component.OpenJDK", "Microsoft.VisualStudio.Workload.NetCrossPlat", "Microsoft.VisualStudio.Workload.NetCoreTools"]}