using BusinessAdRequestData = JT.Client.Models.BusinessAdRequestData;

namespace JT.Business.Models;

public partial record BusinessAdRequest
{
	internal BusinessAdRequest(BusinessAdRequestData businessAdRequestData)
	{
		Id = businessAdRequestData.Id ?? string.Empty;
		UserId = businessAdRequestData.UserId ?? Guid.Empty;
		CompanyName = businessAdRequestData.CompanyName ?? string.Empty;
		Tagline = businessAdRequestData.Tagline ?? string.Empty;
		Description = businessAdRequestData.Description ?? string.Empty;
		WebsiteUrl = businessAdRequestData.WebsiteUrl ?? string.Empty;
		Email = businessAdRequestData.Email ?? string.Empty;
		PhoneNumber = businessAdRequestData.PhoneNumber ?? string.Empty;
		Category = businessAdRequestData.Category ?? string.Empty;
		BudgetInOMR = businessAdRequestData.BudgetInOMR ?? 0;
		DurationInDays = businessAdRequestData.DurationInDays ?? 0;
		TargetAudience = businessAdRequestData.TargetAudience ?? string.Empty;
		Status = businessAdRequestData.Status ?? "pending";
		CreatedAt = businessAdRequestData.CreatedAt ?? DateTime.MinValue;
		ApprovedAt = businessAdRequestData.ApprovedAt;
		StartDate = businessAdRequestData.StartDate;
		EndDate = businessAdRequestData.EndDate;
		ApprovedBy = businessAdRequestData.ApprovedBy;
		RejectionReason = businessAdRequestData.RejectionReason;
		ViewCount = businessAdRequestData.ViewCount ?? 0;
		ClickCount = businessAdRequestData.ClickCount ?? 0;
	}

	public string Id { get; init; }
	public Guid UserId { get; init; }
	public string CompanyName { get; init; }
	public string Tagline { get; init; }
	public string Description { get; init; }
	public string WebsiteUrl { get; init; }
	public string Email { get; init; }
	public string PhoneNumber { get; init; }
	public string Category { get; init; }
	public decimal BudgetInOMR { get; init; }
	public int DurationInDays { get; init; }
	public string TargetAudience { get; init; }
	public string Status { get; init; } // pending, approved, rejected, active, expired
	public DateTime CreatedAt { get; init; }
	public DateTime? ApprovedAt { get; init; }
	public DateTime? StartDate { get; init; }
	public DateTime? EndDate { get; init; }
	public string? ApprovedBy { get; init; }
	public string? RejectionReason { get; init; }
	public int ViewCount { get; init; }
	public int ClickCount { get; init; }

	// Computed properties
	public string StatusColor => Status switch
	{
		"approved" => "#28A745",
		"active" => "#007BFF",
		"pending" => "#FFC107",
		"rejected" => "#DC3545",
		"expired" => "#6C757D",
		_ => "#6C757D"
	};

	public string StatusDisplayName => Status switch
	{
		"pending" => "Pending Review",
		"approved" => "Approved",
		"rejected" => "Rejected",
		"active" => "Active",
		"expired" => "Expired",
		_ => Status
	};

	public bool IsActive => Status == "active" && EndDate.HasValue && DateTime.UtcNow <= EndDate.Value;
	public bool IsExpired => EndDate.HasValue && DateTime.UtcNow > EndDate.Value;
	public bool IsPending => Status == "pending";
	public bool IsApproved => Status == "approved";
	public bool IsRejected => Status == "rejected";

	public int DaysRemaining => EndDate.HasValue ? Math.Max(0, (int)(EndDate.Value - DateTime.UtcNow).TotalDays) : 0;
	public decimal ClickThroughRate => ViewCount > 0 ? (decimal)ClickCount / ViewCount * 100 : 0;

	public string BudgetDisplay => $"{BudgetInOMR:F2} OMR";
	public string DurationDisplay => DurationInDays == 1 ? "1 day" : $"{DurationInDays} days";

	internal BusinessAdRequestData ToData() => new()
	{
		Id = Id,
		UserId = UserId,
		CompanyName = CompanyName,
		Tagline = Tagline,
		Description = Description,
		WebsiteUrl = WebsiteUrl,
		Email = Email,
		PhoneNumber = PhoneNumber,
		Category = Category,
		BudgetInOMR = BudgetInOMR,
		DurationInDays = DurationInDays,
		TargetAudience = TargetAudience,
		Status = Status,
		CreatedAt = CreatedAt,
		ApprovedAt = ApprovedAt,
		StartDate = StartDate,
		EndDate = EndDate,
		ApprovedBy = ApprovedBy,
		RejectionReason = RejectionReason,
		ViewCount = ViewCount,
		ClickCount = ClickCount
	};
}
