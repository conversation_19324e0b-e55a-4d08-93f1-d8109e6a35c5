using AnalyticsData = JT.Client.Models.AnalyticsData;

namespace JT.Business.Models;

public partial record Analytics
{
	internal Analytics(AnalyticsData analyticsData)
	{
		Id = analyticsData.Id;
		Type = analyticsData.Type;
		Data = analyticsData.Data;
		LastUpdated = analyticsData.LastUpdated ?? DateTime.MinValue;
	}

	public string? Id { get; init; }
	public string? Type { get; init; }
	public object? Data { get; init; }
	public DateTime LastUpdated { get; init; }

	// Computed properties
	public string TypeDisplayName => Type switch
	{
		"popular_locations" => "Popular Locations",
		"trending_skills" => "Trending Skills",
		"salary_trends" => "Salary Trends",
		"user_activity" => "User Activity",
		"transfer_success_rate" => "Transfer Success Rate",
		_ => Type ?? "Unknown"
	};

	public bool IsRecent => (DateTime.UtcNow - LastUpdated).TotalDays <= 7;
	public string LastUpdatedDisplay => LastUpdated == DateTime.MinValue ? "Never" : 
		(DateTime.UtcNow - LastUpdated).TotalDays switch
		{
			< 1 => "Today",
			< 2 => "Yesterday",
			< 7 => $"{(int)(DateTime.UtcNow - LastUpdated).TotalDays} days ago",
			< 30 => $"{(int)(DateTime.UtcNow - LastUpdated).TotalDays / 7} weeks ago",
			_ => LastUpdated.ToString("MMM dd, yyyy")
		};

	internal AnalyticsData ToData() => new()
	{
		Id = Id,
		Type = Type,
		Data = Data,
		LastUpdated = LastUpdated
	};
}

// Supporting models for specific analytics types
public partial record LocationAnalytics
{
	public string? Name { get; init; }
	public int SearchCount { get; init; }
	public int JobCount { get; init; }
	public double PopularityRatio => SearchCount > 0 ? (double)JobCount / SearchCount : 0;
}

public partial record SkillAnalytics
{
	public string? Name { get; init; }
	public int DemandScore { get; init; }
	public double GrowthRate { get; init; }
	public bool IsGrowing => GrowthRate > 0;
	public string GrowthTrend => GrowthRate switch
	{
		> 15 => "High Growth",
		> 5 => "Moderate Growth", 
		> 0 => "Slow Growth",
		0 => "Stable",
		_ => "Declining"
	};
}

public partial record SalaryAnalytics
{
	public string? Name { get; init; }
	public decimal AverageSalary { get; init; }
	public string? Currency { get; init; }
	public double GrowthRate { get; init; }
	public string SalaryDisplay => $"{AverageSalary:F0} {Currency}";
	public bool IsGrowingSalary => GrowthRate > 0;
}
