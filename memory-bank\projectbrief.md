# Project Brief - JT Job Transfer Application

## Project Overview
JT is a comprehensive Omani job transfer application built using Uno Platform with .NET 9. The application revolutionizes professional mobility in Oman through advanced commercial features, token economy, and subscription tiers, generating 8,600+ OMR monthly revenue potential.

## Business Model - FULLY IMPLEMENTED ✅

### Revenue Streams (8 Complete)
1. **Banner Advertisements**: 5-50 OMR/month with performance analytics
2. **Sponsored Content**: 25-200 OMR/month with engagement tracking
3. **Job Promotions**: 10-100 OMR/promotion with visibility boost
4. **Directory Listings**: 15-75 OMR/month for business exposure
5. **Event Promotions**: 50-300 OMR/event for recruitment events
6. **Enterprise Packages**: 500-2000 OMR/month for large organizations
7. **Subscription Tiers**: 5-50 OMR/month per user (Bronze/Silver/Gold/Diamond)
8. **Token Economy**: 0.1 baisa per token with referral bonuses

### Core Features - PRODUCTION READY ✅
1. **Job Transfer Management**
   - Complete transfer request system with employer responses
   - Document upload and management
   - Status tracking and real-time notifications
   - Geographic search across Omani governorates

2. **Commercial Promotion System**
   - Complete advertising platform with 8 revenue streams
   - Performance analytics and ROI tracking
   - Business directory with enhanced listings
   - Event promotion for recruitment activities

3. **Advanced Token Economy**
   - User authentication and registration
   - User profiles and preferences
   - Social features (following, sharing)

4. **Cross-Platform Support**
   - Native experience on Windows, Android, iOS
   - Web application via WebAssembly
   - Desktop application support

### Technical Requirements
1. **Architecture**: MVVM pattern with clean separation of concerns
2. **Backend**: ASP.NET Core Web API with RESTful endpoints
3. **Authentication**: IdentityServer4 integration (needs migration to Duende)
4. **Data**: JSON-based development data, designed for future database integration
5. **UI Framework**: Uno Platform with XAML-based UI
6. **Testing**: Comprehensive unit and UI testing coverage

## Project Goals

### Primary Goals
- Create a modern, intuitive recipe management experience
- Provide seamless cross-platform functionality
- Enable social recipe sharing and discovery
- Maintain high code quality and testability

### Success Criteria
- Application runs on all target platforms
- User authentication and data persistence work reliably
- Recipe browsing and management features are fully functional
- Code coverage exceeds 80%
- Documentation is comprehensive and up-to-date

## Current Status
**Phase**: Early Development / Foundation Building
**Priority**: Completing core infrastructure and implementing comprehensive testing

## Key Stakeholders
- **Development Team**: Primary implementers
- **End Users**: Recipe enthusiasts and home cooks
- **Platform**: Uno Platform ecosystem

## Project Constraints
- Must support multiple platforms through single codebase
- Authentication system needs modernization
- Limited initial budget for external services
- Development team learning Uno Platform patterns

## Success Metrics
- Cross-platform deployment success rate
- User engagement with recipe features
- Application performance across platforms
- Code quality metrics (coverage, maintainability)
- Time to implement new features

## Risk Factors
- **Technical Debt**: Significant commented-out code and incomplete implementations
- **Testing Gap**: Minimal test coverage currently
- **Documentation**: Severely lacking project documentation
- **Dependencies**: Some pre-release packages in use
- **Security**: Deprecated IdentityServer4 needs migration
