namespace JT.Business.Services.Analytics;

public interface IAnalyticsService
{
	ValueTask<IImmutableList<Analytics>> GetAll(CancellationToken ct = default);
	ValueTask<Analytics?> GetById(string id, CancellationToken ct = default);
	ValueTask<Analytics?> GetByType(string type, CancellationToken ct = default);
	ValueTask<IImmutableList<LocationAnalytics>> GetPopularLocations(CancellationToken ct = default);
	ValueTask<IImmutableList<SkillAnalytics>> GetTrendingSkills(CancellationToken ct = default);
	ValueTask<IImmutableList<SalaryAnalytics>> GetSalaryTrends(CancellationToken ct = default);
	ValueTask<Analytics> UpdateAnalytics(string type, object data, CancellationToken ct = default);
	ValueTask<bool> RefreshAnalytics(string type, CancellationToken ct = default);
}
