// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace JT.Content.Client.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class TokenTransactionDataPaginatedResponse : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The data property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<global::JT.Content.Client.Models.TokenTransactionData>? Data { get; set; }
#nullable restore
#else
        public List<global::JT.Content.Client.Models.TokenTransactionData> Data { get; set; }
#endif
        /// <summary>The hasNextPage property</summary>
        public bool? HasNextPage { get; private set; }
        /// <summary>The hasPreviousPage property</summary>
        public bool? HasPreviousPage { get; private set; }
        /// <summary>The page property</summary>
        public int? Page { get; set; }
        /// <summary>The pageSize property</summary>
        public int? PageSize { get; set; }
        /// <summary>The totalCount property</summary>
        public int? TotalCount { get; set; }
        /// <summary>The totalPages property</summary>
        public int? TotalPages { get; set; }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::JT.Content.Client.Models.TokenTransactionDataPaginatedResponse"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::JT.Content.Client.Models.TokenTransactionDataPaginatedResponse CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::JT.Content.Client.Models.TokenTransactionDataPaginatedResponse();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "data", n => { Data = n.GetCollectionOfObjectValues<global::JT.Content.Client.Models.TokenTransactionData>(global::JT.Content.Client.Models.TokenTransactionData.CreateFromDiscriminatorValue)?.AsList(); } },
                { "hasNextPage", n => { HasNextPage = n.GetBoolValue(); } },
                { "hasPreviousPage", n => { HasPreviousPage = n.GetBoolValue(); } },
                { "page", n => { Page = n.GetIntValue(); } },
                { "pageSize", n => { PageSize = n.GetIntValue(); } },
                { "totalCount", n => { TotalCount = n.GetIntValue(); } },
                { "totalPages", n => { TotalPages = n.GetIntValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteCollectionOfObjectValues<global::JT.Content.Client.Models.TokenTransactionData>("data", Data);
            writer.WriteIntValue("page", Page);
            writer.WriteIntValue("pageSize", PageSize);
            writer.WriteIntValue("totalCount", TotalCount);
            writer.WriteIntValue("totalPages", TotalPages);
        }
    }
}
#pragma warning restore CS0618
