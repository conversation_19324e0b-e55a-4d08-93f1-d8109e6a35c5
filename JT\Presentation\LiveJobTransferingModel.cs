using JT.Business.Services.TransferRequests;

namespace JT.Presentation;

public partial record LiveJobTransferingParameter(TransferRequest TransferRequest, IImmutableList<Step> Steps);

public partial class LiveJobTransferingModel
{
    private readonly ITransferRequestService _transferRequestService;

    private readonly IImmutableList<Step> _steps;
	private readonly INavigator _navigator;

	public TransferRequest TransferRequest { get; }

	public IState<StepIterator> Steps => State.Value(this, () => new StepIterator(_steps));

	public IState<bool> Completed => State.Value(this, () => false);

	public LiveJobTransferingModel(LiveJobTransferingParameter parameter, ITransferRequestService transferRequestService, INavigator navigator)
	{
        TransferRequest = parameter.TransferRequest;
        _transferRequestService = transferRequestService;
		_navigator = navigator;
		_steps = parameter.Steps;
	}

	public async ValueTask Complete()
	{
		await Completed.SetAsync(true);
	}

	public async ValueTask BackToLastStep()
	{
		await Completed.SetAsync(false);
	}

	public async ValueTask Favorite(CancellationToken ct)
	{
		await _transferRequestService.Favorite(TransferRequest, ct);
		await _navigator.NavigateViewModelAsync<HomeModel>(this, qualifier: Qualifiers.ClearBackStack, cancellation: ct);
	}
}
