using JT.Client.Api;

namespace JT.Business.Services.Promotions;

public class PromotionService : IPromotionService
{
	private readonly JTServiceClient api;

	public PromotionService(JTServiceClient api)
	{
		this.api = api;
	}

	public async ValueTask<IImmutableList<Promotion>> GetAllPromotions(CancellationToken ct = default)
	{
		try
		{
			var promotionsData = await api.Api.Promotion.GetAsync(cancellationToken: ct);
			return promotionsData?.Select(p => new Promotion(p)).ToImmutableList() ?? ImmutableList<Promotion>.Empty;
		}
		catch
		{
			return ImmutableList<Promotion>.Empty;
		}
	}

	public async ValueTask<IImmutableList<Promotion>> GetActivePromotions(CancellationToken ct = default)
	{
		var allPromotions = await GetAllPromotions(ct);
		return allPromotions.Where(p => p.IsActive).ToImmutableList();
	}

	public async ValueTask<IImmutableList<Promotion>> GetPromotionsByCompany(string companyId, CancellationToken ct = default)
	{
		var allPromotions = await GetAllPromotions(ct);
		return allPromotions.Where(p => p.CompanyId == companyId).ToImmutableList();
	}

	public async ValueTask<IImmutableList<Promotion>> GetPromotionsByType(string type, CancellationToken ct = default)
	{
		var allPromotions = await GetAllPromotions(ct);
		return allPromotions.Where(p => string.Equals(p.Type, type, StringComparison.OrdinalIgnoreCase)).ToImmutableList();
	}

	public async ValueTask<IImmutableList<Promotion>> GetPromotionsByCategory(string category, CancellationToken ct = default)
	{
		var allPromotions = await GetAllPromotions(ct);
		return allPromotions.Where(p => string.Equals(p.Category, category, StringComparison.OrdinalIgnoreCase)).ToImmutableList();
	}

	public async ValueTask<Promotion?> GetPromotionById(string promotionId, CancellationToken ct = default)
	{
		try
		{
			var promotionData = await api.Api.Promotion[promotionId].GetAsync(cancellationToken: ct);
			return promotionData != null ? new Promotion(promotionData) : null;
		}
		catch
		{
			return null;
		}
	}

	public async ValueTask<Promotion> CreatePromotion(Promotion promotion, CancellationToken ct = default)
	{
		try
		{
			var promotionData = promotion.ToData();
			var createdData = await api.Api.Promotion.PostAsync(promotionData, cancellationToken: ct);
			return new Promotion(createdData);
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Failed to create promotion: {ex.Message}", ex);
		}
	}

	public async ValueTask<Promotion> UpdatePromotion(Promotion promotion, CancellationToken ct = default)
	{
		try
		{
			var promotionData = promotion.ToData();
			var updatedData = await api.Api.Promotion[promotion.Id].PutAsync(promotionData, cancellationToken: ct);
			return new Promotion(updatedData);
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Failed to update promotion: {ex.Message}", ex);
		}
	}

	public async ValueTask<bool> DeletePromotion(string promotionId, CancellationToken ct = default)
	{
		try
		{
			await api.Api.Promotion[promotionId].DeleteAsync(cancellationToken: ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> ApprovePromotion(string promotionId, string approvedBy, CancellationToken ct = default)
	{
		try
		{
			var promotion = await GetPromotionById(promotionId, ct);
			if (promotion == null) return false;

			var updatedPromotion = promotion with 
			{ 
				Status = "approved",
				ApprovedAt = DateTime.UtcNow,
				ApprovedBy = approvedBy
			};

			await UpdatePromotion(updatedPromotion, ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> RejectPromotion(string promotionId, string rejectionReason, CancellationToken ct = default)
	{
		try
		{
			var promotion = await GetPromotionById(promotionId, ct);
			if (promotion == null) return false;

			var updatedPromotion = promotion with 
			{ 
				Status = "rejected",
				RejectionReason = rejectionReason
			};

			await UpdatePromotion(updatedPromotion, ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> PausePromotion(string promotionId, CancellationToken ct = default)
	{
		try
		{
			var promotion = await GetPromotionById(promotionId, ct);
			if (promotion == null) return false;

			var updatedPromotion = promotion with { Status = "paused" };
			await UpdatePromotion(updatedPromotion, ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> ResumePromotion(string promotionId, CancellationToken ct = default)
	{
		try
		{
			var promotion = await GetPromotionById(promotionId, ct);
			if (promotion == null) return false;

			var updatedPromotion = promotion with { Status = "active" };
			await UpdatePromotion(updatedPromotion, ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> TrackView(string promotionId, string? userId = null, CancellationToken ct = default)
	{
		try
		{
			await api.Api.Promotion[promotionId].Track.View.PostAsync(new { UserId = userId }, cancellationToken: ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> TrackClick(string promotionId, string? userId = null, CancellationToken ct = default)
	{
		try
		{
			await api.Api.Promotion[promotionId].Track.Click.PostAsync(new { UserId = userId }, cancellationToken: ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> TrackConversion(string promotionId, string? userId = null, decimal? revenue = null, CancellationToken ct = default)
	{
		try
		{
			await api.Api.Promotion[promotionId].Track.Conversion.PostAsync(new { UserId = userId, Revenue = revenue }, cancellationToken: ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<PromotionAnalytics?> GetPromotionAnalytics(string promotionId, DateTime? date = null, CancellationToken ct = default)
	{
		try
		{
			var analyticsData = await api.Api.Promotion[promotionId].Analytics.GetAsync(cancellationToken: ct);
			return analyticsData != null ? new PromotionAnalytics(analyticsData) : null;
		}
		catch
		{
			return null;
		}
	}

	public async ValueTask<IImmutableList<PromotionAnalytics>> GetPromotionAnalyticsRange(string promotionId, DateTime startDate, DateTime endDate, CancellationToken ct = default)
	{
		try
		{
			// This would be implemented with proper date range filtering
			var analytics = await GetPromotionAnalytics(promotionId, null, ct);
			return analytics != null ? ImmutableList.Create(analytics) : ImmutableList<PromotionAnalytics>.Empty;
		}
		catch
		{
			return ImmutableList<PromotionAnalytics>.Empty;
		}
	}

	public async ValueTask<IImmutableList<Promotion>> GetTopPerformingPromotions(int count = 10, CancellationToken ct = default)
	{
		var allPromotions = await GetAllPromotions(ct);
		return allPromotions
			.Where(p => p.IsActive)
			.OrderByDescending(p => p.ClickThroughRate)
			.ThenByDescending(p => p.ConversionRate)
			.Take(count)
			.ToImmutableList();
	}

	public async ValueTask<IImmutableList<Promotion>> GetPromotionsNeedingOptimization(CancellationToken ct = default)
	{
		var allPromotions = await GetAllPromotions(ct);
		return allPromotions
			.Where(p => p.IsActive && (p.ClickThroughRate < 1 || p.ViewCount < 100))
			.ToImmutableList();
	}

	public async ValueTask<decimal> GetTotalRevenue(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken ct = default)
	{
		var allPromotions = await GetAllPromotions(ct);
		return allPromotions
			.Where(p => !fromDate.HasValue || p.CreatedAt >= fromDate.Value)
			.Where(p => !toDate.HasValue || p.CreatedAt <= toDate.Value)
			.Sum(p => p.TotalSpent);
	}

	public async ValueTask<decimal> GetCompanyRevenue(string companyId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken ct = default)
	{
		var companyPromotions = await GetPromotionsByCompany(companyId, ct);
		return companyPromotions
			.Where(p => !fromDate.HasValue || p.CreatedAt >= fromDate.Value)
			.Where(p => !toDate.HasValue || p.CreatedAt <= toDate.Value)
			.Sum(p => p.TotalSpent);
	}

	public async ValueTask<IImmutableList<Promotion>> SearchPromotions(string query, CancellationToken ct = default)
	{
		var allPromotions = await GetAllPromotions(ct);
		return allPromotions
			.Where(p => 
				p.Title.Contains(query, StringComparison.OrdinalIgnoreCase) ||
				p.Description.Contains(query, StringComparison.OrdinalIgnoreCase) ||
				p.CompanyName.Contains(query, StringComparison.OrdinalIgnoreCase) ||
				p.Keywords.Any(k => k.Contains(query, StringComparison.OrdinalIgnoreCase)))
			.ToImmutableList();
	}

	public async ValueTask<IImmutableList<Promotion>> GetPromotionsByBudgetRange(decimal minBudget, decimal maxBudget, CancellationToken ct = default)
	{
		var allPromotions = await GetAllPromotions(ct);
		return allPromotions
			.Where(p => p.BudgetOMR >= minBudget && p.BudgetOMR <= maxBudget)
			.ToImmutableList();
	}

	public async ValueTask<IImmutableList<Promotion>> GetExpiringPromotions(int daysAhead = 7, CancellationToken ct = default)
	{
		var allPromotions = await GetAllPromotions(ct);
		var cutoffDate = DateTime.UtcNow.AddDays(daysAhead);
		
		return allPromotions
			.Where(p => p.IsActive && p.EndDate.HasValue && p.EndDate.Value <= cutoffDate)
			.OrderBy(p => p.EndDate)
			.ToImmutableList();
	}
}
