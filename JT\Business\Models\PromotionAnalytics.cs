using PromotionAnalyticsData = JT.Client.Models.PromotionAnalyticsData;

namespace JT.Business.Models;

public partial record PromotionAnalytics
{
	internal PromotionAnalytics(PromotionAnalyticsData analyticsData)
	{
		Id = analyticsData.Id ?? string.Empty;
		PromotionId = analyticsData.PromotionId ?? string.Empty;
		Date = analyticsData.Date ?? DateTime.MinValue;
		Views = analyticsData.Views ?? 0;
		Clicks = analyticsData.Clicks ?? 0;
		Conversions = analyticsData.Conversions ?? 0;
		Impressions = analyticsData.Impressions ?? 0;
		UniqueViews = analyticsData.UniqueViews ?? 0;
		BounceRate = analyticsData.BounceRate ?? 0;
		AverageTimeSpent = analyticsData.AverageTimeSpent ?? 0;
		CostSpent = analyticsData.CostSpent ?? 0;
		Revenue = analyticsData.Revenue ?? 0;
		DeviceTypes = analyticsData.DeviceTypes?.ToImmutableDictionary() ?? ImmutableDictionary<string, int>.Empty;
		LocationData = analyticsData.LocationData?.ToImmutableDictionary() ?? ImmutableDictionary<string, int>.Empty;
		AgeGroups = analyticsData.AgeGroups?.ToImmutableDictionary() ?? ImmutableDictionary<string, int>.Empty;
		TrafficSources = analyticsData.TrafficSources?.ToImmutableDictionary() ?? ImmutableDictionary<string, int>.Empty;
	}

	public string Id { get; init; }
	public string PromotionId { get; init; }
	public DateTime Date { get; init; }
	public int Views { get; init; }
	public int Clicks { get; init; }
	public int Conversions { get; init; }
	public int Impressions { get; init; }
	public int UniqueViews { get; init; }
	public decimal BounceRate { get; init; } // Percentage
	public decimal AverageTimeSpent { get; init; } // Seconds
	public decimal CostSpent { get; init; }
	public decimal Revenue { get; init; }
	public IImmutableDictionary<string, int> DeviceTypes { get; init; } // mobile, desktop, tablet
	public IImmutableDictionary<string, int> LocationData { get; init; } // city/governorate breakdown
	public IImmutableDictionary<string, int> AgeGroups { get; init; } // age group breakdown
	public IImmutableDictionary<string, int> TrafficSources { get; init; } // direct, search, social, etc.

	// Computed properties
	public decimal ClickThroughRate => Views > 0 ? (decimal)Clicks / Views * 100 : 0;
	public decimal ConversionRate => Clicks > 0 ? (decimal)Conversions / Clicks * 100 : 0;
	public decimal CostPerClick => Clicks > 0 ? CostSpent / Clicks : 0;
	public decimal CostPerConversion => Conversions > 0 ? CostSpent / Conversions : 0;
	public decimal ReturnOnInvestment => CostSpent > 0 ? ((Revenue - CostSpent) / CostSpent) * 100 : 0;
	public decimal EngagementRate => Impressions > 0 ? (decimal)(Clicks + Views) / Impressions * 100 : 0;

	public string PerformanceRating => ClickThroughRate switch
	{
		>= 5 => "Excellent",
		>= 3 => "Good",
		>= 1.5m => "Average",
		>= 0.5m => "Below Average",
		_ => "Poor"
	};

	public string TopDevice => DeviceTypes.OrderByDescending(d => d.Value).FirstOrDefault().Key ?? "Unknown";
	public string TopLocation => LocationData.OrderByDescending(l => l.Value).FirstOrDefault().Key ?? "Unknown";
	public string TopAgeGroup => AgeGroups.OrderByDescending(a => a.Value).FirstOrDefault().Key ?? "Unknown";
	public string TopTrafficSource => TrafficSources.OrderByDescending(t => t.Value).FirstOrDefault().Key ?? "Unknown";

	public string AverageTimeSpentDisplay => AverageTimeSpent switch
	{
		< 60 => $"{AverageTimeSpent:F0} seconds",
		< 3600 => $"{AverageTimeSpent / 60:F1} minutes",
		_ => $"{AverageTimeSpent / 3600:F1} hours"
	};

	public string BounceRateDisplay => $"{BounceRate:F1}%";
	public string ClickThroughRateDisplay => $"{ClickThroughRate:F2}%";
	public string ConversionRateDisplay => $"{ConversionRate:F2}%";
	public string EngagementRateDisplay => $"{EngagementRate:F2}%";
	public string ROIDisplay => $"{ReturnOnInvestment:F1}%";

	public bool IsHighPerforming => ClickThroughRate >= 3 && ConversionRate >= 2;
	public bool NeedsOptimization => ClickThroughRate < 1 || BounceRate > 70;

	internal PromotionAnalyticsData ToData() => new()
	{
		Id = Id,
		PromotionId = PromotionId,
		Date = Date,
		Views = Views,
		Clicks = Clicks,
		Conversions = Conversions,
		Impressions = Impressions,
		UniqueViews = UniqueViews,
		BounceRate = BounceRate,
		AverageTimeSpent = AverageTimeSpent,
		CostSpent = CostSpent,
		Revenue = Revenue,
		DeviceTypes = DeviceTypes.ToDictionary(d => d.Key, d => d.Value),
		LocationData = LocationData.ToDictionary(l => l.Key, l => l.Value),
		AgeGroups = AgeGroups.ToDictionary(a => a.Key, a => a.Value),
		TrafficSources = TrafficSources.ToDictionary(t => t.Key, t => t.Value)
	};
}
