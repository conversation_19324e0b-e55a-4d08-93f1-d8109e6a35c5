[{"id": "650e8400-e29b-41d4-a716-446655440001", "userId": "550e8400-e29b-41d4-a716-446655440001", "ImageUrl": "ms-appx:///Assets/Transfers/avocado_toast.png", "name": "Software Engineer", "requestTitle": "Software Engineer Transfer - Muscat to Salalah", "currentLocation": {"city": "Muscat", "state": "Muscat Governorate", "country": "Oman"}, "destinationLocation": {"city": "<PERSON><PERSON><PERSON>", "state": "Dhofar Governorate", "country": "Oman"}, "currentSalaryGrade": "Grade 12", "desiredSalaryGrade": "Grade 12", "currentFinancialGrade": "Level 3", "desiredFinancialGrade": "Level 3", "industry": "Information Technology", "Category": {"id": 1, "name": "Information Technology", "UrlIcon": "ms-appx:///Assets/Categories/hamburger.png", "color": "#007BFF", "description": "Software development, system administration, cybersecurity, and IT support roles", "TransferRequests": 2}, "TransferTime": {"hours": 1, "minutes": 30, "seconds": 0, "ticks": 9600000000}, "Difficulty": 4, "Serves": 2, "Nutrition": {"calories": 300, "protein": 20, "fat": 10, "carbs": 30, "fiber": 5, "sugar": 15}, "Steps": [{"Name": "Getting started", "TransferTime": {"ticks": 3000000000}, "Transferware": ["Fork", "Knife", "<PERSON><PERSON><PERSON>", "Spa<PERSON>la"], "Description": "Melt butter in a skillet over medium-low heat.\n\nCrack eggs into the skillet side by side and cook until eggs are white on the bottom layer and firm enough to flip, 2 to 3 minutes.", "Ingredients": ["Eggs", "Olive oil"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Next step", "TransferTime": {"ticks": 3000000000}, "Transferware": ["<PERSON><PERSON><PERSON>", "Spa<PERSON>la"], "Description": "Flip eggs, trying not to crack the yolk, and cook until the egg reaches desired doneness, 2 to 4 minutes more.", "Ingredients": ["Bread"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "TransferTime": {"ticks": 3000000000}, "Transferware": ["Fork", "Knife", "Bowl", "Cutting Boards"], "Description": "Meanwhile, toast bread slices to desired doneness, 3 to 4 minutes.", "Ingredients": ["Avocado", "Lemon juice", "Salt and pepper"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "Date": "2024-01-15T00:00:00Z", "transferReason": "Family relocation - spouse transferred to Salalah University", "status": "pending", "priority": "high", "submissionDate": "2024-01-15T10:00:00Z", "expirationDate": "2024-04-15T23:59:59Z", "viewCount": 45, "interestedEmployers": 3, "documents": ["resume.pdf", "certificates.pdf", "recommendation_letter.pdf"], "requiredSkills": ["C#", ".NET Core", "Azure", "SQL Server", "Angular"], "preferredCompanySize": "Large (500+ employees)", "remoteWorkPreference": "Hybrid", "languageRequirements": ["Arabic", "English"], "createdBy": {"id": "550e8400-e29b-41d4-a716-446655440002", "fullName": "<PERSON>", "UrlProfileImage": "ms-appx:///Assets/Profiles/roberta_any.png", "currentPosition": "Senior Software Engineer", "experience": "8 years"}, "responses": [{"id": "resp_001", "employerId": "emp_salalah_tech", "companyName": "Salalah Technology Solutions", "responseDate": "2024-01-18T14:30:00Z", "status": "interested", "message": "We are interested in your profile. Would you be available for an interview next week?", "offeredSalary": "Grade 12 equivalent", "interviewScheduled": true}, {"id": "resp_002", "employerId": "emp_dhofar_systems", "companyName": "Dhofar Systems LLC", "responseDate": "2024-01-19T09:15:00Z", "status": "interview_scheduled", "message": "Your experience matches our requirements. Interview scheduled for January 25th.", "offeredSalary": "Grade 12-13", "interviewScheduled": true}]}, {"id": "650e8400-e29b-41d4-a716-446655440002", "userId": "550e8400-e29b-41d4-a716-446655440002", "ImageUrl": "ms-appx:///Assets/Transfers/fresh_salad_thaid.png", "name": "Marketing Manager", "requestTitle": "Marketing Manager Transfer - <PERSON><PERSON><PERSON> to Muscat", "currentLocation": {"city": "<PERSON><PERSON><PERSON>", "state": "Dhofar Governorate", "country": "Oman"}, "destinationLocation": {"city": "Muscat", "state": "Muscat Governorate", "country": "Oman"}, "currentSalaryGrade": "Grade 10", "desiredSalaryGrade": "Grade 11", "currentFinancialGrade": "Level 2", "desiredFinancialGrade": "Level 3", "industry": "Marketing & Advertising", "Category": {"id": 2, "name": "Oil & Gas", "UrlIcon": "ms-appx:///Assets/Categories/lunch.png", "color": "#FF6B35", "description": "Petroleum engineering, drilling operations, refinery operations, and energy sector roles", "TransferRequests": 1}, "TransferTime": {"hours": 0, "minutes": 45, "seconds": 0, "ticks": 6000000000}, "Difficulty": 3, "Serves": 1, "Nutrition": {"calories": 250, "protein": 15, "fat": 8, "carbs": 25, "fiber": 4, "sugar": 12}, "Steps": [{"Name": "Getting started", "TransferTime": {"ticks": 1800000000}, "Transferware": ["Knife", "Dish"], "Description": "Cut all your vegetables to size.\n\nYou can also use a bag of pre-shredded coleslaw.\n\nI diced the English cucumbers and cut the red bell peppers into thin strips.\n\nYou may also use julienne carrots, lettuce, etc.", "Ingredients": ["Carrot"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "TransferTime": {"ticks": 3600000000}, "Transferware": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Knife", "Cutting Boards"], "Description": "Place the dressing ingredients in a jar and shake together until well combined.\n\nAdd the mix salad greens first, then strategically place the carrot, onion, red pepper and cucumber around the mixed greens.", "Ingredients": ["<PERSON><PERSON>", "Carrot", "Onion", "Red Pepper", "<PERSON><PERSON><PERSON>ber"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "TransferTime": {"ticks": 600000000}, "Transferware": ["Fork", "Knife", "Bowl"], "Description": "Finish up with the dressing.\n\nAdd the dressing just before you want to serve the salad otherwise it'll go soggy.", "Ingredients": ["Lime Juice", "Sesame Oil", "<PERSON>"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "Date": "2024-01-10T00:00:00Z", "transferReason": "Career advancement opportunities in the capital", "status": "pending", "priority": "normal", "submissionDate": "2024-01-10T09:00:00Z", "expirationDate": "2024-04-10T23:59:59Z", "viewCount": 67, "interestedEmployers": 5, "documents": ["resume.pdf", "portfolio.pdf", "certifications.pdf"], "requiredSkills": ["Digital Marketing", "Social Media Management", "Content Strategy", "Google Analytics"], "preferredCompanySize": "Medium (100-500 employees)", "remoteWorkPreference": "Office", "languageRequirements": ["Arabic", "English"], "createdBy": {"id": "550e8400-e29b-41d4-a716-446655440002", "fullName": "<PERSON>ima <PERSON>", "UrlProfileImage": "ms-appx:///Assets/Profiles/roberta_any.png", "currentPosition": "Marketing Manager", "experience": "5 years"}, "responses": [{"id": "resp_003", "employerId": "emp_muscat_marketing", "companyName": "Muscat Marketing Group", "responseDate": "2024-01-12T11:00:00Z", "status": "offer_made", "message": "We would like to offer you a Senior Marketing Manager position.", "offeredSalary": "Grade 11", "interviewScheduled": false}]}, {"id": "650e8400-e29b-41d4-a716-446655440003", "userId": "550e8400-e29b-41d4-a716-446655440002", "ImageUrl": "ms-appx:///Assets/Transfers/fresh_salad_thaid.png", "name": "Marketing Manager", "requestTitle": "Marketing Manager Transfer - <PERSON><PERSON><PERSON> to Muscat", "currentLocation": {"city": "<PERSON><PERSON><PERSON>", "state": "Dhofar Governorate", "country": "Oman"}, "destinationLocation": {"city": "Muscat", "state": "Muscat Governorate", "country": "Oman"}, "currentSalaryGrade": "Grade 10", "desiredSalaryGrade": "Grade 11", "currentFinancialGrade": "Level 2", "desiredFinancialGrade": "Level 3", "industry": "Marketing & Advertising", "Category": {"id": 2, "name": "Oil & Gas", "UrlIcon": "ms-appx:///Assets/Categories/lunch.png", "color": "#FF6B35", "description": "Petroleum engineering, drilling operations, refinery operations, and energy sector roles", "TransferRequests": 4}, "TransferTime": {"hours": 0, "minutes": 45, "seconds": 0, "ticks": 6000000000}, "Difficulty": 3, "Serves": 1, "Nutrition": {"calories": 250, "protein": 15, "fat": 8, "carbs": 25, "fiber": 4, "sugar": 12}, "Steps": [{"Name": "Getting started", "TransferTime": {"ticks": 1800000000}, "Transferware": ["Knife", "Dish"], "Description": "Cut all your vegetables to size.\n\nYou can also use a bag of pre-shredded coleslaw.\n\nI diced the English cucumbers and cut the red bell peppers into thin strips.\n\nYou may also use julienne carrots, lettuce, etc.", "Ingredients": ["Carrot"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "TransferTime": {"ticks": 3600000000}, "Transferware": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Knife", "Cutting Boards"], "Description": "Place the dressing ingredients in a jar and shake together until well combined.\n\nAdd the mix salad greens first, then strategically place the carrot, onion, red pepper and cucumber around the mixed greens.", "Ingredients": ["<PERSON><PERSON>", "Carrot", "Onion", "Red Pepper", "<PERSON><PERSON><PERSON>ber"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "TransferTime": {"ticks": 600000000}, "Transferware": ["Fork", "Knife", "Bowl"], "Description": "Finish up with the dressing.\n\nAdd the dressing just before you want to serve the salad otherwise it'll go soggy.", "Ingredients": ["Lime Juice", "Sesame Oil", "<PERSON>"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "Date": "2024-01-10T00:00:00Z", "transferReason": "Career advancement opportunities in the capital", "status": "pending", "priority": "normal", "submissionDate": "2024-01-10T09:00:00Z", "expirationDate": "2024-04-10T23:59:59Z", "viewCount": 67, "interestedEmployers": 5, "documents": ["resume.pdf", "portfolio.pdf", "certifications.pdf"], "requiredSkills": ["Digital Marketing", "Social Media Management", "Content Strategy", "Google Analytics"], "preferredCompanySize": "Medium (100-500 employees)", "remoteWorkPreference": "Office", "languageRequirements": ["Arabic", "English"], "createdBy": {"id": "550e8400-e29b-41d4-a716-446655440002", "fullName": "<PERSON>ima <PERSON>", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "currentPosition": "Marketing Manager", "experience": "5 years"}, "responses": [{"id": "resp_003", "employerId": "emp_muscat_marketing", "companyName": "Muscat Marketing Group", "responseDate": "2024-01-12T11:00:00Z", "status": "offer_made", "message": "We would like to offer you a Senior Marketing Manager position.", "offeredSalary": "Grade 11", "interviewScheduled": false}]}, {"id": "650e8400-e29b-41d4-a716-446655440004", "userId": "550e8400-e29b-41d4-a716-446655440003", "ImageUrl": "ms-appx:///Assets/Transfers/fresh_salad_thaid.png", "name": "Marketing Manager", "requestTitle": "Marketing Manager Transfer - <PERSON><PERSON><PERSON> to Muscat", "currentLocation": {"city": "<PERSON><PERSON><PERSON>", "state": "Dhofar Governorate", "country": "Oman"}, "destinationLocation": {"city": "Muscat", "state": "Muscat Governorate", "country": "Oman"}, "currentSalaryGrade": "Grade 10", "desiredSalaryGrade": "Grade 11", "currentFinancialGrade": "Level 2", "desiredFinancialGrade": "Level 3", "industry": "Marketing & Advertising", "Category": {"id": 2, "name": "Oil & Gas", "UrlIcon": "ms-appx:///Assets/Categories/pancakes.png", "color": "#FF6B35", "description": "Petroleum engineering, drilling operations, refinery operations, and energy sector roles", "TransferRequests": 0}, "TransferTime": {"hours": 0, "minutes": 45, "seconds": 0, "ticks": 6000000000}, "Difficulty": 3, "Serves": 1, "Nutrition": {"calories": 250, "protein": 15, "fat": 8, "carbs": 25, "fiber": 4, "sugar": 12}, "Steps": [{"Name": "Getting started", "TransferTime": {"ticks": 1800000000}, "Transferware": ["Knife", "Dish"], "Description": "Cut all your vegetables to size.\n\nYou can also use a bag of pre-shredded coleslaw.\n\nI diced the English cucumbers and cut the red bell peppers into thin strips.\n\nYou may also use julienne carrots, lettuce, etc.", "Ingredients": ["Carrot"], "Number": 1, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Second step", "TransferTime": {"ticks": 3600000000}, "Transferware": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Knife", "Cutting Boards"], "Description": "Place the dressing ingredients in a jar and shake together until well combined.\n\nAdd the mix salad greens first, then strategically place the carrot, onion, red pepper and cucumber around the mixed greens.", "Ingredients": ["<PERSON><PERSON>", "Carrot", "Onion", "Red Pepper", "<PERSON><PERSON><PERSON>ber"], "Number": 2, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}, {"Name": "Final step", "TransferTime": {"ticks": 600000000}, "Transferware": ["Fork", "Knife", "Bowl"], "Description": "Finish up with the dressing.\n\nAdd the dressing just before you want to serve the salad otherwise it'll go soggy.", "Ingredients": ["Lime Juice", "Sesame Oil", "<PERSON>"], "Number": 3, "UrlVideo": "ms-appx:///Assets/Videos/CookingVideo.mp4"}], "Date": "2024-01-10T00:00:00Z", "transferReason": "Career advancement opportunities in the capital", "status": "pending", "priority": "normal", "submissionDate": "2024-01-10T09:00:00Z", "expirationDate": "2024-04-10T23:59:59Z", "viewCount": 67, "interestedEmployers": 5, "documents": ["resume.pdf", "portfolio.pdf", "certifications.pdf"], "requiredSkills": ["Digital Marketing", "Social Media Management", "Content Strategy", "Google Analytics"], "preferredCompanySize": "Medium (100-500 employees)", "remoteWorkPreference": "Office", "languageRequirements": ["Arabic", "English"], "createdBy": {"id": "550e8400-e29b-41d4-a716-446655440002", "fullName": "<PERSON>ima <PERSON>", "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png", "currentPosition": "Marketing Manager", "experience": "5 years"}, "responses": [{"id": "resp_003", "employerId": "emp_muscat_marketing", "companyName": "Muscat Marketing Group", "responseDate": "2024-01-12T11:00:00Z", "status": "offer_made", "message": "We would like to offer you a Senior Marketing Manager position.", "offeredSalary": "Grade 11", "interviewScheduled": false}]}]