namespace JT.Content.Client.Mock;

internal static class MockApiConstants
{
    // File Names
    internal const string SavedTransferRequestsFile = "SavedTransferRequests.json";
    internal const string TransferRequestsFile = "TransferRequests.json";
    internal const string CategoriesFile = "Categories.json";
    internal const string NotificationsFile = "Notifications.json";
    internal const string UserFile = "Users.json";
    internal const string JobTransfersFile = "JobTransfers.json";
    internal const string SavedJobTransfersFile = "SavedJobTransfers.json";

    // API Paths
    internal const string TransferRequestBasePath = "/api/TransferRequest";
    internal const string CategoryPath = "/api/TransferRequest/categories";
    internal const string TrendingPath = "/api/TransferRequest/trending";
    internal const string PopularPath = "/api/TransferRequest/popular";
    internal const string FavoritedPath = "/api/TransferRequest/favorited";
    internal const string StepsPath = "/steps";
    internal const string IngredientsPath = "/ingredients";
    internal const string ReviewsPath = "/reviews";
    internal const string ReviewLikePath = "/api/TransferRequest/review/like";
    internal const string ReviewDislikePath = "/api/TransferRequest/review/dislike";
    internal const string JobTransferControllerPath = "/api/JobTransferController";
}
