// Copyright (c) <PERSON> & <PERSON>. All rights reserved.
// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.


using IdentityServer4.Models;
using System.Collections.Generic;

namespace JT.Identity;

public static class Config
{
    public static IEnumerable<IdentityResource> IdentityResources =>
        new IdentityResource[]
        { 
            new IdentityResources.OpenId()
        };

    public static IEnumerable<ApiScope> ApiScopes =>
        new ApiScope[]
        {
            new ApiScope("JTApi", "Job Transfer API")
        };

    public static IEnumerable<Client> Clients =>
        new Client[] 
        {
            new Client
            {
                ClientId = "JT",
                AllowedGrantTypes = GrantTypes.ClientCredentials,
                ClientSecrets =
                {
                    new Secret("A2W7aQVFQWRX".Sha256())
                },
                AllowedScopes = { "JTApi" }
            },
            // Add new client for SMS authentication
            new Client
            {
                ClientId = "JT.SMS",
                AllowedGrantTypes = { "sms_auth" },
                RequireClientSecret = false,
                AllowedScopes = { "JTApi" },
                AllowOfflineAccess = true,
                RefreshTokenUsage = TokenUsage.ReUse,
                RefreshTokenExpiration = TokenExpiration.Sliding
            }
        };
}
