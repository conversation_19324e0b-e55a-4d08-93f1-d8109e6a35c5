using OrganizationData = JT.Client.Models.OrganizationData;

namespace JT.Business.Models;

public partial record Organization
{
	internal Organization(OrganizationData organizationData)
	{
		Id = organizationData.Id;
		Name = organizationData.Name;
		Industry = organizationData.Industry;
		Location = new Location(organizationData.Location);
		CompanySize = organizationData.CompanySize;
		LogoUrl = organizationData.LogoUrl;
		Description = organizationData.Description;
		Website = organizationData.Website;
		IsVerified = organizationData.IsVerified ?? false;
		SubscriptionTier = organizationData.SubscriptionTier;
		ActiveJobPostings = organizationData.ActiveJobPostings ?? 0;
		EmployeeCount = organizationData.EmployeeCount ?? 0;
		EstablishedYear = organizationData.EstablishedYear ?? 0;
		Benefits = organizationData.Benefits?.ToImmutableList() ?? ImmutableList<string>.Empty;
		WorkEnvironment = organizationData.WorkEnvironment;
		LanguagesRequired = organizationData.LanguagesRequired?.ToImmutableList() ?? ImmutableList<string>.Empty;
		ContactInfo = new ContactInfo(organizationData.ContactInfo);
	}

	public string? Id { get; init; }
	public string? Name { get; init; }
	public string? Industry { get; init; }
	public Location Location { get; init; }
	public string? CompanySize { get; init; }
	public string? LogoUrl { get; init; }
	public string? Description { get; init; }
	public string? Website { get; init; }
	public bool IsVerified { get; init; }
	public string? SubscriptionTier { get; init; }
	public int ActiveJobPostings { get; init; }
	public int EmployeeCount { get; init; }
	public int EstablishedYear { get; init; }
	public IImmutableList<string> Benefits { get; init; }
	public string? WorkEnvironment { get; init; }
	public IImmutableList<string> LanguagesRequired { get; init; }
	public ContactInfo ContactInfo { get; init; }

	// Computed properties
	public string CompanySizeCategory => CompanySize?.ToLower() switch
	{
		var size when size.Contains("small") => "Small",
		var size when size.Contains("medium") => "Medium", 
		var size when size.Contains("large") => "Large",
		_ => "Unknown"
	};

	public string SubscriptionTierColor => SubscriptionTier?.ToLower() switch
	{
		"corporate premium" => "#FFD700",
		"corporate standard" => "#C0C0C0",
		"corporate basic" => "#CD7F32",
		_ => "#6C757D"
	};

	public int CompanyAge => DateTime.Now.Year - EstablishedYear;
	public bool IsEstablished => CompanyAge >= 10;
	public bool HasActiveJobs => ActiveJobPostings > 0;

	internal OrganizationData ToData() => new()
	{
		Id = Id,
		Name = Name,
		Industry = Industry,
		Location = Location.ToData(),
		CompanySize = CompanySize,
		LogoUrl = LogoUrl,
		Description = Description,
		Website = Website,
		IsVerified = IsVerified,
		SubscriptionTier = SubscriptionTier,
		ActiveJobPostings = ActiveJobPostings,
		EmployeeCount = EmployeeCount,
		EstablishedYear = EstablishedYear,
		Benefits = Benefits.ToList(),
		WorkEnvironment = WorkEnvironment,
		LanguagesRequired = LanguagesRequired.ToList(),
		ContactInfo = ContactInfo.ToData()
	};
}

public partial record ContactInfo
{
	internal ContactInfo(ContactInfoData? contactInfoData)
	{
		Email = contactInfoData?.Email;
		Phone = contactInfoData?.Phone;
		Address = contactInfoData?.Address;
	}

	public string? Email { get; init; }
	public string? Phone { get; init; }
	public string? Address { get; init; }

	internal ContactInfoData ToData() => new()
	{
		Email = Email,
		Phone = Phone,
		Address = Address
	};
}
