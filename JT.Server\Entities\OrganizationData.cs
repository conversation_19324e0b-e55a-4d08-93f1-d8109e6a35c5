namespace JT.Server.Entities;

public class OrganizationData
{
	public string? Id { get; set; }
	public string? Name { get; set; }
	public string? Industry { get; set; }
	public LocationData? Location { get; set; }
	public string? CompanySize { get; set; }
	public string? LogoUrl { get; set; }
	public string? Description { get; set; }
	public string? Website { get; set; }
	public bool? IsVerified { get; set; }
	public string? SubscriptionTier { get; set; }
	public int? ActiveJobPostings { get; set; }
	public int? EmployeeCount { get; set; }
	public int? EstablishedYear { get; set; }
	public List<string>? Benefits { get; set; }
	public string? WorkEnvironment { get; set; }
	public List<string>? LanguagesRequired { get; set; }
	public ContactInfoData? ContactInfo { get; set; }
}

public class ContactInfoData
{
	public string? Email { get; set; }
	public string? Phone { get; set; }
	public string? Address { get; set; }
}
