using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ReferralController : ChefsControllerBase
{
	private readonly ILogger<ReferralController> _logger;

	public ReferralController(ILogger<ReferralController> logger)
	{
		_logger = logger;
	}

	[HttpGet]
	public async Task<ActionResult<IEnumerable<ReferralData>>> GetReferrals()
	{
		try
		{
			var referrals = await GetMockData<ReferralData>("Referrals.json");
			return Ok(referrals);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting referrals");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("{id}")]
	public async Task<ActionResult<ReferralData>> GetReferral(string id)
	{
		try
		{
			var referrals = await GetMockData<ReferralData>("Referrals.json");
			var referral = referrals.FirstOrDefault(r => r.Id == id);
			
			if (referral == null)
			{
				return NotFound();
			}

			return Ok(referral);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting referral {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost]
	public async Task<ActionResult<ReferralData>> CreateReferral(ReferralData referral)
	{
		try
		{
			referral.Id = Guid.NewGuid().ToString();
			referral.CreatedAt = DateTime.UtcNow;
			referral.Status = "pending";
			referral.TokensEarned = 0;
			referral.BonusPaid = false;

			var referrals = await GetMockData<ReferralData>("Referrals.json");
			var updatedList = referrals.Append(referral).ToList();
			await SaveMockData("Referrals.json", updatedList);

			return CreatedAtAction(nameof(GetReferral), new { id = referral.Id }, referral);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error creating referral");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPut("{id}")]
	public async Task<ActionResult<ReferralData>> UpdateReferral(string id, ReferralData referral)
	{
		try
		{
			var referrals = await GetMockData<ReferralData>("Referrals.json");
			var existingIndex = referrals.FindIndex(r => r.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			referral.Id = id;
			referrals[existingIndex] = referral;
			await SaveMockData("Referrals.json", referrals);

			return Ok(referral);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error updating referral {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("user/{userId}")]
	public async Task<ActionResult<IEnumerable<ReferralData>>> GetUserReferrals(Guid userId)
	{
		try
		{
			var referrals = await GetMockData<ReferralData>("Referrals.json");
			var userReferrals = referrals.Where(r => r.ReferrerId == userId).ToList();
			return Ok(userReferrals);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting referrals for user {UserId}", userId);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("code/{referralCode}")]
	public async Task<ActionResult<ReferralData>> GetByReferralCode(string referralCode)
	{
		try
		{
			var referrals = await GetMockData<ReferralData>("Referrals.json");
			var referral = referrals.FirstOrDefault(r => r.ReferralCode == referralCode);
			
			if (referral == null)
			{
				return NotFound();
			}

			return Ok(referral);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting referral by code {ReferralCode}", referralCode);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("{id}/complete")]
	public async Task<ActionResult<ReferralData>> CompleteReferral(string id, [FromBody] CompleteReferralRequest request)
	{
		try
		{
			var referrals = await GetMockData<ReferralData>("Referrals.json");
			var existingIndex = referrals.FindIndex(r => r.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			var referral = referrals[existingIndex];
			referral.ReferredUserId = request.ReferredUserId;
			referral.Status = "completed";
			referral.CompletedAt = DateTime.UtcNow;
			referral.TokensEarned = 25; // Standard bonus
			
			await SaveMockData("Referrals.json", referrals);

			return Ok(referral);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error completing referral {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}
}

public class CompleteReferralRequest
{
	public Guid ReferredUserId { get; set; }
}
