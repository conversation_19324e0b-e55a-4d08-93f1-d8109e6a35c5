﻿<Page x:Class="JT.Presentation.JobTransferPage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:local="using:JT.Presentation"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:models="using:JT.Business.Models"
	  xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
	  xmlns:not_mobile="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:toolkit="using:Uno.UI.Toolkit"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:uer="using:Uno.Extensions.Reactive.UI"
	  xmlns:ut="using:Uno.Themes"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  xmlns:win="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  NavigationCacheMode="Enabled"
	  mc:Ignorable="d"
	  Background="{ThemeResource BackgroundBrush}">

	<Page.Resources>
		<muxc:UniformGridLayout x:Key="ResponsiveGridLayout"
								ItemsStretch="Fill"
								MaximumRowsOrColumns="{utu:Responsive Normal=2,
																	  Wide=7}"
								MinColumnSpacing="{utu:Responsive Normal=6,
																  Wide=15}"
								MinItemWidth="{utu:Responsive Normal=155,
															  Wide=240}"
								MinRowSpacing="{utu:Responsive Normal=6,
															   Wide=15}" />
	</Page.Resources>

	<utu:AutoLayout>
        <utu:NavigationBar Content="{Binding JobTransfer.Name}"
						   utu:AutoLayout.PrimaryAlignment="Auto" />
		<uer:FeedView utu:AutoLayout.PrimaryAlignment="Stretch"
					  Source="{Binding JobTransfer}">
			<DataTemplate>
				<utu:AutoLayout utu:AutoLayout.PrimaryAlignment="Stretch"
								Padding="16">
					<ScrollViewer x:Name="TransfersScrollViewer"
								  utu:AutoLayout.PrimaryAlignment="Stretch"
								  HorizontalScrollMode="Disabled"
								  VerticalScrollBarVisibility="Hidden">
						<utu:AutoLayout>
							<TextBlock Padding="0,0,0,16"
									   HorizontalAlignment="Stretch"
									   utu:AutoLayout.CounterAlignment="Center"
									   Style="{StaticResource BodyLarge}">
								<Run Text="{Binding Data.TransferRequests.Count}" />
								<Run Text="results" />
							</TextBlock>
                            <muxc:ItemsRepeater uen:Navigation.Request="TransferRequestDetails"
												ItemTemplate="{StaticResource TransferTemplate}"
												ItemsSource="{Binding Data.TransferRequests}"
												Layout="{StaticResource ResponsiveGridLayout}" />
						</utu:AutoLayout>
					</ScrollViewer>
					<Button x:Name="FabButton"
							HorizontalAlignment="Right"
							VerticalAlignment="Bottom"
							Content="Edit"
							uen:Navigation.Data="{Binding Data}"
							uen:Navigation.Request="UpdateJobTransfer"
							utu:AutoLayout.IsIndependentLayout="True"
							Style="{StaticResource JTsFabButtonStyle}">
						<ut:ControlExtensions.Icon>
							<PathIcon Data="{StaticResource Icon_Edit}" />
						</ut:ControlExtensions.Icon>
					</Button>
				</utu:AutoLayout>
			</DataTemplate>
		</uer:FeedView>
	</utu:AutoLayout>
</Page>
