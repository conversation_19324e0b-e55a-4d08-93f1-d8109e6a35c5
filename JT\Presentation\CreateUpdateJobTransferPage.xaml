﻿<Page x:Class="JT.Presentation.CreateUpdateJobTransferPage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:converters="using:JT.Converters"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:local="using:JT.Presentation"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:models="using:JT.Business.Models"
	  xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
	  xmlns:not_mobile="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:toolkit="using:Uno.UI.Toolkit"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:uer="using:Uno.Extensions.Reactive.UI"
	  xmlns:ut="using:Uno.Themes"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  xmlns:win="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  mc:Ignorable="d"
	  Background="{ThemeResource BackgroundBrush}">

	<Page.Resources>
		<muxc:UniformGridLayout x:Key="ResponsiveGridLayout"
								ItemsStretch="Fill"
								MaximumRowsOrColumns="{utu:Responsive Normal=2,
																	  Wide=7}"
								MinColumnSpacing="{utu:Responsive Normal=6,
																  Wide=15}"
								MinItemWidth="{utu:Responsive Normal=155,
															  Wide=240}"
								MinRowSpacing="{utu:Responsive Normal=6,
															   Wide=15}" />

		<DataTemplate x:Key="JobTransferTransferTemplate"
					  x:DataType="models:TransferRequest">
			<ListViewItem x:Name="TransferTemplateCard"
						  Background="{ThemeResource SurfaceBrush}"
						  CornerRadius="4">
				<ListViewItem.ContentTemplate>
					<DataTemplate>
						<utu:AutoLayout CornerRadius="4">
							<Border x:Name="ImageLayout"
									Height="144">
								<Image Source="{Binding ImageUrl}"
									   Stretch="UniformToFill"
									   VerticalAlignment="Center"
									   HorizontalAlignment="Center" />
							</Border>

							<utu:AutoLayout>
								<utu:AutoLayout Spacing="16"
												Orientation="Horizontal"
												Padding="16">
									<utu:AutoLayout PrimaryAxisAlignment="Center"
													Spacing="4"
													utu:AutoLayout.CounterAlignment="Center"
													utu:AutoLayout.PrimaryAlignment="Stretch">
										<TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
												   Style="{StaticResource TitleSmall}"
												   Text="{Binding Name}"
												   TextWrapping="NoWrap"
												   TextTrimming="CharacterEllipsis" />
										<TextBlock Foreground="{ThemeResource OnSurfaceMediumBrush}"
												   Style="{StaticResource CaptionMedium}"
												   utu:AutoLayout.PrimaryAlignment="Stretch"
												   Text="{Binding TimeCal}"
												   TextWrapping="NoWrap"
												   TextTrimming="CharacterEllipsis" />
									</utu:AutoLayout>
									<utu:AutoLayout PrimaryAxisAlignment="End"
													CounterAxisAlignment="Start"
													Orientation="Horizontal">
										<Border Visibility="{Binding Converter={StaticResource TrueToCollapsed}, ElementName=TransferTemplateCard, Path=IsSelected}">
											<PathIcon Data="{StaticResource Icon_Heart}"
													  DataContext="{utu:AncestorBinding AncestorType=uer:FeedView}"
													  Foreground="{ThemeResource OnSurfaceBrush}" />
										</Border>
										<Border Visibility="{Binding ElementName=TransferTemplateCard, Path=IsSelected}">
											<PathIcon Data="{StaticResource Icon_Heart_Filled}"
													  DataContext="{utu:AncestorBinding AncestorType=uer:FeedView}"
													  Foreground="{ThemeResource PrimaryBrush}" />
										</Border>
									</utu:AutoLayout>
								</utu:AutoLayout>
							</utu:AutoLayout>
						</utu:AutoLayout>
					</DataTemplate>
				</ListViewItem.ContentTemplate>
			</ListViewItem>
		</DataTemplate>
	</Page.Resources>

	<utu:AutoLayout>
		<utu:AutoLayout utu:AutoLayout.PrimaryAlignment="Stretch">
			<utu:NavigationBar Content="{Binding Title}" />

			<!-- It is necessary to disable the HorizontalScrollMode because of this issue: https://github.com/unoplatform/uno/issues/12871 -->
			<ScrollViewer utu:AutoLayout.PrimaryAlignment="Stretch"
						  HorizontalScrollMode="Disabled"
						  VerticalScrollBarVisibility="Hidden">
				<utu:AutoLayout Margin="16"
								Spacing="16">
                    <TextBox PlaceholderText="JobTransfer name"
							 Style="{StaticResource OutlinedTextBoxStyle}"
							 Text="{Binding JobTransfer.Name, Mode=TwoWay}" />
					<TextBlock Margin="0,16,0,8"
							   Style="{StaticResource BodyLarge}"
							   Text="{Binding SubTitle}" />
					<uer:FeedView utu:AutoLayout.CounterAlignment="Stretch"
								  utu:AutoLayout.PrimaryAlignment="Stretch"
								  Source="{Binding TransferRequests}">
						<DataTemplate>
							<ScrollViewer>
								<muxc:ItemsRepeater Grid.Row="1"
													ItemTemplate="{StaticResource JobTransferTransferTemplate}"
													ItemsSource="{Binding Data}"
													utu:ItemsRepeaterExtensions.SelectionMode="Multiple"
													utu:ItemsRepeaterExtensions.SupportsIncrementalLoading="True"
													Layout="{StaticResource ResponsiveGridLayout}" />
							</ScrollViewer>
						</DataTemplate>
					</uer:FeedView>
				</utu:AutoLayout>
			</ScrollViewer>
		</utu:AutoLayout>
		<utu:AutoLayout Height="108"
						Padding="16,24"
						HorizontalAlignment="Stretch"
						Background="{ThemeResource SurfaceBrush}"
						Orientation="Horizontal"
						PrimaryAxisAlignment="Start"
						Spacing="16">
			<Button uen:Navigation.Request="-"
					utu:AutoLayout.PrimaryAlignment="Stretch"
					Content="Cancel"
					Style="{StaticResource JTsPrimaryButtonStyle}" />
			<Button utu:AutoLayout.PrimaryAlignment="Stretch"
					Command="{Binding Submit}"
					Content="{Binding SaveButtonContent}"
					Style="{StaticResource JTsPrimaryButtonStyle}" />
		</utu:AutoLayout>
	</utu:AutoLayout>
</Page>
