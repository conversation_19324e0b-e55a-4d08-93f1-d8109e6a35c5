// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace JT.Content.Client.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class EmployerResponseData : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The companyName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CompanyName { get; set; }
#nullable restore
#else
        public string CompanyName { get; set; }
#endif
        /// <summary>The employerId property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? EmployerId { get; set; }
#nullable restore
#else
        public string EmployerId { get; set; }
#endif
        /// <summary>The id property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Id { get; set; }
#nullable restore
#else
        public string Id { get; set; }
#endif
        /// <summary>The interviewScheduled property</summary>
        public bool? InterviewScheduled { get; set; }
        /// <summary>The message property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Message { get; set; }
#nullable restore
#else
        public string Message { get; set; }
#endif
        /// <summary>The offeredSalary property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? OfferedSalary { get; set; }
#nullable restore
#else
        public string OfferedSalary { get; set; }
#endif
        /// <summary>The responseDate property</summary>
        public DateTimeOffset? ResponseDate { get; set; }
        /// <summary>The status property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Status { get; set; }
#nullable restore
#else
        public string Status { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::JT.Content.Client.Models.EmployerResponseData"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::JT.Content.Client.Models.EmployerResponseData CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::JT.Content.Client.Models.EmployerResponseData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "companyName", n => { CompanyName = n.GetStringValue(); } },
                { "employerId", n => { EmployerId = n.GetStringValue(); } },
                { "id", n => { Id = n.GetStringValue(); } },
                { "interviewScheduled", n => { InterviewScheduled = n.GetBoolValue(); } },
                { "message", n => { Message = n.GetStringValue(); } },
                { "offeredSalary", n => { OfferedSalary = n.GetStringValue(); } },
                { "responseDate", n => { ResponseDate = n.GetDateTimeOffsetValue(); } },
                { "status", n => { Status = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("companyName", CompanyName);
            writer.WriteStringValue("employerId", EmployerId);
            writer.WriteStringValue("id", Id);
            writer.WriteBoolValue("interviewScheduled", InterviewScheduled);
            writer.WriteStringValue("message", Message);
            writer.WriteStringValue("offeredSalary", OfferedSalary);
            writer.WriteDateTimeOffsetValue("responseDate", ResponseDate);
            writer.WriteStringValue("status", Status);
        }
    }
}
#pragma warning restore CS0618
