using JT.Business.Services.TransferRequests;

namespace JT.Presentation;

public partial record SearchModel
{
	private readonly INavigator _navigator;
	private readonly ITransferRequestService _transferRequestService;
	private readonly IMessenger _messenger;

	public SearchModel(SearchFilter? filter, INavigator navigator, ITransferRequestService transferRequestService, IMessenger messenger)
	{
		_navigator = navigator;
        _transferRequestService = transferRequestService;
		_messenger = messenger;

		Filter = State.Value(this, () => filter ?? new SearchFilter())
			.Observe(_messenger, f => f);
	}

	public IState<string> Term => State<string>.Value(this, () => string.Empty)
		.Observe(_messenger, t => t);

	public IState<SearchFilter> Filter { get; }
	public IListState<TransferRequest> Results => ListState.FromFeed(this, Feed
		.Combine(Term, Filter)
		.SelectAsync(Search)
		.AsListFeed())
		.Observe(_messenger, r => r.Id);

	public IFeed<bool> Searched => Feed.Combine(Filter, Term).Select(GetSearched);

	public IFeed<bool> HasFilter => Filter.Select(f => f.<PERSON>);

	public IListFeed<TransferRequest> Recommended => ListFeed.Async(_transferRequestService.GetRecommended);

	public IListFeed<TransferRequest> FromJTs => ListFeed.Async(_transferRequestService.GetFromJTs);

	public IListFeed<string> SearchHistory => ListFeed.Async(async ct => _transferRequestService.GetSearchHistory());

	public async ValueTask ApplyHistory(string term) => await Term.SetAsync(term);

	private async ValueTask<IImmutableList<TransferRequest>> Search((string term, SearchFilter filter) inputs, CancellationToken ct)
	{
		var searchedTransfers = await _transferRequestService.Search(inputs.term, inputs.filter, ct);
		return searchedTransfers.Where(inputs.filter.Match).ToImmutableList();
	}

	private bool GetSearched((SearchFilter filter, string term) inputs) => inputs.filter.HasFilter || !inputs.term.IsNullOrEmpty();

	public async ValueTask SearchPopular() =>
		await _navigator.NavigateViewModelAsync<SearchModel>(this, data: new SearchFilter(FilterGroup: FilterGroup.Popular));

	public async ValueTask ResetFilters() =>
		await Filter.UpdateAsync(current => new SearchFilter());
}
