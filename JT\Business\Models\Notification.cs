using NotificationData = JT.Content.Client.Models.NotificationData;

namespace JT.Business.Models;

public partial record Notification
{
	internal Notification(NotificationData notificationData)
	{
		Id = notificationData.Id ?? string.Empty;
		UserId = notificationData.UserId ?? Guid.Empty;
		Title = notificationData.Title ?? string.Empty;
		Body = notificationData.Body ?? notificationData.Description ?? string.Empty;
		Type = notificationData.Type ?? string.Empty;
		Priority = notificationData.Priority ?? "normal";
		Timestamp = notificationData.Timestamp?.DateTime ?? notificationData.Date?.DateTime ?? DateTime.MinValue;
		Read = notificationData.Read ?? notificationData.IsRead ?? false;
		ReadAt = notificationData.ReadAt?.DateTime;
		ActionUrl = notificationData.ActionUrl;
		ImageUrl = notificationData.ImageUrl;
	}

	public string Id { get; init; }
	public Guid UserId { get; init; }
	public string Title { get; init; }
	public string Body { get; init; }
	public string Type { get; init; }
	public string Priority { get; init; } // low, normal, high, critical
	public DateTime Timestamp { get; init; }
	public bool Read { get; init; }
	public DateTime? ReadAt { get; init; }
	public string? ActionUrl { get; init; }
	public string? ImageUrl { get; init; }

	// Computed properties
	public string PriorityColor => Priority switch
	{
		"critical" => "#DC3545",
		"high" => "#FD7E14",
		"normal" => "#0D6EFD",
		"low" => "#6C757D",
		_ => "#6C757D"
	};

	public string TypeIcon => Type switch
	{
		"transfer_request" => "📋",
		"payment" => "💳",
		"subscription" => "⭐",
		"referral" => "👥",
		"system" => "⚙️",
		"employer_response" => "🏢",
		_ => "📢"
	};

	public bool IsRecent => (DateTime.UtcNow - Timestamp).TotalHours <= 24;
	public bool IsUrgent => Priority is "high" or "critical";

	public string TimeAgo
	{
		get
		{
			var timeSpan = DateTime.UtcNow - Timestamp;
			return timeSpan.TotalMinutes switch
			{
				< 1 => "Just now",
				< 60 => $"{(int)timeSpan.TotalMinutes}m ago",
				< 1440 => $"{(int)timeSpan.TotalHours}h ago",
				< 10080 => $"{(int)timeSpan.TotalDays}d ago",
				_ => Timestamp.ToString("MMM dd, yyyy")
			};
		}
	}

	// Legacy properties for backward compatibility
	public string? Description => Body;
	public DateTime Date => Timestamp;

	internal NotificationData ToData() => new()
	{
		Id = Id,
		UserId = UserId,
		Title = Title,
		Body = Body,
		Description = Body, // For backward compatibility
		Type = Type,
		Priority = Priority,
		Timestamp = Timestamp,
		Date = Timestamp, // For backward compatibility
		Read = Read,
		IsRead = Read, // For backward compatibility
		ReadAt = ReadAt,
		ActionUrl = ActionUrl,
		ImageUrl = ImageUrl
	};
}
