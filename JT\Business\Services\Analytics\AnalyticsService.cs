using JT.Client.Api;
using System.Text.Json;

namespace JT.Business.Services.Analytics;

public class AnalyticsService : IAnalyticsService
{
	private readonly JTServiceClient api;

	public AnalyticsService(JTServiceClient api)
	{
		this.api = api;
	}

	public async ValueTask<IImmutableList<Analytics>> GetAll(CancellationToken ct = default)
	{
		try
		{
			var analyticsData = await api.Api.Analytics.GetAsync(cancellationToken: ct);
			return analyticsData?.Select(a => new Analytics(a)).ToImmutableList() ?? ImmutableList<Analytics>.Empty;
		}
		catch
		{
			return ImmutableList<Analytics>.Empty;
		}
	}

	public async ValueTask<Analytics?> GetById(string id, CancellationToken ct = default)
	{
		try
		{
			var analyticsData = await api.Api.Analytics[id].GetAsync(cancellationToken: ct);
			return analyticsData != null ? new Analytics(analyticsData) : null;
		}
		catch
		{
			return null;
		}
	}

	public async ValueTask<Analytics?> GetByType(string type, CancellationToken ct = default)
	{
		var allAnalytics = await GetAll(ct);
		return allAnalytics.FirstOrDefault(a => string.Equals(a.Type, type, StringComparison.OrdinalIgnoreCase));
	}

	public async ValueTask<IImmutableList<LocationAnalytics>> GetPopularLocations(CancellationToken ct = default)
	{
		try
		{
			var analytics = await GetByType("popular_locations", ct);
			if (analytics?.Data is JsonElement jsonElement && jsonElement.TryGetProperty("locations", out var locationsElement))
			{
				var locations = JsonSerializer.Deserialize<LocationAnalytics[]>(locationsElement.GetRawText());
				return locations?.ToImmutableList() ?? ImmutableList<LocationAnalytics>.Empty;
			}
			return ImmutableList<LocationAnalytics>.Empty;
		}
		catch
		{
			return ImmutableList<LocationAnalytics>.Empty;
		}
	}

	public async ValueTask<IImmutableList<SkillAnalytics>> GetTrendingSkills(CancellationToken ct = default)
	{
		try
		{
			var analytics = await GetByType("trending_skills", ct);
			if (analytics?.Data is JsonElement jsonElement && jsonElement.TryGetProperty("skills", out var skillsElement))
			{
				var skills = JsonSerializer.Deserialize<SkillAnalytics[]>(skillsElement.GetRawText());
				return skills?.ToImmutableList() ?? ImmutableList<SkillAnalytics>.Empty;
			}
			return ImmutableList<SkillAnalytics>.Empty;
		}
		catch
		{
			return ImmutableList<SkillAnalytics>.Empty;
		}
	}

	public async ValueTask<IImmutableList<SalaryAnalytics>> GetSalaryTrends(CancellationToken ct = default)
	{
		try
		{
			var analytics = await GetByType("salary_trends", ct);
			if (analytics?.Data is JsonElement jsonElement && jsonElement.TryGetProperty("industries", out var industriesElement))
			{
				var industries = JsonSerializer.Deserialize<SalaryAnalytics[]>(industriesElement.GetRawText());
				return industries?.ToImmutableList() ?? ImmutableList<SalaryAnalytics>.Empty;
			}
			return ImmutableList<SalaryAnalytics>.Empty;
		}
		catch
		{
			return ImmutableList<SalaryAnalytics>.Empty;
		}
	}

	public async ValueTask<Analytics> UpdateAnalytics(string type, object data, CancellationToken ct = default)
	{
		try
		{
			var analyticsData = new AnalyticsData
			{
				Id = $"analytics-{type}",
				Type = type,
				Data = data,
				LastUpdated = DateTime.UtcNow
			};

			var updatedData = await api.Api.Analytics.PostAsync(analyticsData, cancellationToken: ct);
			return new Analytics(updatedData);
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Failed to update analytics: {ex.Message}", ex);
		}
	}

	public async ValueTask<bool> RefreshAnalytics(string type, CancellationToken ct = default)
	{
		try
		{
			// This would typically trigger a background job to recalculate analytics
			// For now, just update the LastUpdated timestamp
			var analytics = await GetByType(type, ct);
			if (analytics != null)
			{
				await UpdateAnalytics(type, analytics.Data!, ct);
				return true;
			}
			return false;
		}
		catch
		{
			return false;
		}
	}
}
