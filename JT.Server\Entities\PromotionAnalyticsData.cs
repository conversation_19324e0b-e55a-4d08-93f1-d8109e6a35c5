namespace JT.Server.Entities;

public class PromotionAnalyticsData
{
	public string? Id { get; set; }
	public string? PromotionId { get; set; }
	public DateTime? Date { get; set; }
	public int? Views { get; set; }
	public int? Clicks { get; set; }
	public int? Conversions { get; set; }
	public int? Impressions { get; set; }
	public int? UniqueViews { get; set; }
	public decimal? BounceRate { get; set; } // Percentage
	public decimal? AverageTimeSpent { get; set; } // Seconds
	public decimal? CostSpent { get; set; }
	public decimal? Revenue { get; set; }
	public Dictionary<string, int>? DeviceTypes { get; set; } // mobile, desktop, tablet
	public Dictionary<string, int>? LocationData { get; set; } // city/governorate breakdown
	public Dictionary<string, int>? AgeGroups { get; set; } // age group breakdown
	public Dictionary<string, int>? TrafficSources { get; set; } // direct, search, social, etc.
}
