﻿<Page x:Class="JT.Presentation.SearchPage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:android="http://uno.ui/android"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:data="using:JT.Business.Models"
	  xmlns:ios="http://uno.ui/ios"
	  xmlns:local="using:JT.Presentation"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:uer="using:Uno.Extensions.Reactive.UI"
	  xmlns:ut="using:Uno.Themes"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  utu:StatusBar.Background="{ThemeResource SurfaceInverseBrush}"
	  utu:StatusBar.Foreground="AutoInverse"
	  NavigationCacheMode="Enabled"
	  mc:Ignorable="d android ios"
	  Background="{ThemeResource BackgroundBrush}">

	<Page.Resources>
		<DataTemplate x:Key="EmptyTemplate">
			<utu:AutoLayout Background="{ThemeResource BackgroundBrush}">
				<utu:AutoLayout Padding="32,0"
								utu:AutoLayout.PrimaryAlignment="Stretch"
								PrimaryAxisAlignment="Center"
								Spacing="24">
					<utu:AutoLayout Width="72"
									Height="72"
									utu:AutoLayout.CounterAlignment="Center">
						<utu:AutoLayout Margin="3,0,-3,0"
										HorizontalAlignment="Stretch"
										VerticalAlignment="Stretch"
										utu:AutoLayout.IsIndependentLayout="True"
										PrimaryAxisAlignment="Center">
							<BitmapIcon Width="72"
										Height="72"
										utu:AutoLayout.CounterAlignment="Center"
										UriSource="{ThemeResource Empty_Box}" />
						</utu:AutoLayout>
					</utu:AutoLayout>
					<TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
							   Style="{StaticResource TitleLarge}"
							   Text="No Results Found"
							   TextAlignment="Center"
							   TextWrapping="Wrap" />
					<TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
							   Style="{StaticResource TitleMedium}"
							   Text="We couldn't find any transferRequests matching your search. Try adjusting your keywords or filters, or explore our popular categories to discover something delicious!"
							   TextAlignment="Center"
							   TextWrapping="Wrap" />
					<Button Padding="12,10,16,10"
							HorizontalContentAlignment="Center"
							VerticalContentAlignment="Center"
							utu:AutoLayout.CounterAlignment="Center"
							Command="{Binding SearchPopular}"
							Content="View popular transferRequests"
							CornerRadius="20"
							Foreground="{ThemeResource PrimaryBrush}"
							Style="{StaticResource TextButtonStyle}">
						<ut:ControlExtensions.Icon>
							<PathIcon Data="{StaticResource Icon_Star_Outline}"
									  Foreground="{ThemeResource PrimaryBrush}" />
						</ut:ControlExtensions.Icon>
					</Button>
				</utu:AutoLayout>
			</utu:AutoLayout>
		</DataTemplate>

		<muxc:UniformGridLayout x:Key="ResponsiveGridLayout"
								ItemsStretch="Fill"
								MaximumRowsOrColumns="{utu:Responsive Normal=2,
																	  Wide=8}"
								MinColumnSpacing="{utu:Responsive Normal=8,
																  Wide=16}"
								MinItemWidth="{utu:Responsive Normal=155,
															  Wide=240}"
								MinRowSpacing="{utu:Responsive Normal=8,
															   Wide=16}" />
	</Page.Resources>

	<utu:AutoLayout utu:AutoLayout.PrimaryAlignment="Stretch">

		<utu:NavigationBar>
			<utu:NavigationBar.Content>
				<Grid>
					<Image Source="{ThemeResource JTsAppSignature}"
						   HorizontalAlignment="Left"
						   Width="128"
						   Height="40" />
				</Grid>
			</utu:NavigationBar.Content>
			<utu:NavigationBar.PrimaryCommands>
				<AppBarButton uen:Navigation.Request="!Profile">
					<AppBarButton.Icon>
						<PathIcon Data="{StaticResource Icon_Person_Outline}" />
					</AppBarButton.Icon>
				</AppBarButton>
				<AppBarButton uen:Navigation.Request="!Notifications">
					<AppBarButton.Icon>
						<PathIcon Data="{StaticResource Icon_Notification_Bell}" />
					</AppBarButton.Icon>
				</AppBarButton>
			</utu:NavigationBar.PrimaryCommands>
		</utu:NavigationBar>

		<utu:AutoLayout Padding="{utu:Responsive Normal='16,24',
												 Wide='40,40,40,24'}"
						PrimaryAxisAlignment="Center"
						Spacing="24">
			<TextBox utu:CommandExtensions.Command="{Binding Search}"
					 Style="{StaticResource JTsPrimaryTextBoxStyle}"
					 CornerRadius="28"
					 PlaceholderText="Search"
					 Text="{Binding Term, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
				<ut:ControlExtensions.Icon>
					<PathIcon Data="{StaticResource Icon_Search}"
							  Foreground="{ThemeResource OnSurfaceMediumBrush}" />
				</ut:ControlExtensions.Icon>
			</TextBox>

			<utu:AutoLayout Justify="SpaceBetween"
							Orientation="Horizontal"
							CounterAxisAlignment="Center">
				<TextBlock Style="{StaticResource BodyLarge}"
						   Text="{Binding Results.Count, Converter={StaticResource StringFormatter}, ConverterParameter='{}{0} results', UpdateSourceTrigger=PropertyChanged}" />
				<utu:AutoLayout Orientation="Horizontal">
					<Button utu:AutoLayout.PrimaryAlignment="Auto"
							Content="Clear filters"
							Visibility="{Binding HasFilter, Converter={StaticResource TrueToVisible}}"
							Command="{Binding ResetFilters}"
							Margin="0,0,8,0"
							CornerRadius="20"
							Foreground="{ThemeResource PrimaryBrush}"
							Style="{StaticResource TextButtonStyle}">
						<ut:ControlExtensions.Icon>
							<PathIcon Data="{StaticResource Icon_Close}"
									  Foreground="{ThemeResource PrimaryBrush}" />
						</ut:ControlExtensions.Icon>
					</Button>
					<Button x:Name="FiltersButton"
							uen:Navigation.Data="{Binding Filter.Value, Mode=TwoWay}"
							uen:Navigation.Request="!Filter"
							Content="Filters"
							CornerRadius="20"
							Foreground="{ThemeResource PrimaryBrush}"
							Style="{StaticResource TextButtonStyle}">
						<ut:ControlExtensions.Icon>
							<PathIcon Data="{StaticResource Icon_Tune}"
									  Foreground="{ThemeResource PrimaryBrush}" />
						</ut:ControlExtensions.Icon>
					</Button>
				</utu:AutoLayout>
			</utu:AutoLayout>
		</utu:AutoLayout>


		<uer:FeedView x:Name="SearchFeed"
					  NoneTemplate="{StaticResource EmptyTemplate}"
					  utu:AutoLayout.PrimaryAlignment="Stretch"
					  Source="{Binding Results}">
			<DataTemplate>
				<ScrollViewer VerticalScrollBarVisibility="Hidden">
					<muxc:ItemsRepeater x:Name="SearchRepeater"
										Margin="{utu:Responsive Normal='16,0,16,16',
																Wide='40,0,40,40'}"
										uen:Navigation.Request="TransferRequestDetails"
										ItemTemplate="{StaticResource TransferTemplate}"
										ItemsSource="{Binding Data}"
										Layout="{StaticResource ResponsiveGridLayout}" />
				</ScrollViewer>

			</DataTemplate>
		</uer:FeedView>
	</utu:AutoLayout>
</Page>
