namespace JT.Server.Apis;

/// <summary>
/// Controller for managing skill-related operations.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SkillController : JTControllerBase
{
    private readonly string _skillsFilePath = "Skills.json";

    /// <summary>
    /// Logger for the SkillController.
    /// </summary>
    private readonly ILogger<SkillController> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="SkillController"/> class.
    /// </summary>
    /// <param name="logger">The logger instance for logging operations.</param>
    public SkillController(ILogger<SkillController> logger) => _logger = logger;


    /// <summary>
    /// Retrieves all skills.
    /// </summary>
    /// <returns>A list of all skills.</returns>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<SkillData>), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<SkillData>> GetSkills()
    {
        try
        {
            var skills = LoadData<List<SkillData>>(_skillsFilePath);
            return Ok(skills.ToImmutableList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting skills");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves a specific skill by its ID.
    /// </summary>
    /// <param name="id">The ID of the skill to retrieve.</param>
    /// <returns>The skill with the specified ID.</returns>
    [HttpGet("{id}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(SkillData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<SkillData> GetSkill(int id)
    {
        try
        {
            var skills = LoadData<List<SkillData>>(_skillsFilePath);
            var skill = skills.FirstOrDefault(s => s.Id == id);

            if (skill == null)
            {
                return NotFound();
            }

            return Ok(skill);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting skill {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }


    /// <summary>
    /// Retrieves skills by category.
    /// </summary>
    /// <param name="category">The category to filter skills by.</param>
    /// <returns>A list of skills in the specified category.</returns>
    [HttpGet("category/{category}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(SkillData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<SkillData>> GetSkillsByCategory(string category)
    {
        try
        {
            var skills = LoadData<List<SkillData>>(_skillsFilePath);
            var filteredSkills = skills.Where(s =>
                string.Equals(s.Category, category, StringComparison.OrdinalIgnoreCase)).ToList();
            return Ok(filteredSkills.ToImmutableList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting skills by category {Category}", category);
            return StatusCode(500, "Internal server error");
        }
    }


    /// <summary>
    /// Retrieves skills by industry.
    /// </summary>
    /// <param name="industry">The industry to filter skills by.</param>
    /// <returns>A list of skills in the specified industry.</returns>
    [HttpGet("industry/{industry}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(SkillData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<SkillData>> GetSkillsByIndustry(string industry)
    {
        try
        {
            var skills = LoadData<List<SkillData>>(_skillsFilePath);
            var filteredSkills = skills.Where(s =>
                string.Equals(s.Industry, industry, StringComparison.OrdinalIgnoreCase)).ToList();
            return Ok(filteredSkills.ToImmutableList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting skills by industry {Industry}", industry);
            return StatusCode(500, "Internal server error");
        }
    }


    /// <summary>
    /// Retrieves the top trending skills based on popularity score.
    /// </summary>
    /// <returns>A list of trending skills.</returns>
    [HttpGet("trending")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(SkillData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<SkillData>> GetTrendingSkills()
    {
        try
        {
            var skills = LoadData<List<SkillData>>(_skillsFilePath);
            var trendingSkills = skills
                .Where(s => s.PopularityScore >= 80)
                .OrderByDescending(s => s.PopularityScore)
                .Take(10)
                .ToList();
            return Ok(trendingSkills.ToImmutableList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting trending skills");
            return StatusCode(500, "Internal server error");
        }
    }
}
