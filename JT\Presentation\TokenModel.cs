using JT.Business.Services.Tokens;
using JT.Business.Services.Users;

namespace JT.Presentation;

public partial record TokenModel
{
	private readonly INavigator _navigator;
	private readonly ITokenService _tokenService;
	private readonly IUserService _userService;
	private readonly IMessenger _messenger;

	public TokenModel(
		INavigator navigator,
		ITokenService tokenService,
		IUserService userService,
		IMessenger messenger)
	{
		_navigator = navigator;
		_tokenService = tokenService;
		_userService = userService;
		_messenger = messenger;
	}

	public IFeed<User> CurrentUser => _userService.User;

	public IState<int> TokenBalance => State.Async(this, GetTokenBalance);

	public IListFeed<TokenTransaction> RecentTransactions => ListFeed.Async(GetRecentTransactions);

	[ObservableProperty]
	private int _redeemAmount;

	[ObservableProperty]
	private string _redeemType = "boost";

	[ObservableProperty]
	private bool _isProcessingRedemption;

	private async ValueTask<int> GetTokenBalance(CancellationToken ct)
	{
		var user = await _userService.GetCurrent(ct);
		return user?.TokenBalance ?? 0;
	}

	private async ValueTask<IImmutableList<TokenTransaction>> GetRecentTransactions(CancellationToken ct)
	{
		var user = await _userService.GetCurrent(ct);
		if (user == null) return ImmutableList<TokenTransaction>.Empty;

		return await _tokenService.GetUserTransactions(user.Id, ct);
	}

	public async ValueTask RedeemTokens(CancellationToken ct)
	{
		if (IsProcessingRedemption || RedeemAmount <= 0) return;

		try
		{
			IsProcessingRedemption = true;
			
			var user = await _userService.GetCurrent(ct);
			if (user == null) return;

			// Check if user has sufficient balance
			var hasBalance = await _tokenService.HasSufficientBalance(user.Id, RedeemAmount, ct);
			if (!hasBalance)
			{
				await _messenger.Send(new ErrorMessage("Insufficient token balance"));
				return;
			}

			// Process redemption
			var description = RedeemType switch
			{
				"boost" => "Profile boost for increased visibility",
				"priority" => "Priority listing for transfer request",
				"featured" => "Featured placement on homepage",
				_ => "Token redemption"
			};

			await _tokenService.DeductTokens(user.Id, RedeemAmount, "redemption", description, ct);
			
			await _messenger.Send(new SuccessMessage($"Successfully redeemed {RedeemAmount} tokens for {description}"));
			
			// Reset form
			RedeemAmount = 0;
		}
		catch (Exception ex)
		{
			await _messenger.Send(new ErrorMessage($"Failed to redeem tokens: {ex.Message}"));
		}
		finally
		{
			IsProcessingRedemption = false;
		}
	}

	public async ValueTask InviteFriend(CancellationToken ct)
	{
		var user = await _userService.GetCurrent(ct);
		if (user?.ReferralCode != null)
		{
			var inviteText = $"Join JobTransfer app and earn tokens! Use my referral code: {user.ReferralCode}";
			// Use share service to share the referral
			await _navigator.NavigateRouteAsync(this, route: "/Main/-/Share", data: new { Text = inviteText }, cancellation: ct);
		}
	}

	public async ValueTask ViewReferralHistory(CancellationToken ct)
	{
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Referrals", cancellation: ct);
	}

	public async ValueTask ViewTokenEarningTips(CancellationToken ct)
	{
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/TokenTips", cancellation: ct);
	}

	public string GetTransactionIcon(TokenTransaction transaction)
	{
		return transaction.Type switch
		{
			"referral" => "👥",
			"purchase" => "💳",
			"bonus" => "🎁",
			"redemption" => "🔄",
			_ => "💰"
		};
	}

	public string GetRedemptionDescription(string type)
	{
		return type switch
		{
			"boost" => "Profile Boost (20 tokens) - Increase your profile visibility for 7 days",
			"priority" => "Priority Listing (50 tokens) - Feature your transfer request at the top",
			"featured" => "Featured Placement (100 tokens) - Get featured on the homepage for 3 days",
			_ => "Select a redemption option"
		};
	}

	public int GetRedemptionCost(string type)
	{
		return type switch
		{
			"boost" => 20,
			"priority" => 50,
			"featured" => 100,
			_ => 0
		};
	}

	public bool CanRedeem(string type)
	{
		var cost = GetRedemptionCost(type);
		var balance = TokenBalance.Value;
		return balance >= cost && cost > 0;
	}
}
