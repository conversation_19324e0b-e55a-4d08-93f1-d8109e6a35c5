namespace JT.Server.Types
{
    [Serializable]
    public class TimeSpanObject
    {
        /// <summary>The days property</summary>
        public int? Days { get; private set; }
        /// <summary>The hours property</summary>
        public int? Hours { get; private set; }
        /// <summary>The microseconds property</summary>
        public int? Microseconds { get; private set; }
        /// <summary>The milliseconds property</summary>
        public int? Milliseconds { get; private set; }
        /// <summary>The minutes property</summary>
        public int? Minutes { get; private set; }
        /// <summary>The nanoseconds property</summary>
        public int? Nanoseconds { get; private set; }
        /// <summary>The seconds property</summary>
        public int? Seconds { get; private set; }
        /// <summary>The ticks property</summary>
        public long? Ticks { get; set; }
        /// <summary>The totalDays property</summary>
        public double? TotalDays { get; private set; }
        /// <summary>The totalHours property</summary>
        public double? TotalHours { get; private set; }
        /// <summary>The totalMicroseconds property</summary>
        public double? TotalMicroseconds { get; private set; }
        /// <summary>The totalMilliseconds property</summary>
        public double? TotalMilliseconds { get; private set; }
        /// <summary>The totalMinutes property</summary>
        public double? TotalMinutes { get; private set; }
        /// <summary>The totalNanoseconds property</summary>
        public double? TotalNanoseconds { get; private set; }
        /// <summary>The totalSeconds property</summary>
        public double? TotalSeconds { get; private set; }
    }
}
