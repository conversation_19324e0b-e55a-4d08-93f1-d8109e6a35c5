﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
					xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

	<!-- Workaround for winui/windows -->
	<x:String x:Key="DummyResource">test</x:String>

	<Style x:Key="JTsPrimaryTextBoxStyle"
		   TargetType="TextBox"
		   BasedOn="{StaticResource OutlinedTextBoxStyle}">
		<Setter Property="PlaceholderForeground" Value="{ThemeResource OnSurfaceMediumBrush}" />
		<Setter Property="BorderBrush" Value="{ThemeResource OutlineVariantBrush}" />
		<Setter Property="BorderThickness" Value="1" />
		<Setter Property="CornerRadius" Value="4" />
		<Setter Property="Foreground" Value="{ThemeResource OnSurfaceBrush}" />
	</Style>
</ResourceDictionary>
