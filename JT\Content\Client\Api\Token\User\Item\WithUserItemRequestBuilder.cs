// <auto-generated/>
#pragma warning disable CS0618
using JT.Content.Client.Api.Token.User.Item.Add;
using JT.Content.Client.Api.Token.User.Item.Balance;
using JT.Content.Client.Api.Token.User.Item.Deduct;
using JT.Content.Client.Api.Token.User.Item.Transactions;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace JT.Content.Client.Api.Token.User.Item
{
    /// <summary>
    /// Builds and executes requests for operations under \api\Token\user\{userId}
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class WithUserItemRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The add property</summary>
        public global::JT.Content.Client.Api.Token.User.Item.Add.AddRequestBuilder Add
        {
            get => new global::JT.Content.Client.Api.Token.User.Item.Add.AddRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The balance property</summary>
        public global::JT.Content.Client.Api.Token.User.Item.Balance.BalanceRequestBuilder Balance
        {
            get => new global::JT.Content.Client.Api.Token.User.Item.Balance.BalanceRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The deduct property</summary>
        public global::JT.Content.Client.Api.Token.User.Item.Deduct.DeductRequestBuilder Deduct
        {
            get => new global::JT.Content.Client.Api.Token.User.Item.Deduct.DeductRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The transactions property</summary>
        public global::JT.Content.Client.Api.Token.User.Item.Transactions.TransactionsRequestBuilder Transactions
        {
            get => new global::JT.Content.Client.Api.Token.User.Item.Transactions.TransactionsRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.Token.User.Item.WithUserItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WithUserItemRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Token/user/{userId}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.Token.User.Item.WithUserItemRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public WithUserItemRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Token/user/{userId}", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
