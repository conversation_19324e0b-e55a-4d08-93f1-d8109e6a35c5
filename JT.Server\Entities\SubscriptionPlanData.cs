namespace JT.Server.Entities;

public class SubscriptionPlanData
{
	public string? Id { get; set; }
	public string? Name { get; set; }
	public string? DisplayName { get; set; }
	public decimal PriceOMR { get; set; }
	public string? PriceDisplay { get; set; }
	public string? Duration { get; set; }
	public string? Color { get; set; }
	public string? IconUrl { get; set; }
	public int MaxDestinations { get; set; }
	public bool PriorityListing { get; set; }
	public string? PriorityFrequency { get; set; }
	public bool WhatsAppAlerts { get; set; }
	public bool EmailAlerts { get; set; }
	public bool SMSAlerts { get; set; }
	public int InviteLimit { get; set; }
	public string? InviteLimitDisplay { get; set; }
	public double TokenMultiplier { get; set; }
	public bool AdFreeExperience { get; set; }
	public List<string>? Features { get; set; }
	public double ServiceFeePercentage { get; set; }
	public double PaymentGatewayFeePercentage { get; set; }
	public double TotalFeePercentage { get; set; }
	public bool IsPopular { get; set; }
	public string? Description { get; set; }
}
