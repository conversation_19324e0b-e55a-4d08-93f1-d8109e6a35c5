using JT.Client.Api;

namespace JT.Business.Services.Referrals;

public class ReferralService : IReferralService
{
	private readonly JTServiceClient api;

	public ReferralService(JTServiceClient api)
	{
		this.api = api;
	}

	public async ValueTask<IImmutableList<Referral>> GetUserReferrals(Guid userId, CancellationToken ct = default)
	{
		try
		{
			var referralsData = await api.Api.Referrals.GetAsync(cancellationToken: ct);
			return referralsData?
				.Where(r => r.ReferrerId == userId)
				.Select(r => new Referral(r))
				.ToImmutableList() ?? ImmutableList<Referral>.Empty;
		}
		catch
		{
			return ImmutableList<Referral>.Empty;
		}
	}

	public async ValueTask<Referral?> GetById(string id, CancellationToken ct = default)
	{
		try
		{
			var referralData = await api.Api.Referrals[id].GetAsync(cancellationToken: ct);
			return referralData != null ? new Referral(referralData) : null;
		}
		catch
		{
			return null;
		}
	}

	public async ValueTask<Referral> CreateReferral(Guid referrerId, string referralCode, string inviteeEmail, CancellationToken ct = default)
	{
		try
		{
			var referralData = new ReferralData
			{
				Id = Guid.NewGuid().ToString(),
				ReferrerId = referrerId,
				ReferralCode = referralCode,
				ReferredUserEmail = inviteeEmail,
				Status = "pending",
				TokensEarned = 0,
				CreatedAt = DateTime.UtcNow,
				BonusPaid = false,
				InvitationSent = true
			};

			var createdData = await api.Api.Referrals.PostAsync(referralData, cancellationToken: ct);
			return new Referral(createdData);
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Failed to create referral: {ex.Message}", ex);
		}
	}

	public async ValueTask<Referral> CompleteReferral(string referralId, Guid referredUserId, CancellationToken ct = default)
	{
		try
		{
			var referral = await GetById(referralId, ct);
			if (referral == null)
			{
				throw new InvalidOperationException($"Referral not found: {referralId}");
			}

			var updatedReferral = referral with 
			{ 
				ReferredUserId = referredUserId,
				Status = "completed",
				CompletedAt = DateTime.UtcNow,
				TokensEarned = 25 // Standard referral bonus
			};

			var referralData = updatedReferral.ToData();
			var updatedData = await api.Api.Referrals[referralId].PutAsync(referralData, cancellationToken: ct);
			return new Referral(updatedData);
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Failed to complete referral: {ex.Message}", ex);
		}
	}

	public async ValueTask<bool> ValidateReferralCode(string referralCode, CancellationToken ct = default)
	{
		try
		{
			var referral = await GetByReferralCode(referralCode, ct);
			return referral != null;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<Referral?> GetByReferralCode(string referralCode, CancellationToken ct = default)
	{
		try
		{
			var referralsData = await api.Api.Referrals.GetAsync(cancellationToken: ct);
			var referralData = referralsData?.FirstOrDefault(r => r.ReferralCode == referralCode);
			return referralData != null ? new Referral(referralData) : null;
		}
		catch
		{
			return null;
		}
	}

	public async ValueTask<int> GetTotalTokensEarned(Guid userId, CancellationToken ct = default)
	{
		var userReferrals = await GetUserReferrals(userId, ct);
		return userReferrals.Where(r => r.IsCompleted).Sum(r => r.TokensEarned);
	}

	public async ValueTask<int> GetCompletedReferralsCount(Guid userId, CancellationToken ct = default)
	{
		var userReferrals = await GetUserReferrals(userId, ct);
		return userReferrals.Count(r => r.IsCompleted);
	}

	public async ValueTask<int> GetPendingReferralsCount(Guid userId, CancellationToken ct = default)
	{
		var userReferrals = await GetUserReferrals(userId, ct);
		return userReferrals.Count(r => r.IsPending);
	}

	public async ValueTask<IImmutableList<Referral>> GetByStatus(string status, CancellationToken ct = default)
	{
		try
		{
			var referralsData = await api.Api.Referrals.GetAsync(cancellationToken: ct);
			return referralsData?
				.Where(r => string.Equals(r.Status, status, StringComparison.OrdinalIgnoreCase))
				.Select(r => new Referral(r))
				.ToImmutableList() ?? ImmutableList<Referral>.Empty;
		}
		catch
		{
			return ImmutableList<Referral>.Empty;
		}
	}

	public async ValueTask<bool> MarkBonusPaid(string referralId, CancellationToken ct = default)
	{
		try
		{
			var referral = await GetById(referralId, ct);
			if (referral == null) return false;

			var updatedReferral = referral with { BonusPaid = true };
			var referralData = updatedReferral.ToData();
			await api.Api.Referrals[referralId].PutAsync(referralData, cancellationToken: ct);
			return true;
		}
		catch
		{
			return false;
		}
	}
}
