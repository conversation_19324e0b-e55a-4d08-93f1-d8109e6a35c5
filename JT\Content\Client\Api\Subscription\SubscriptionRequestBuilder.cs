// <auto-generated/>
#pragma warning disable CS0618
using JT.Content.Client.Api.Subscription.Plans;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace JT.Content.Client.Api.Subscription
{
    /// <summary>
    /// Builds and executes requests for operations under \api\Subscription
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class SubscriptionRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The plans property</summary>
        public global::JT.Content.Client.Api.Subscription.Plans.PlansRequestBuilder Plans
        {
            get => new global::JT.Content.Client.Api.Subscription.Plans.PlansRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.Subscription.SubscriptionRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public SubscriptionRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Subscription", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.Subscription.SubscriptionRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public SubscriptionRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Subscription", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
