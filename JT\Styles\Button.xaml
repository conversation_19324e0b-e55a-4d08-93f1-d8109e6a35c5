﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
					xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
					xmlns:utu="using:Uno.Toolkit.UI">

	<Style x:Key="JTsPrimaryButtonStyle"
		   TargetType="Button"
		   BasedOn="{StaticResource FilledButtonStyle}">
		<Setter Property="Padding" Value="24,20" />
		<Setter Property="CornerRadius" Value="4" />
		<Setter Property="Height" Value="59" />
		<Setter Property="Background" Value="{ThemeResource PrimaryBrush}" />
		<Setter Property="Foreground" Value="{ThemeResource OnPrimaryBrush}" />
		<Setter Property="BorderBrush" Value="{ThemeResource PrimaryBrush}" />
	</Style>

	<Style x:Key="JTsSecondaryButtonStyle"
		   TargetType="Button"
		   BasedOn="{StaticResource TextButtonStyle}">
		<Setter Property="Padding" Value="12,20" />
		<Setter Property="CornerRadius" Value="4" />
		<Setter Property="Height" Value="60" />
	</Style>

	<Style x:Key="JTsTonalButtonStyle"
		   TargetType="Button"
		   BasedOn="{StaticResource FilledTonalButtonStyle}">
		<Setter Property="Padding" Value="16,20,24,20" />
		<Setter Property="CornerRadius" Value="4" />
	</Style>

	<Style x:Key="JTsOutlinedButtonStyle"
		   TargetType="Button"
		   BasedOn="{StaticResource OutlinedButtonStyle}">
		<Setter Property="Padding" Value="24,20" />
		<Setter Property="CornerRadius" Value="4" />
		<Setter Property="Height" Value="59" />
		<Setter Property="Foreground" Value="{ThemeResource PrimaryBrush}" />
		<Setter Property="BorderBrush" Value="{ThemeResource OutlineBrush}" />
		<Setter Property="BorderThickness" Value="1" />
	</Style>

	<Style x:Key="PrimaryFabStyle"
		   TargetType="Button"
		   BasedOn="{StaticResource FabStyle}">
		<Setter Property="utu:ResourceExtensions.Resources">
			<Setter.Value>
				<ResourceDictionary>
					<ResourceDictionary.ThemeDictionaries>
						<ResourceDictionary x:Key="Default">
							<StaticResource x:Key="FabForeground" ResourceKey="OnPrimaryBrush" />
							<StaticResource x:Key="FabForegroundPressed" ResourceKey="OnPrimaryBrush" />
							<StaticResource x:Key="FabForegroundPointerOver" ResourceKey="OnPrimaryBrush" />
							<StaticResource x:Key="FabForegroundDisabled" ResourceKey="OnSurfaceDisabledBrush" />
							<StaticResource x:Key="FabBackground" ResourceKey="PrimaryBrush" />
							<StaticResource x:Key="FabBackgroundPressed" ResourceKey="PrimaryBrush" />
							<StaticResource x:Key="FabBackgroundPointerOver" ResourceKey="PrimaryBrush" />
							<StaticResource x:Key="FabBackgroundDisabled" ResourceKey="SystemControlTransparentBrush" />
							<StaticResource x:Key="FabStateOverlayBackground" ResourceKey="SystemControlTransparentBrush" />
							<StaticResource x:Key="FabStateOverlayBackgroundPointerOver" ResourceKey="OnPrimaryHoverBrush" />
							<StaticResource x:Key="FabStateOverlayBackgroundFocused" ResourceKey="OnPrimaryFocusedBrush" />
							<StaticResource x:Key="FabStateOverlayBackgroundPressed" ResourceKey="OnPrimaryPressedBrush" />
						</ResourceDictionary>
						<ResourceDictionary x:Key="Light">
							<StaticResource x:Key="FabForeground" ResourceKey="OnPrimaryBrush" />
							<StaticResource x:Key="FabForegroundPressed" ResourceKey="OnPrimaryBrush" />
							<StaticResource x:Key="FabForegroundPointerOver" ResourceKey="OnPrimaryBrush" />
							<StaticResource x:Key="FabForegroundDisabled" ResourceKey="OnSurfaceDisabledBrush" />
							<StaticResource x:Key="FabBackground" ResourceKey="PrimaryBrush" />
							<StaticResource x:Key="FabBackgroundPressed" ResourceKey="PrimaryBrush" />
							<StaticResource x:Key="FabBackgroundPointerOver" ResourceKey="PrimaryBrush" />
							<StaticResource x:Key="FabBackgroundDisabled" ResourceKey="SystemControlTransparentBrush" />
							<StaticResource x:Key="FabStateOverlayBackground" ResourceKey="SystemControlTransparentBrush" />
							<StaticResource x:Key="FabStateOverlayBackgroundPointerOver" ResourceKey="OnPrimaryHoverBrush" />
							<StaticResource x:Key="FabStateOverlayBackgroundFocused" ResourceKey="OnPrimaryFocusedBrush" />
							<StaticResource x:Key="FabStateOverlayBackgroundPressed" ResourceKey="OnPrimaryPressedBrush" />
						</ResourceDictionary>
					</ResourceDictionary.ThemeDictionaries>
				</ResourceDictionary>
			</Setter.Value>
		</Setter>
	</Style>

	<Style x:Key="JTsFabButtonStyle"
		   TargetType="Button"
		   BasedOn="{StaticResource PrimaryFabStyle}">
		<Setter Property="Padding" Value="24,20" />
		<Setter Property="CornerRadius" Value="4" />
		<Setter Property="Margin" Value="0,0,16,16" />
	</Style>

</ResourceDictionary>
