using System.Net;
using JT.Content.Client;
//using JTR = JT.Content.Client.Models.JT;

namespace JT.Services.Caching;
public sealed class JTCache : IJTCache
{
    private readonly JTApiClient _client;
    private readonly ISerializer _serializer;
    private readonly ILogger _logger;

    public JTCache(
        JT.Content.Client.JTApiClient client,
        ISerializer serializer
        , ILogger<JTCache> logger
    )
    {
        _client = client;
        _serializer = serializer;
        _logger = logger;
    }

    //private bool IsConnected => NetworkInformation.GetInternetConnectionProfile().GetNetworkConnectivityLevel() == NetworkConnectivityLevel.InternetAccess;

    //public async ValueTask<IImmutableList<JTR>> GetForecast(CancellationToken token)
    //{
    //    var JTText = await GetCachedWeather(token);
    //    if (!string.IsNullOrWhiteSpace(JTText))
    //    {
    //        return _serializer.FromString<ImmutableArray<JTR>>(JTText);
    //    }

    //    if (!IsConnected)
    //    {
    //        _logger.LogWarning("App is offline and cannot connect to the API.");
    //        throw new WebException("No internet connection", WebExceptionStatus.ConnectFailure);
    //    }

    //    IImmutableList<JTR> jt;

    //    var response = await _client
    //        .Api
    //        .Jt
    //        .GetAsync(null, token)
    //        .ConfigureAwait(false)
    //        ?? new List<JTR>();

    //    var json = _serializer.ToString(response);
    //    jt = _serializer.FromString<ImmutableArray<JTR>>(json);

    //    await Save(jt, token);
    //    return jt;
    //}

    //private static async ValueTask<StorageFile> GetFile(CreationCollisionOption option) =>
    //    await ApplicationData.Current.TemporaryFolder.CreateFileAsync("job.json", option);

    //private async ValueTask<string?> GetCachedWeather(CancellationToken token)
    //{
    //    var file = await GetFile(CreationCollisionOption.OpenIfExists);
    //    var properties = await file.GetBasicPropertiesAsync();

    //    // Reuse latest cache file if offline
    //    // or if the file is less than 5 minutes old
    //    if (IsConnected || DateTimeOffset.Now.AddMinutes(-5) > properties.DateModified || token.IsCancellationRequested)
    //    {
    //        return null;
    //    }

    //    return await File.ReadAllTextAsync(file.Path, token);
    //}

    //private async ValueTask Save(IImmutableList<JTR> weather, CancellationToken token)
    //{
    //    var weatherText = _serializer.ToString(weather);
    //    var file = await GetFile(CreationCollisionOption.ReplaceExisting);
    //    await File.WriteAllTextAsync(file.Path, weatherText, token);
    //}
}
