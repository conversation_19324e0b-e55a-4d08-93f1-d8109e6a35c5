namespace JT.Presentation;

public partial record JobTransferModel
{
	private readonly IMessenger _messenger;

	private readonly JobTransfer _jobTransfer;
	public JobTransferModel(JobTransfer jobTransfer, IMessenger messenger)
	{
		_messenger = messenger;
        _jobTransfer = jobTransfer ?? new JobTransfer();
	}

	public IState<JobTransfer> JobTransfer => State
		.Value(this, () => _jobTransfer ?? new JobTransfer())
		.Observe(_messenger, cb => cb.Id);
}
