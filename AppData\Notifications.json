[{"id": "notif_001", "userId": "550e8400-e29b-41d4-a716-446655440001", "title": "New Employer Response", "body": "Salalah Technology Solutions has responded to your transfer request", "description": "Salalah Technology Solutions has responded to your transfer request", "type": "employer_response", "priority": "high", "timestamp": "2024-01-18T14:30:00Z", "date": "2024-01-18T14:30:00Z", "read": false, "isRead": false, "readAt": null, "actionUrl": "/transfer-request/650e8400-e29b-41d4-a716-446655440001", "imageUrl": "https://example.com/logos/sts.png"}, {"id": "notif_002", "userId": "550e8400-e29b-41d4-a716-446655440001", "title": "Interview Scheduled", "body": "Your interview with Dhofar Systems LLC is scheduled for January 25th at 10:00 AM", "description": "Your interview with Dhofar Systems LLC is scheduled for January 25th at 10:00 AM", "type": "employer_response", "priority": "high", "timestamp": "2024-01-19T09:15:00Z", "date": "2024-01-19T09:15:00Z", "read": false, "isRead": false, "readAt": null, "actionUrl": "/interviews/interview_001", "imageUrl": null}, {"id": "notif_003", "userId": "550e8400-e29b-41d4-a716-446655440001", "title": "Referral Bonus Earned", "body": "You earned 25 tokens for referring <PERSON>", "description": "You earned 25 tokens for referring <PERSON>", "type": "referral", "priority": "normal", "timestamp": "2022-11-10T00:00:00Z", "date": "2022-11-10T00:00:00Z", "read": true, "isRead": true, "readAt": "2022-11-10T08:30:00Z", "actionUrl": "/tokens", "imageUrl": null}, {"id": "notif_004", "userId": "550e8400-e29b-41d4-a716-446655440002", "title": "Job Offer Received", "body": "Muscat Marketing Group has made you a job offer for Senior Marketing Manager position", "description": "Muscat Marketing Group has made you a job offer for Senior Marketing Manager position", "type": "employer_response", "priority": "critical", "timestamp": "2024-01-12T11:00:00Z", "date": "2024-01-12T11:00:00Z", "read": false, "isRead": false, "readAt": null, "actionUrl": "/offers/offer_001", "imageUrl": "https://example.com/logos/mmg.png"}, {"id": "notif_005", "userId": "550e8400-e29b-41d4-a716-446655440002", "title": "Transfer Request Approved", "body": "Your transfer request has been approved and is now visible to employers", "description": "Your transfer request has been approved and is now visible to employers", "type": "transfer_request", "priority": "high", "timestamp": "2024-01-11T10:00:00Z", "date": "2024-01-11T10:00:00Z", "read": true, "isRead": true, "readAt": "2024-01-11T10:30:00Z", "actionUrl": "/transfer-request/650e8400-e29b-41d4-a716-446655440002", "imageUrl": null}, {"id": "notif_006", "userId": "550e8400-e29b-41d4-a716-446655440003", "title": "Subscription Renewed", "body": "Your Diamond subscription has been renewed for another month", "description": "Your Diamond subscription has been renewed for another month", "type": "subscription", "priority": "normal", "timestamp": "2024-01-01T00:00:00Z", "date": "2024-01-01T00:00:00Z", "read": true, "isRead": true, "readAt": "2024-01-01T09:00:00Z", "actionUrl": "/subscriptions", "imageUrl": null}, {"id": "notif_007", "userId": "550e8400-e29b-41d4-a716-446655440003", "title": "Multiple Referral Bonuses", "body": "You earned 75 tokens for referring 3 new users this month", "description": "You earned 75 tokens for referring 3 new users this month", "type": "referral", "priority": "normal", "timestamp": "2023-06-15T00:00:00Z", "date": "2023-06-15T00:00:00Z", "read": true, "isRead": true, "readAt": "2023-06-15T10:00:00Z", "actionUrl": "/tokens", "imageUrl": null}, {"id": "notif_008", "userId": "550e8400-e29b-41d4-a716-446655440001", "title": "System Maintenance", "body": "Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM", "description": "Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM", "type": "system", "priority": "low", "timestamp": "2024-01-20T18:00:00Z", "date": "2024-01-20T18:00:00Z", "read": false, "isRead": false, "readAt": null, "actionUrl": null, "imageUrl": null}]