using TransferRequestData = JT.Content.Client.Models.TransferRequestData;

namespace JT.Business.Models;

public class JobTransferImages
{
	public JobTransferImages(ImmutableList<TransferRequestData> transferRequestData)
	{
		FirstImage = transferRequestData.Count > 0
			? transferRequestData[0].ImageUrl
			: null;
		SecondImage = transferRequestData.Count > 1
			? transferRequestData[1].ImageUrl
			: null;
		ThirdImage = transferRequestData.Count > 2
			? transferRequestData[2].ImageUrl
			: null;
	}

	public string? FirstImage { get; set; }

	public string? SecondImage { get; set; }

	public string? ThirdImage { get; set; }
}
