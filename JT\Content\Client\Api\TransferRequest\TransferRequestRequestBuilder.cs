// <auto-generated/>
#pragma warning disable CS0618
using JT.Content.Client.Api.TransferRequest.Categories;
using JT.Content.Client.Api.TransferRequest.Count;
using JT.Content.Client.Api.TransferRequest.CreatetransferRequest;
using JT.Content.Client.Api.TransferRequest.Favorited;
using JT.Content.Client.Api.TransferRequest.Item;
using JT.Content.Client.Api.TransferRequest.Popular;
using JT.Content.Client.Api.TransferRequest.Status;
using JT.Content.Client.Api.TransferRequest.Trending;
using JT.Content.Client.Api.TransferRequest.User;
using JT.Content.Client.Models;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Threading;
using System;
namespace JT.Content.Client.Api.TransferRequest
{
    /// <summary>
    /// Builds and executes requests for operations under \api\TransferRequest
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class TransferRequestRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The categories property</summary>
        public global::JT.Content.Client.Api.TransferRequest.Categories.CategoriesRequestBuilder Categories
        {
            get => new global::JT.Content.Client.Api.TransferRequest.Categories.CategoriesRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The count property</summary>
        public global::JT.Content.Client.Api.TransferRequest.Count.CountRequestBuilder Count
        {
            get => new global::JT.Content.Client.Api.TransferRequest.Count.CountRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The createtransferRequest property</summary>
        public global::JT.Content.Client.Api.TransferRequest.CreatetransferRequest.CreatetransferRequestRequestBuilder CreatetransferRequest
        {
            get => new global::JT.Content.Client.Api.TransferRequest.CreatetransferRequest.CreatetransferRequestRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The favorited property</summary>
        public global::JT.Content.Client.Api.TransferRequest.Favorited.FavoritedRequestBuilder Favorited
        {
            get => new global::JT.Content.Client.Api.TransferRequest.Favorited.FavoritedRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The popular property</summary>
        public global::JT.Content.Client.Api.TransferRequest.Popular.PopularRequestBuilder Popular
        {
            get => new global::JT.Content.Client.Api.TransferRequest.Popular.PopularRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The status property</summary>
        public global::JT.Content.Client.Api.TransferRequest.Status.StatusRequestBuilder Status
        {
            get => new global::JT.Content.Client.Api.TransferRequest.Status.StatusRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The trending property</summary>
        public global::JT.Content.Client.Api.TransferRequest.Trending.TrendingRequestBuilder Trending
        {
            get => new global::JT.Content.Client.Api.TransferRequest.Trending.TrendingRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>The user property</summary>
        public global::JT.Content.Client.Api.TransferRequest.User.UserRequestBuilder User
        {
            get => new global::JT.Content.Client.Api.TransferRequest.User.UserRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>Gets an item from the JT.Content.Client.api.TransferRequest.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::JT.Content.Client.Api.TransferRequest.Item.ItemRequestBuilder"/></returns>
        public global::JT.Content.Client.Api.TransferRequest.Item.ItemRequestBuilder this[Guid position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("%2Did", position);
                return new global::JT.Content.Client.Api.TransferRequest.Item.ItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>Gets an item from the JT.Content.Client.api.TransferRequest.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::JT.Content.Client.Api.TransferRequest.Item.ItemRequestBuilder"/></returns>
        [Obsolete("This indexer is deprecated and will be removed in the next major version. Use the one with the typed parameter instead.")]
        public global::JT.Content.Client.Api.TransferRequest.Item.ItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                if (!string.IsNullOrWhiteSpace(position)) urlTplParams.Add("%2Did", position);
                return new global::JT.Content.Client.Api.TransferRequest.Item.ItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public TransferRequestRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/TransferRequest{?userId*}", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public TransferRequestRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/TransferRequest{?userId*}", rawUrl)
        {
        }
        /// <returns>A List&lt;global::JT.Content.Client.Models.TransferRequestData&gt;</returns>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
        /// <exception cref="global::JT.Content.Client.Models.ProblemDetails">When receiving a 404 status code</exception>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<List<global::JT.Content.Client.Models.TransferRequestData>?> GetAsync(Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<List<global::JT.Content.Client.Models.TransferRequestData>> GetAsync(Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            var requestInfo = ToGetRequestInformation(requestConfiguration);
            var errorMapping = new Dictionary<string, ParsableFactory<IParsable>>
            {
                { "404", global::JT.Content.Client.Models.ProblemDetails.CreateFromDiscriminatorValue },
            };
            var collectionResult = await RequestAdapter.SendCollectionAsync<global::JT.Content.Client.Models.TransferRequestData>(requestInfo, global::JT.Content.Client.Models.TransferRequestData.CreateFromDiscriminatorValue, errorMapping, cancellationToken).ConfigureAwait(false);
            return collectionResult?.AsList();
        }
        /// <returns>A <see cref="Guid"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="cancellationToken">Cancellation token to use when cancelling requests</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
        /// <exception cref="global::JT.Content.Client.Models.ProblemDetails">When receiving a 404 status code</exception>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public async Task<Guid?> PostAsync(global::JT.Content.Client.Models.TransferRequestData body, Action<RequestConfiguration<global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder.TransferRequestRequestBuilderPostQueryParameters>>? requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#nullable restore
#else
        public async Task<Guid?> PostAsync(global::JT.Content.Client.Models.TransferRequestData body, Action<RequestConfiguration<global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder.TransferRequestRequestBuilderPostQueryParameters>> requestConfiguration = default, CancellationToken cancellationToken = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = ToPostRequestInformation(body, requestConfiguration);
            var errorMapping = new Dictionary<string, ParsableFactory<IParsable>>
            {
                { "404", global::JT.Content.Client.Models.ProblemDetails.CreateFromDiscriminatorValue },
            };
            return await RequestAdapter.SendPrimitiveAsync<Guid?>(requestInfo, errorMapping, cancellationToken).ConfigureAwait(false);
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<DefaultQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToGetRequestInformation(Action<RequestConfiguration<DefaultQueryParameters>> requestConfiguration = default)
        {
#endif
            var requestInfo = new RequestInformation(Method.GET, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json");
            return requestInfo;
        }
        /// <returns>A <see cref="RequestInformation"/></returns>
        /// <param name="body">The request body</param>
        /// <param name="requestConfiguration">Configuration for the request such as headers, query parameters, and middleware options.</param>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public RequestInformation ToPostRequestInformation(global::JT.Content.Client.Models.TransferRequestData body, Action<RequestConfiguration<global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder.TransferRequestRequestBuilderPostQueryParameters>>? requestConfiguration = default)
        {
#nullable restore
#else
        public RequestInformation ToPostRequestInformation(global::JT.Content.Client.Models.TransferRequestData body, Action<RequestConfiguration<global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder.TransferRequestRequestBuilderPostQueryParameters>> requestConfiguration = default)
        {
#endif
            _ = body ?? throw new ArgumentNullException(nameof(body));
            var requestInfo = new RequestInformation(Method.POST, UrlTemplate, PathParameters);
            requestInfo.Configure(requestConfiguration);
            requestInfo.Headers.TryAdd("Accept", "application/json");
            requestInfo.SetContentFromParsable(RequestAdapter, "application/json", body);
            return requestInfo;
        }
        /// <summary>
        /// Returns a request builder with the provided arbitrary URL. Using this method means any other path or query parameters are ignored.
        /// </summary>
        /// <returns>A <see cref="global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder"/></returns>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        public global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder WithUrl(string rawUrl)
        {
            return new global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder(rawUrl, RequestAdapter);
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class TransferRequestRequestBuilderGetRequestConfiguration : RequestConfiguration<DefaultQueryParameters>
        {
        }
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        #pragma warning disable CS1591
        public partial class TransferRequestRequestBuilderPostQueryParameters 
        #pragma warning restore CS1591
        {
            [QueryParameter("userId")]
            public Guid? UserId { get; set; }
        }
        /// <summary>
        /// Configuration for the request such as headers, query parameters, and middleware options.
        /// </summary>
        [Obsolete("This class is deprecated. Please use the generic RequestConfiguration class generated by the generator.")]
        [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
        public partial class TransferRequestRequestBuilderPostRequestConfiguration : RequestConfiguration<global::JT.Content.Client.Api.TransferRequest.TransferRequestRequestBuilder.TransferRequestRequestBuilderPostQueryParameters>
        {
        }
    }
}
#pragma warning restore CS0618
