{"descriptionHash": "84265CCD655F78C60F08D5B753BF23E7BDFE9FC1A119052B00226E8D1FBA5EC0B5E071B152C4E0576AF4CFB3241A2A54477A48BC3B5F7A279CE791975935E912", "descriptionLocation": "../../Specs/JTApiClient.swagger.json", "lockFileVersion": "1.0.0", "kiotaVersion": "1.27.0", "clientClassName": "JTApiClient", "typeAccessModifier": "Public", "clientNamespaceName": "JT.Content.Client", "language": "CSharp", "usesBackingStore": false, "excludeBackwardCompatible": false, "includeAdditionalData": true, "disableSSLValidation": false, "serializers": ["Microsoft.Kiota.Serialization.Json.JsonSerializationWriterFactory", "Microsoft.Kiota.Serialization.Text.TextSerializationWriterFactory", "Microsoft.Kiota.Serialization.Form.FormSerializationWriterFactory", "Microsoft.Kiota.Serialization.Multipart.MultipartSerializationWriterFactory"], "deserializers": ["Microsoft.Kiota.Serialization.Json.JsonParseNodeFactory", "Microsoft.Kiota.Serialization.Text.TextParseNodeFactory", "Microsoft.Kiota.Serialization.Form.FormParseNodeFactory"], "structuredMimeTypes": ["application/json", "text/plain;q=0.9", "application/x-www-form-urlencoded;q=0.2", "multipart/form-data;q=0.1"], "includePatterns": [], "excludePatterns": [], "disabledValidationRules": []}