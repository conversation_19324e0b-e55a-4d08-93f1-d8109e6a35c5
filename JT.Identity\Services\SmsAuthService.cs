using IdentityServer4.Validation;
using System.Threading.Tasks;
using JT.Identity.Services;
using IdentityServer4.Models;

namespace JT.Identity.Services;

public class SmsAuthService : IExtensionGrantValidator
{
    private readonly ISmsSender _smsSender;
    private readonly IUserRepository _userRepository;

    public SmsAuthService(ISmsSender smsSender, IUserRepository userRepository)
    {
        _smsSender = smsSender;
        _userRepository = userRepository;
    }

    public string GrantType => "sms_auth";

    public async Task ValidateAsync(ExtensionGrantValidationContext context)
    {
        var phoneNumber = context.Request.Raw["phone_number"];
        var code = context.Request.Raw["code"];

        // Validate SMS code (you'd implement this)
        var isValid = await _smsSender.ValidateCodeAsync(phoneNumber, code);
        
        if (isValid)
        {
            var user = await _userRepository.FindByPhoneNumberAsync(phoneNumber);
            context.Result = new GrantValidationResult(user.SubjectId, GrantType);
        }
        else
        {
            context.Result = new GrantValidationResult(TokenRequestErrors.InvalidGrant, "Invalid SMS code");
        }
    }
}
