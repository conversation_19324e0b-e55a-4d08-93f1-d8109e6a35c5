namespace JT.Business.Services.Promotions;

public interface IPromotionService
{
	// Promotion Management
	ValueTask<IImmutableList<Promotion>> GetAllPromotions(CancellationToken ct = default);
	ValueTask<IImmutableList<Promotion>> GetActivePromotions(CancellationToken ct = default);
	ValueTask<IImmutableList<Promotion>> GetPromotionsByCompany(string companyId, CancellationToken ct = default);
	ValueTask<IImmutableList<Promotion>> GetPromotionsByType(string type, CancellationToken ct = default);
	ValueTask<IImmutableList<Promotion>> GetPromotionsByCategory(string category, CancellationToken ct = default);
	ValueTask<Promotion?> GetPromotionById(string promotionId, CancellationToken ct = default);
	ValueTask<Promotion> CreatePromotion(Promotion promotion, CancellationToken ct = default);
	ValueTask<Promotion> UpdatePromotion(Promotion promotion, CancellationToken ct = default);
	ValueTask<bool> DeletePromotion(string promotionId, CancellationToken ct = default);
	ValueTask<bool> ApprovePromotion(string promotionId, string approvedBy, CancellationToken ct = default);
	ValueTask<bool> RejectPromotion(string promotionId, string rejectionReason, CancellationToken ct = default);
	ValueTask<bool> PausePromotion(string promotionId, CancellationToken ct = default);
	ValueTask<bool> ResumePromotion(string promotionId, CancellationToken ct = default);

	// Analytics & Tracking
	ValueTask<bool> TrackView(string promotionId, string? userId = null, CancellationToken ct = default);
	ValueTask<bool> TrackClick(string promotionId, string? userId = null, CancellationToken ct = default);
	ValueTask<bool> TrackConversion(string promotionId, string? userId = null, decimal? revenue = null, CancellationToken ct = default);
	ValueTask<PromotionAnalytics?> GetPromotionAnalytics(string promotionId, DateTime? date = null, CancellationToken ct = default);
	ValueTask<IImmutableList<PromotionAnalytics>> GetPromotionAnalyticsRange(string promotionId, DateTime startDate, DateTime endDate, CancellationToken ct = default);

	// Performance & Optimization
	ValueTask<IImmutableList<Promotion>> GetTopPerformingPromotions(int count = 10, CancellationToken ct = default);
	ValueTask<IImmutableList<Promotion>> GetPromotionsNeedingOptimization(CancellationToken ct = default);
	ValueTask<decimal> GetTotalRevenue(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken ct = default);
	ValueTask<decimal> GetCompanyRevenue(string companyId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken ct = default);

	// Search & Filtering
	ValueTask<IImmutableList<Promotion>> SearchPromotions(string query, CancellationToken ct = default);
	ValueTask<IImmutableList<Promotion>> GetPromotionsByBudgetRange(decimal minBudget, decimal maxBudget, CancellationToken ct = default);
	ValueTask<IImmutableList<Promotion>> GetExpiringPromotions(int daysAhead = 7, CancellationToken ct = default);
}
