namespace JT.Business.Services.Referrals;

public interface IReferralService
{
	ValueTask<IImmutableList<Referral>> GetUserReferrals(Guid userId, CancellationToken ct = default);
	ValueTask<Referral?> GetById(string id, CancellationToken ct = default);
	ValueTask<Referral> CreateReferral(Guid referrerId, string referralCode, string inviteeEmail, CancellationToken ct = default);
	ValueTask<Referral> CompleteReferral(string referralId, Guid referredUserId, CancellationToken ct = default);
	ValueTask<bool> ValidateReferralCode(string referralCode, CancellationToken ct = default);
	ValueTask<Referral?> GetByReferralCode(string referralCode, CancellationToken ct = default);
	ValueTask<int> GetTotalTokensEarned(Guid userId, CancellationToken ct = default);
	ValueTask<int> GetCompletedReferralsCount(Guid userId, CancellationToken ct = default);
	ValueTask<int> GetPendingReferralsCount(Guid userId, CancellationToken ct = default);
	ValueTask<IImmutableList<Referral>> GetByStatus(string status, CancellationToken ct = default);
	ValueTask<bool> MarkBonusPaid(string referralId, CancellationToken ct = default);
}
