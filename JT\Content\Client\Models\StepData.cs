// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace JT.Content.Client.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class StepData : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
        /// <summary>The ingredients property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? Ingredients { get; set; }
#nullable restore
#else
        public List<string> Ingredients { get; set; }
#endif
        /// <summary>The name property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Name { get; set; }
#nullable restore
#else
        public string Name { get; set; }
#endif
        /// <summary>The number property</summary>
        public int? Number { get; set; }
        /// <summary>The transferTime property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public global::JT.Content.Client.Models.TimeSpanObject? TransferTime { get; set; }
#nullable restore
#else
        public global::JT.Content.Client.Models.TimeSpanObject TransferTime { get; set; }
#endif
        /// <summary>The transferware property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? Transferware { get; set; }
#nullable restore
#else
        public List<string> Transferware { get; set; }
#endif
        /// <summary>The urlVideo property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UrlVideo { get; set; }
#nullable restore
#else
        public string UrlVideo { get; set; }
#endif
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::JT.Content.Client.Models.StepData"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::JT.Content.Client.Models.StepData CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::JT.Content.Client.Models.StepData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "description", n => { Description = n.GetStringValue(); } },
                { "ingredients", n => { Ingredients = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
                { "name", n => { Name = n.GetStringValue(); } },
                { "number", n => { Number = n.GetIntValue(); } },
                { "transferTime", n => { TransferTime = n.GetObjectValue<global::JT.Content.Client.Models.TimeSpanObject>(global::JT.Content.Client.Models.TimeSpanObject.CreateFromDiscriminatorValue); } },
                { "transferware", n => { Transferware = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
                { "urlVideo", n => { UrlVideo = n.GetStringValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteStringValue("description", Description);
            writer.WriteCollectionOfPrimitiveValues<string>("ingredients", Ingredients);
            writer.WriteStringValue("name", Name);
            writer.WriteIntValue("number", Number);
            writer.WriteObjectValue<global::JT.Content.Client.Models.TimeSpanObject>("transferTime", TransferTime);
            writer.WriteCollectionOfPrimitiveValues<string>("transferware", Transferware);
            writer.WriteStringValue("urlVideo", UrlVideo);
        }
    }
}
#pragma warning restore CS0618
