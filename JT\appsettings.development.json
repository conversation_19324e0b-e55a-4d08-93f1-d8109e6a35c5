﻿{
  "AppConfig": {
    "Environment": "Development"
 },
 "OidcAuthentication": {
    "Authority": "https://mydomain.com/",
    "ClientId": "myClientId",
    "ClientSecret": "myClientSecret",
    "Scope": "openid profile email api offline_access",
    "RedirectUri": "myprotocol://callback",
    "PostLogoutRedirectUri": "myprotocol://callback"
  },
  "ApiClient": {
    "Url": "https://localhost:5002",
    "UseNativeHandler": true
  }
}
