/**
  When adding fonts here, make sure to add them using a base64 data uri, otherwise
  fonts loading are delayed, and text may get displayed incorrectly.
*/

/* https://github.com/unoplatform/uno/issues/3954 */
@font-face {
  font-family: 'Segoe UI';
  src: local('Segoe UI'), local('-apple-system'), local('BlinkMacSystemFont'), local('Inter'), local('Cantarell'), local('Ubuntu'), local('Roboto'), local('Open Sans'), local('Noto Sans'), local('Helvetica Neue'), local('sans-serif');
}

@font-face {
  font-family: 'Roboto';
  src: url(./Uno.Fonts.Roboto/Fonts/Roboto-Light.ttf) format('truetype');
  font-weight: 300;
}

@font-face {
  font-family: 'Roboto';
  src: url(./Uno.Fonts.Roboto/Fonts/Roboto-Regular.ttf) format('truetype');
  font-weight: 400;
}

@font-face {
  font-family: 'Roboto';
  src: url(./Uno.Fonts.Roboto/Fonts/Roboto-Medium.ttf) format('truetype');
  font-weight: 500;
}
