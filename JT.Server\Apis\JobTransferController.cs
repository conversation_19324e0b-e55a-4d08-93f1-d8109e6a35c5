namespace JT.Server.Apis;

/// <summary>
/// JobTransfer Endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class JobTransferController() : JTControllerBase
{
	private readonly string _jobTransfersFilePath = "JobTransfers.json";
	private readonly string _savedJobTransferFilePath = "SavedJobTransfers.json";

    /// <summary>
    /// Retrieves all jobTransfers.
    /// </summary>
    /// <returns>A list of jobTransfers.</returns>
    [HttpGet]
	[Produces("application/json")]
	[ProducesResponseType(typeof(IEnumerable<JobTransferData>), 200)]
	[ProducesResponseType(404)]
	public ActionResult<IEnumerable<JobTransferData>> GetAll()
	{
		var jobTransfers = LoadData<List<JobTransferData>>(_jobTransfersFilePath);
		return Ok(jobTransfers.ToImmutableList());
	}

    /// <summary>
    /// Creates a new jobTransfer.
    /// </summary>
    /// <param name="jobTransfer">The jobTransfer data.</param>
    /// <param name="userId">The user ID.</param>
    /// <returns>The created jobTransfer.</returns>
    [HttpPost]
	[Produces("application/json")]
	[ProducesResponseType(typeof(JobTransferData), 201)]
	public ActionResult<JobTransferData> Create([FromBody] JobTransferData jobTransfer, [FromQuery] Guid userId)
	{
		var jobTransfers = LoadData<List<JobTransferData>>(_jobTransfersFilePath);
        jobTransfer.UserId = userId;
        jobTransfers.Add(jobTransfer);

		return Created("", jobTransfer);
	}

    /// <summary>
    /// Updates an existing jobTransfer.
    /// </summary>
    /// <param name="jobTransfer">The updated jobTransfer data.</param>
    /// <returns>The updated jobTransfer, or NotFound if the jobTransfer does not exist.</returns>
    [HttpPut]
	[Produces("application/json")]
	[ProducesResponseType(typeof(JobTransferData), 200)]
	[ProducesResponseType(404)]
	public ActionResult<JobTransferData> Update([FromBody] JobTransferData jobTransfer)
	{
		var jobTransfers = LoadData<List<JobTransferData>>(_jobTransfersFilePath);
		var jobTransferItem = jobTransfers.FirstOrDefault(c => c.Id == jobTransfer.Id);

		if (jobTransferItem != null)
		{
            jobTransferItem.Name = jobTransfer.Name;
            jobTransferItem.TransferRequests = jobTransfer.TransferRequests;

			return Ok(jobTransferItem);
		}
		else
		{
			return NotFound("jobTransfer not found");
		}
	}

    /// <summary>
    /// Saves or unsaves a jobTransfer for a specific user.
    /// </summary>
    /// <param name="jobTransfer">The jobTransfer data.</param>
    /// <param name="userId">The user ID.</param>
    /// <returns>No content.</returns>
    [HttpPost("save")]
	public IActionResult Save([FromBody] JobTransferData jobTransfer, [FromQuery] Guid userId) =>
		// We do not persist the saved state in this example.
		NoContent();

    /// <summary>
    /// Retrieves saved jobTransfers for a specific user.
    /// </summary>
    /// <param name="userId">The user ID.</param>
    /// <returns>A list of saved jobTransfers.</returns>
    [HttpGet("saved")]
	[Produces("application/json")]
	[ProducesResponseType(typeof(IEnumerable<JobTransferData>), 200)]
	public ActionResult<IEnumerable<JobTransferData>> GetSaved([FromQuery] Guid userId)
	{
		var savedJobTransfers = LoadData<List<Guid>>(_savedJobTransferFilePath);

		var jobTransfers = LoadData<List<JobTransferData>>(_jobTransfersFilePath);
		var savedJobTransfersList = jobTransfers.Where(cb => savedJobTransfers.Contains(cb.Id)).ToImmutableList();

		return Ok(savedJobTransfersList);
	}
}
