﻿<ContentDialog x:Class="JT.Presentation.Dialogs.CompletedDialog"
			   xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
			   xmlns:uen="using:Uno.Extensions.Navigation.UI"
			   xmlns:uer="using:Uno.Extensions.Reactive.UI"
			   xmlns:utu="using:Uno.Toolkit.UI"
			   xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
			   xmlns:ut="using:Uno.Themes"
			   xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
			   xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			   xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
			   mc:Ignorable="d"
			   Style="{StaticResource MaterialContentDialogStyle}"
			   Background="{ThemeResource SurfaceBrush}"
			   BorderBrush="Transparent"
			   CornerRadius="30"
			   Padding="0">
	<utu:AutoLayout PrimaryAxisAlignment="Center">
		<utu:AutoLayout Spacing="35"
						Padding="15"
						PrimaryAxisAlignment="Center">
			<utu:AutoLayout utu:AutoLayout.CounterAlignment="Center">
				<utu:AutoLayout Background="{ThemeResource PrimaryPressedBrush}"
								Padding="14"
								CornerRadius="15"
								Orientation="Horizontal"
								utu:AutoLayout.CounterAlignment="Center">
					<utu:AutoLayout Width="30"
									Height="30">
						<Image Source="ms-appx:///Assets/Icons/party.png"
							   Stretch="UniformToFill"
							   utu:AutoLayout.IsIndependentLayout="True"
							   VerticalAlignment="Stretch"
							   HorizontalAlignment="Stretch"
							   Width="30"
							   Height="30" />
					</utu:AutoLayout>
				</utu:AutoLayout>
				<TextBlock TextAlignment="Center"
						   Text="Hurray!"
						   Margin="10"
						   Foreground="{ThemeResource SurfaceInverseBrush}"
						   Style="{StaticResource HeadlineLarge}" />
				<TextBlock TextAlignment="Center"
						   Text="All steps are completed.&#xA;Enjoy your meal!"
						   Margin="10,0"
						   Foreground="{ThemeResource SurfaceInverseBrush}"
						   Style="{StaticResource BodyLarge}" />
			</utu:AutoLayout>
			<Button Background="{ThemeResource PrimaryBrush}"
					Content="Completed"
					Height="56"
					Width="350"
					CornerRadius="16"
					uen:Navigation.Request="-" />
		</utu:AutoLayout>
	</utu:AutoLayout>
</ContentDialog>
