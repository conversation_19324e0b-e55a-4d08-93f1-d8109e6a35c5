using JT.Business.Services.TransferRequests;

namespace JT.Presentation;

public partial record FilterModel
{
	private readonly INavigator _navigator;
    private readonly ITransferRequestService _transferRequestService;

    public FilterModel(SearchFilter filters, INavigator navigator, ITransferRequestService transferRequestService)
	{
		_navigator = navigator;
        _transferRequestService = transferRequestService;
		Filter = State.Value(this, () => filters);
	}
	public IState<SearchFilter> Filter { get; }
	public IEnumerable<FilterGroup> FilterGroups => Enum.GetValues(typeof(FilterGroup)).Cast<FilterGroup>();
	public IEnumerable<Time> Times => Enum.GetValues(typeof(Time)).Cast<Time>();
	public IEnumerable<Difficulty> Difficulties => Enum.GetValues(typeof(Difficulty)).Cast<Difficulty>();
	public IEnumerable<int> Serves => new int[] { 1, 2, 3, 4, 5 };
	public IListFeed<Category> Categories => ListFeed.Async(_transferRequestService.GetCategories);

	public async ValueTask ApplySearchFilter(SearchFilter filter) =>
		await _navigator.NavigateBackWithResultAsync(this, data: filter);

	public async ValueTask Reset(CancellationToken ct) =>
		await Filter.UpdateAsync(current => new SearchFilter(), ct);
}
