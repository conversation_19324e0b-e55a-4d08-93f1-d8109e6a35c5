﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
					xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
					xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
					xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
					xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
					xmlns:uen="using:Uno.Extensions.Navigation.UI"
					xmlns:uer="using:Uno.Extensions.Reactive.UI"
					xmlns:ut="using:Uno.Themes"
					xmlns:utu="using:Uno.Toolkit.UI">
	<!-- Themed colors -->
	<ResourceDictionary.ThemeDictionaries>
		<ResourceDictionary x:Key="Light">
			<x:String x:Key="JTsAppSignature">ms-appx:///Assets/Images/chefsappsignature_light.png</x:String>
			<x:String x:Key="Empty_Transfer">ms-appx:///Assets/Images/empty_transfer_light.png</x:String>
			<x:String x:Key="Empty_Box">ms-appx:///Assets/Images/empty_box_light.png</x:String>
			<x:String x:Key="Empty_Notification">ms-appx:///Assets/Images/empty_notification_light.png</x:String>
            <x:String x:Key="LiveTransfering_Success">ms-appx:///Assets/Images/success_light.png</x:String>
		</ResourceDictionary>
		<ResourceDictionary x:Key="Dark">
			<x:String x:Key="JTsAppSignature">ms-appx:///Assets/Images/chefsappsignature_dark.png</x:String>
			<x:String x:Key="Empty_Transfer">ms-appx:///Assets/Images/empty_transfer_dark.png</x:String>
			<x:String x:Key="Empty_Box">ms-appx:///Assets/Images/empty_box_dark.png</x:String>
			<x:String x:Key="Empty_Notification">ms-appx:///Assets/Images/empty_notification_dark.png</x:String>
            <x:String x:Key="LiveTransfering_Success">ms-appx:///Assets/Images/success_dark.png</x:String>
		</ResourceDictionary>
	</ResourceDictionary.ThemeDictionaries>
</ResourceDictionary>
