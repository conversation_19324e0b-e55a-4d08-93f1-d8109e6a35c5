// <auto-generated/>
#pragma warning disable CS0618
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions.Serialization;
using System.Collections.Generic;
using System.IO;
using System;
namespace JT.Content.Client.Models
{
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    #pragma warning disable CS1591
    public partial class UserData : IParsable
    #pragma warning restore CS1591
    {
        /// <summary>The age property</summary>
        public int? Age { get; set; }
        /// <summary>The bio property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Bio { get; set; }
#nullable restore
#else
        public string Bio { get; set; }
#endif
        /// <summary>The certifications property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? Certifications { get; set; }
#nullable restore
#else
        public List<string> Certifications { get; set; }
#endif
        /// <summary>The completedTransfers property</summary>
        public int? CompletedTransfers { get; set; }
        /// <summary>The currentEmployer property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CurrentEmployer { get; set; }
#nullable restore
#else
        public string CurrentEmployer { get; set; }
#endif
        /// <summary>The currentEmployerCity property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CurrentEmployerCity { get; set; }
#nullable restore
#else
        public string CurrentEmployerCity { get; set; }
#endif
        /// <summary>The currentEmployerLocation property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CurrentEmployerLocation { get; set; }
#nullable restore
#else
        public string CurrentEmployerLocation { get; set; }
#endif
        /// <summary>The currentEmployerState property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CurrentEmployerState { get; set; }
#nullable restore
#else
        public string CurrentEmployerState { get; set; }
#endif
        /// <summary>The currentPosition property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CurrentPosition { get; set; }
#nullable restore
#else
        public string CurrentPosition { get; set; }
#endif
        /// <summary>The currentSalaryGrade property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? CurrentSalaryGrade { get; set; }
#nullable restore
#else
        public string CurrentSalaryGrade { get; set; }
#endif
        /// <summary>The description property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Description { get; set; }
#nullable restore
#else
        public string Description { get; set; }
#endif
        /// <summary>The educationalQualification property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? EducationalQualification { get; set; }
#nullable restore
#else
        public string EducationalQualification { get; set; }
#endif
        /// <summary>The email property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Email { get; set; }
#nullable restore
#else
        public string Email { get; set; }
#endif
        /// <summary>The firstName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? FirstName { get; set; }
#nullable restore
#else
        public string FirstName { get; set; }
#endif
        /// <summary>The fullName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? FullName { get; set; }
#nullable restore
#else
        public string FullName { get; set; }
#endif
        /// <summary>The gender property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Gender { get; set; }
#nullable restore
#else
        public string Gender { get; set; }
#endif
        /// <summary>The id property</summary>
        public Guid? Id { get; set; }
        /// <summary>The invitedFriends property</summary>
        public int? InvitedFriends { get; set; }
        /// <summary>The isActive property</summary>
        public bool? IsActive { get; set; }
        /// <summary>The isAvailableForTransfer property</summary>
        public bool? IsAvailableForTransfer { get; set; }
        /// <summary>The isCurrent property</summary>
        public bool? IsCurrent { get; set; }
        /// <summary>The joinDate property</summary>
        public DateTimeOffset? JoinDate { get; set; }
        /// <summary>The languages property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? Languages { get; set; }
#nullable restore
#else
        public List<string> Languages { get; set; }
#endif
        /// <summary>The lastLoginAt property</summary>
        public DateTimeOffset? LastLoginAt { get; set; }
        /// <summary>The linkedInProfile property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? LinkedInProfile { get; set; }
#nullable restore
#else
        public string LinkedInProfile { get; set; }
#endif
        /// <summary>The password property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Password { get; set; }
#nullable restore
#else
        public string Password { get; set; }
#endif
        /// <summary>The phoneNumber property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? PhoneNumber { get; set; }
#nullable restore
#else
        public string PhoneNumber { get; set; }
#endif
        /// <summary>The portfolio property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Portfolio { get; set; }
#nullable restore
#else
        public string Portfolio { get; set; }
#endif
        /// <summary>The preferredWorkType property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? PreferredWorkType { get; set; }
#nullable restore
#else
        public string PreferredWorkType { get; set; }
#endif
        /// <summary>The profileImageUrl property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? ProfileImageUrl { get; set; }
#nullable restore
#else
        public string ProfileImageUrl { get; set; }
#endif
        /// <summary>The referralCode property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? ReferralCode { get; set; }
#nullable restore
#else
        public string ReferralCode { get; set; }
#endif
        /// <summary>The resumeUrl property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? ResumeUrl { get; set; }
#nullable restore
#else
        public string ResumeUrl { get; set; }
#endif
        /// <summary>The secondName property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? SecondName { get; set; }
#nullable restore
#else
        public string SecondName { get; set; }
#endif
        /// <summary>The skills property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public List<string>? Skills { get; set; }
#nullable restore
#else
        public List<string> Skills { get; set; }
#endif
        /// <summary>The subscriptionTier property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? SubscriptionTier { get; set; }
#nullable restore
#else
        public string SubscriptionTier { get; set; }
#endif
        /// <summary>The tokenBalance property</summary>
        public int? TokenBalance { get; set; }
        /// <summary>The tribe property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? Tribe { get; set; }
#nullable restore
#else
        public string Tribe { get; set; }
#endif
        /// <summary>The updatedAt property</summary>
        public DateTimeOffset? UpdatedAt { get; set; }
        /// <summary>The urlProfileImage property</summary>
#if NETSTANDARD2_1_OR_GREATER || NETCOREAPP3_1_OR_GREATER
#nullable enable
        public string? UrlProfileImage { get; set; }
#nullable restore
#else
        public string UrlProfileImage { get; set; }
#endif
        /// <summary>The yearsOfExperience property</summary>
        public int? YearsOfExperience { get; set; }
        /// <summary>
        /// Creates a new instance of the appropriate class based on discriminator value
        /// </summary>
        /// <returns>A <see cref="global::JT.Content.Client.Models.UserData"/></returns>
        /// <param name="parseNode">The parse node to use to read the discriminator value and create the object</param>
        public static global::JT.Content.Client.Models.UserData CreateFromDiscriminatorValue(IParseNode parseNode)
        {
            _ = parseNode ?? throw new ArgumentNullException(nameof(parseNode));
            return new global::JT.Content.Client.Models.UserData();
        }
        /// <summary>
        /// The deserialization information for the current model
        /// </summary>
        /// <returns>A IDictionary&lt;string, Action&lt;IParseNode&gt;&gt;</returns>
        public virtual IDictionary<string, Action<IParseNode>> GetFieldDeserializers()
        {
            return new Dictionary<string, Action<IParseNode>>
            {
                { "age", n => { Age = n.GetIntValue(); } },
                { "bio", n => { Bio = n.GetStringValue(); } },
                { "certifications", n => { Certifications = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
                { "completedTransfers", n => { CompletedTransfers = n.GetIntValue(); } },
                { "currentEmployer", n => { CurrentEmployer = n.GetStringValue(); } },
                { "currentEmployerCity", n => { CurrentEmployerCity = n.GetStringValue(); } },
                { "currentEmployerLocation", n => { CurrentEmployerLocation = n.GetStringValue(); } },
                { "currentEmployerState", n => { CurrentEmployerState = n.GetStringValue(); } },
                { "currentPosition", n => { CurrentPosition = n.GetStringValue(); } },
                { "currentSalaryGrade", n => { CurrentSalaryGrade = n.GetStringValue(); } },
                { "description", n => { Description = n.GetStringValue(); } },
                { "educationalQualification", n => { EducationalQualification = n.GetStringValue(); } },
                { "email", n => { Email = n.GetStringValue(); } },
                { "firstName", n => { FirstName = n.GetStringValue(); } },
                { "fullName", n => { FullName = n.GetStringValue(); } },
                { "gender", n => { Gender = n.GetStringValue(); } },
                { "id", n => { Id = n.GetGuidValue(); } },
                { "invitedFriends", n => { InvitedFriends = n.GetIntValue(); } },
                { "isActive", n => { IsActive = n.GetBoolValue(); } },
                { "isAvailableForTransfer", n => { IsAvailableForTransfer = n.GetBoolValue(); } },
                { "isCurrent", n => { IsCurrent = n.GetBoolValue(); } },
                { "joinDate", n => { JoinDate = n.GetDateTimeOffsetValue(); } },
                { "languages", n => { Languages = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
                { "lastLoginAt", n => { LastLoginAt = n.GetDateTimeOffsetValue(); } },
                { "linkedInProfile", n => { LinkedInProfile = n.GetStringValue(); } },
                { "password", n => { Password = n.GetStringValue(); } },
                { "phoneNumber", n => { PhoneNumber = n.GetStringValue(); } },
                { "portfolio", n => { Portfolio = n.GetStringValue(); } },
                { "preferredWorkType", n => { PreferredWorkType = n.GetStringValue(); } },
                { "profileImageUrl", n => { ProfileImageUrl = n.GetStringValue(); } },
                { "referralCode", n => { ReferralCode = n.GetStringValue(); } },
                { "resumeUrl", n => { ResumeUrl = n.GetStringValue(); } },
                { "secondName", n => { SecondName = n.GetStringValue(); } },
                { "skills", n => { Skills = n.GetCollectionOfPrimitiveValues<string>()?.AsList(); } },
                { "subscriptionTier", n => { SubscriptionTier = n.GetStringValue(); } },
                { "tokenBalance", n => { TokenBalance = n.GetIntValue(); } },
                { "tribe", n => { Tribe = n.GetStringValue(); } },
                { "updatedAt", n => { UpdatedAt = n.GetDateTimeOffsetValue(); } },
                { "urlProfileImage", n => { UrlProfileImage = n.GetStringValue(); } },
                { "yearsOfExperience", n => { YearsOfExperience = n.GetIntValue(); } },
            };
        }
        /// <summary>
        /// Serializes information the current object
        /// </summary>
        /// <param name="writer">Serialization writer to use to serialize this model</param>
        public virtual void Serialize(ISerializationWriter writer)
        {
            _ = writer ?? throw new ArgumentNullException(nameof(writer));
            writer.WriteIntValue("age", Age);
            writer.WriteStringValue("bio", Bio);
            writer.WriteCollectionOfPrimitiveValues<string>("certifications", Certifications);
            writer.WriteIntValue("completedTransfers", CompletedTransfers);
            writer.WriteStringValue("currentEmployer", CurrentEmployer);
            writer.WriteStringValue("currentEmployerCity", CurrentEmployerCity);
            writer.WriteStringValue("currentEmployerLocation", CurrentEmployerLocation);
            writer.WriteStringValue("currentEmployerState", CurrentEmployerState);
            writer.WriteStringValue("currentPosition", CurrentPosition);
            writer.WriteStringValue("currentSalaryGrade", CurrentSalaryGrade);
            writer.WriteStringValue("description", Description);
            writer.WriteStringValue("educationalQualification", EducationalQualification);
            writer.WriteStringValue("email", Email);
            writer.WriteStringValue("firstName", FirstName);
            writer.WriteStringValue("fullName", FullName);
            writer.WriteStringValue("gender", Gender);
            writer.WriteGuidValue("id", Id);
            writer.WriteIntValue("invitedFriends", InvitedFriends);
            writer.WriteBoolValue("isActive", IsActive);
            writer.WriteBoolValue("isAvailableForTransfer", IsAvailableForTransfer);
            writer.WriteBoolValue("isCurrent", IsCurrent);
            writer.WriteDateTimeOffsetValue("joinDate", JoinDate);
            writer.WriteCollectionOfPrimitiveValues<string>("languages", Languages);
            writer.WriteDateTimeOffsetValue("lastLoginAt", LastLoginAt);
            writer.WriteStringValue("linkedInProfile", LinkedInProfile);
            writer.WriteStringValue("password", Password);
            writer.WriteStringValue("phoneNumber", PhoneNumber);
            writer.WriteStringValue("portfolio", Portfolio);
            writer.WriteStringValue("preferredWorkType", PreferredWorkType);
            writer.WriteStringValue("profileImageUrl", ProfileImageUrl);
            writer.WriteStringValue("referralCode", ReferralCode);
            writer.WriteStringValue("resumeUrl", ResumeUrl);
            writer.WriteStringValue("secondName", SecondName);
            writer.WriteCollectionOfPrimitiveValues<string>("skills", Skills);
            writer.WriteStringValue("subscriptionTier", SubscriptionTier);
            writer.WriteIntValue("tokenBalance", TokenBalance);
            writer.WriteStringValue("tribe", Tribe);
            writer.WriteDateTimeOffsetValue("updatedAt", UpdatedAt);
            writer.WriteStringValue("urlProfileImage", UrlProfileImage);
            writer.WriteIntValue("yearsOfExperience", YearsOfExperience);
        }
    }
}
#pragma warning restore CS0618
