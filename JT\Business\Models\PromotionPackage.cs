using PromotionPackageData = JT.Client.Models.PromotionPackageData;

namespace JT.Business.Models;

public partial record PromotionPackage
{
	internal PromotionPackage(PromotionPackageData packageData)
	{
		Id = packageData.Id ?? string.Empty;
		Name = packageData.Name ?? string.Empty;
		DisplayName = packageData.DisplayName ?? string.Empty;
		Description = packageData.Description ?? string.Empty;
		Type = packageData.Type ?? string.Empty;
		PriceOMR = packageData.PriceOMR ?? 0;
		DurationDays = packageData.DurationDays ?? 0;
		MaxPromotions = packageData.MaxPromotions ?? 0;
		MaxViews = packageData.MaxViews ?? 0;
		MaxClicks = packageData.MaxClicks ?? 0;
		Features = packageData.Features?.ToImmutableList() ?? ImmutableList<string>.Empty;
		TargetAudience = packageData.TargetAudience ?? string.Empty;
		Priority = packageData.Priority ?? "normal";
		AnalyticsIncluded = packageData.AnalyticsIncluded ?? false;
		SupportLevel = packageData.SupportLevel ?? "basic";
		IsPopular = packageData.IsPopular ?? false;
		IsActive = packageData.IsActive ?? true;
		CreatedAt = packageData.CreatedAt ?? DateTime.MinValue;
		UpdatedAt = packageData.UpdatedAt ?? DateTime.MinValue;
	}

	public string Id { get; init; }
	public string Name { get; init; }
	public string DisplayName { get; init; }
	public string Description { get; init; }
	public string Type { get; init; } // banner, sponsored, directory, job_promotion, premium
	public decimal PriceOMR { get; init; }
	public int DurationDays { get; init; }
	public int MaxPromotions { get; init; } // -1 for unlimited
	public int MaxViews { get; init; } // -1 for unlimited
	public int MaxClicks { get; init; } // -1 for unlimited
	public IImmutableList<string> Features { get; init; }
	public string TargetAudience { get; init; }
	public string Priority { get; init; } // low, normal, high, premium
	public bool AnalyticsIncluded { get; init; }
	public string SupportLevel { get; init; } // basic, standard, premium
	public bool IsPopular { get; init; }
	public bool IsActive { get; init; }
	public DateTime CreatedAt { get; init; }
	public DateTime UpdatedAt { get; init; }

	// Computed properties
	public string PriceDisplay => $"{PriceOMR:F2} OMR";
	public string DurationDisplay => DurationDays switch
	{
		1 => "1 day",
		7 => "1 week",
		30 => "1 month",
		90 => "3 months",
		365 => "1 year",
		_ => $"{DurationDays} days"
	};

	public string PromotionsDisplay => MaxPromotions == -1 ? "Unlimited" : MaxPromotions.ToString();
	public string ViewsDisplay => MaxViews == -1 ? "Unlimited" : $"{MaxViews:N0}";
	public string ClicksDisplay => MaxClicks == -1 ? "Unlimited" : $"{MaxClicks:N0}";

	public bool IsUnlimitedPromotions => MaxPromotions == -1;
	public bool IsUnlimitedViews => MaxViews == -1;
	public bool IsUnlimitedClicks => MaxClicks == -1;

	public string PriorityColor => Priority switch
	{
		"premium" => "#FFD700",
		"high" => "#FF6B35",
		"normal" => "#007BFF",
		"low" => "#6C757D",
		_ => "#6C757D"
	};

	public string SupportLevelDisplay => SupportLevel switch
	{
		"basic" => "Email Support",
		"standard" => "Email + Phone Support",
		"premium" => "Dedicated Account Manager",
		_ => SupportLevel
	};

	public decimal CostPerDay => DurationDays > 0 ? PriceOMR / DurationDays : 0;
	public decimal CostPerPromotion => MaxPromotions > 0 ? PriceOMR / MaxPromotions : 0;

	public string PackageTier => Type switch
	{
		"banner" => "Advertising",
		"sponsored" => "Content Marketing",
		"directory" => "Business Listing",
		"job_promotion" => "Recruitment",
		"premium" => "Enterprise",
		_ => "Standard"
	};

	internal PromotionPackageData ToData() => new()
	{
		Id = Id,
		Name = Name,
		DisplayName = DisplayName,
		Description = Description,
		Type = Type,
		PriceOMR = PriceOMR,
		DurationDays = DurationDays,
		MaxPromotions = MaxPromotions,
		MaxViews = MaxViews,
		MaxClicks = MaxClicks,
		Features = Features.ToList(),
		TargetAudience = TargetAudience,
		Priority = Priority,
		AnalyticsIncluded = AnalyticsIncluded,
		SupportLevel = SupportLevel,
		IsPopular = IsPopular,
		IsActive = IsActive,
		CreatedAt = CreatedAt,
		UpdatedAt = UpdatedAt
	};
}
