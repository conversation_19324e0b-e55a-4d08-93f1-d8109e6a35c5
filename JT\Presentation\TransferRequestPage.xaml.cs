using LiveChartsCore;

namespace JT.Presentation;

public sealed partial class TransferRequestPage : Page
{
	public TransferRequestPage()
	{
		this.InitializeComponent();

		LiveCharts.Configure(config =>
			config
				.HasMap<NutritionChartItem>((nutritionChartItem, point) =>
				{
					// here we use the index as X, and the nutrition value as Y 
					return new(point, nutritionChartItem.Value);
				})
		);
	}
}
