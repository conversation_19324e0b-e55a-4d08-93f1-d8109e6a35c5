# Meo.Server Swagger Configuration
# To configure Swagger for your Meo.Server project, follow these steps:
# Create local tool manifest file then Install the Swashbuckle.AspNetCore.Cli tool globally or locally in your project.
        
PS D:\source\Test> dotnet new tool-manifest
The template "Dotnet local tool manifest file" was created successfully.

PS D:\source\Test> dotnet tool install Swashbuckle.AspNetCore.Cli
You can invoke the tool from this directory using the following commands: 'dotnet tool run swagger' or 'dotnet swagger'.
Tool 'swashbuckle.aspnetcore.cli' (version '9.0.1') was successfully installed. Entry is added to the manifest file D:\source\Test\.config\dotnet-tools.json.
PS D:\source\Test>

# Meo.Server kiota Swagger Post Build Target
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="dotnet swagger tofile --output ../Meo/Specs/Individual/User.swagger.json $(TargetDir)$(TargetFileName) v1" />
  </Target>



# Meo.Server Swagger Configuration
    <Target Name="PostBuild" AfterTargets="PostBuildEvent">
        <Exec Command="dotnet swagger tofile --output ../Meo/Specs/Individual/UserService.swagger.json $(TargetDir)$(TargetFileName) users" />
        <Exec Command="dotnet swagger tofile --output ../Meo/Specs/Individual/CarService.swagger.json $(TargetDir)$(TargetFileName) cars" />
        <!-- Repeat for other groups -->
    </Target>
# Ensure your CustomServerDocumentFilter (if implemented) sets the servers section as you want for each document.
•	Rebuild and run your project, then visit:
•	https://localhost:5002/specs/users/swagger.json
•	https://localhost:5002/specs/cars/swagger.json
•	https://localhost:5002/api/docs (for Swagger UI)



