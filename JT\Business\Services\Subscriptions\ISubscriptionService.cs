namespace JT.Business.Services.Subscriptions;

public interface ISubscriptionService
{
	ValueTask<IImmutableList<SubscriptionPlan>> GetAllPlans(CancellationToken ct = default);
	ValueTask<SubscriptionPlan?> GetPlanById(string planId, CancellationToken ct = default);
	ValueTask<SubscriptionPlan?> GetUserCurrentPlan(Guid userId, CancellationToken ct = default);
	ValueTask<bool> UpgradeUserSubscription(Guid userId, string newPlanId, CancellationToken ct = default);
	ValueTask<bool> IsFeatureAvailable(Guid userId, string feature, CancellationToken ct = default);
	ValueTask<int> GetRemainingDestinations(Guid userId, CancellationToken ct = default);
	ValueTask<int> GetRemainingInvites(Guid userId, CancellationToken ct = default);
	ValueTask<bool> CanSendWhatsAppAlerts(Guid userId, CancellationToken ct = default);
	ValueTask<bool> CanSendSMSAlerts(Guid userId, CancellationToken ct = default);
	ValueTask<double> GetTokenMultiplier(Guid userId, CancellationToken ct = default);
	ValueTask<DateTime?> GetSubscriptionExpiry(Guid userId, CancellationToken ct = default);
	ValueTask<bool> IsSubscriptionExpired(Guid userId, CancellationToken ct = default);
}
