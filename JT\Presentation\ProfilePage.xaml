﻿<Page x:Class="JT.Presentation.ProfilePage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:local="using:JT.Presentation"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:win="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:uer="using:Uno.Extensions.Reactive.UI"
	  xmlns:models="using:JT.Business.Models"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:ut="using:Uno.Themes"
	  xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
	  xmlns:toolkit="using:Uno.UI.Toolkit"
	  xmlns:not_mobile="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:mobile="http://platform.uno/mobile"
	  mc:Ignorable="d mobile"
	  Background="{ThemeResource BackgroundBrush}"
	  utu:StatusBar.Foreground="AutoInverse"
	  utu:StatusBar.Background="{ThemeResource SurfaceInverseBrush}">
	<Page.Resources>
        <DataTemplate x:Key="TransferEmptyTemplate">
			<ScrollViewer HorizontalScrollMode="Disabled">
				<utu:AutoLayout Spacing="24"
								PrimaryAxisAlignment="Center"
								Padding="32,0">
					<BitmapIcon UriSource="{ThemeResource Empty_Box}"
								Width="64"
								Height="72"
								utu:AutoLayout.PrimaryAlignment="Auto"
								utu:AutoLayout.CounterAlignment="Center" />
					<TextBlock TextAlignment="Center"
							   TextWrapping="Wrap"
							   Text="No Transfer Requests"
							   Foreground="{ThemeResource OnSurfaceBrush}"
							   Style="{StaticResource TitleLarge}" />
					<TextBlock TextAlignment="Center"
							   TextWrapping="Wrap"
							   Text="You haven't created any transfer requests yet. Start your career journey by creating your first transfer request!"
							   Foreground="{ThemeResource OnSurfaceBrush}"
							   Style="{StaticResource TitleMedium}" />
					<Button HorizontalContentAlignment="Center"
							VerticalContentAlignment="Center"
							Content="Create Transfer Request"
							Padding="12,10,16,10"
							Foreground="{ThemeResource PrimaryBrush}"
							CornerRadius="20"
							Style="{StaticResource TextButtonStyle}">
						<ut:ControlExtensions.Icon>
							<PathIcon Data="{StaticResource Icon_Add}"
									  Foreground="{ThemeResource PrimaryBrush}" />
						</ut:ControlExtensions.Icon>
					</Button>
				</utu:AutoLayout>
			</ScrollViewer>
		</DataTemplate>
	</Page.Resources>

	<utu:AutoLayout>
		<utu:NavigationBar Content="Profile"
						   Style="{StaticResource JTsModalNavigationBarStyle}"
						   utu:AutoLayout.PrimaryAlignment="Auto">
			<utu:NavigationBar.PrimaryCommands>
				<AppBarButton uen:Navigation.Request="Settings"
							  uen:Navigation.Data="{Binding Profile.Value}"
							  Visibility="{Binding Profile.Value.IsCurrent}">
					<AppBarButton.Icon>
						<PathIcon Data="{StaticResource Icon_Settings}" />
					</AppBarButton.Icon>
				</AppBarButton>
			</utu:NavigationBar.PrimaryCommands>
		</utu:NavigationBar>

		<!-- Debug: Test if ProfileModel is bound -->
		<!--<TextBlock Text="Profile Page Loaded"
				   Padding="16"
				   Foreground="Red"
				   Style="{StaticResource TitleMedium}" />-->

		<uer:FeedView Source="{Binding Profile}">
			<DataTemplate>
				<utu:AutoLayout Spacing="24"
								Padding="32"
								Background="{ThemeResource SurfaceBrush}">
					<utu:AutoLayout Spacing="16">
						<PersonPicture utu:AutoLayout.CounterAlignment="Center"
									   Width="88"
									   Height="88"
									   ProfilePicture="{Binding Data.UrlProfileImage}" />
						<utu:AutoLayout Spacing="4">
							<TextBlock TextAlignment="Center"
									   TextWrapping="Wrap"
									   Text="{Binding Data.FullName}"
									   Foreground="{ThemeResource OnSurfaceBrush}"
									   Style="{StaticResource TitleMedium}" />
							<TextBlock TextAlignment="Center"
									   TextWrapping="Wrap"
									   Text="{Binding Data.Description}"
									   Foreground="{ThemeResource OnSurfaceMediumBrush}"
									   Style="{StaticResource BodySmall}" />
						</utu:AutoLayout>
					</utu:AutoLayout>
					<utu:AutoLayout Spacing="16"
									PrimaryAxisAlignment="Center"
									Orientation="Horizontal">
						<utu:AutoLayout Spacing="4"
										utu:AutoLayout.PrimaryAlignment="Stretch">
							<TextBlock TextAlignment="Center"
									   TextWrapping="Wrap"
									   Text="{Binding Data.TransferRequests}"
									   Foreground="{ThemeResource OnSurfaceBrush}"
									   Style="{StaticResource TitleMedium}" />
							<TextBlock TextAlignment="Center"
									   TextWrapping="Wrap"
									   Text="Transfers"
									   Foreground="{ThemeResource OnSurfaceMediumBrush}"
									   Style="{StaticResource BodySmall}" />
						</utu:AutoLayout>
						<utu:AutoLayout Spacing="4"
										utu:AutoLayout.PrimaryAlignment="Stretch">
							<TextBlock TextAlignment="Center"
									   TextWrapping="Wrap"
									   Text="{Binding Data.Followers}"
									   Foreground="{ThemeResource OnSurfaceBrush}"
									   Style="{StaticResource TitleMedium}" />
							<TextBlock TextAlignment="Center"
									   TextWrapping="Wrap"
									   Text="Followers"
									   Foreground="{ThemeResource OnSurfaceMediumBrush}"
									   Style="{StaticResource BodySmall}" />
						</utu:AutoLayout>
						<utu:AutoLayout Spacing="4"
										utu:AutoLayout.PrimaryAlignment="Stretch">
							<TextBlock TextAlignment="Center"
									   TextWrapping="Wrap"
									   Text="{Binding Data.Following}"
									   Foreground="{ThemeResource OnSurfaceBrush}"
									   Style="{StaticResource TitleMedium}" />
							<TextBlock TextAlignment="Center"
									   TextWrapping="Wrap"
									   Text="Following"
									   Foreground="{ThemeResource OnSurfaceMediumBrush}"
									   Style="{StaticResource BodySmall}" />
						</utu:AutoLayout>
					</utu:AutoLayout>
				</utu:AutoLayout>
			</DataTemplate>
		</uer:FeedView>

		<TextBlock Text="My Transfer Requests"
				   Padding="16"
				   utu:AutoLayout.CounterAlignment="Start"
				   utu:AutoLayout.PrimaryAlignment="Auto"
				   Foreground="{ThemeResource OnBackgroundBrush}"
				   Style="{StaticResource BodyLarge}" />


		<uer:FeedView Source="{Binding TransferRequests}"
					  utu:AutoLayout.PrimaryAlignment="Stretch"
					  NoneTemplate="{StaticResource TransferEmptyTemplate}">
			<DataTemplate>
				<!-- It is necessary to disable the HorizontalScrollMode because of this issue: https://github.com/unoplatform/uno/issues/12871 -->
				<ScrollViewer utu:AutoLayout.PrimaryAlignment="Stretch"
							  HorizontalScrollMode="Disabled"
							  VerticalScrollBarVisibility="Hidden">
					<muxc:ItemsRepeater ItemsSource="{Binding Data}"
										ItemTemplate="{StaticResource TransferTemplate}"
										uen:Navigation.Request="TransferRequestDetails"
										Margin="16,0,16,16">
						<muxc:ItemsRepeater.Layout>
							<muxc:UniformGridLayout MaximumRowsOrColumns="2"
													MinItemWidth="155"
													MinColumnSpacing="10"
													MinRowSpacing="8"
													ItemsStretch="Fill" />
						</muxc:ItemsRepeater.Layout>
					</muxc:ItemsRepeater>
				</ScrollViewer>
			</DataTemplate>
		</uer:FeedView>
	</utu:AutoLayout>
</Page>
