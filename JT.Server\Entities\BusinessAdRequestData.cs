namespace JT.Server.Entities;

public class BusinessAdRequestData
{
	public string? Id { get; set; }
	public Guid? UserId { get; set; }
	public string? CompanyName { get; set; }
	public string? Tagline { get; set; }
	public string? Description { get; set; }
	public string? WebsiteUrl { get; set; }
	public string? Email { get; set; }
	public string? PhoneNumber { get; set; }
	public string? Category { get; set; }
	public decimal? BudgetInOMR { get; set; }
	public int? DurationInDays { get; set; }
	public string? TargetAudience { get; set; }
	public string? Status { get; set; } = "pending"; // pending, approved, rejected, active, expired
	public DateTime? CreatedAt { get; set; }
	public DateTime? ApprovedAt { get; set; }
	public DateTime? StartDate { get; set; }
	public DateTime? EndDate { get; set; }
	public string? ApprovedBy { get; set; }
	public string? RejectionReason { get; set; }
	public int? ViewCount { get; set; }
	public int? ClickCount { get; set; }
}
