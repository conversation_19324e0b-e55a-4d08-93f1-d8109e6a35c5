﻿<!-- This file is generated by a tool from the file ColorPaletteOverride.zip - - YOU SHOULD NOT EDIT IT manually.-->
<ResourceDictionary xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
  <ResourceDictionary.ThemeDictionaries>
    <ResourceDictionary x:Key="Light">
      <Color x:Key="PrimaryColor">#5946D2</Color>
      <Color x:Key="OnPrimaryColor">#FFFFFF</Color>
      <Color x:Key="PrimaryContainerColor">#E5DEFF</Color>
      <Color x:Key="OnPrimaryContainerColor">#170065</Color>
      <Color x:Key="SecondaryColor">#6B4EA2</Color>
      <Color x:Key="OnSecondaryColor">#FFFFFF</Color>
      <Color x:Key="SecondaryContainerColor">#EBDDFF</Color>
      <Color x:Key="OnSecondaryContainerColor">#220555</Color>
      <Color x:Key="TertiaryColor">#0061A4</Color>
      <Color x:Key="OnTertiaryColor">#FFFFFF</Color>
      <Color x:Key="TertiaryContainerColor">#CFE4FF</Color>
      <Color x:Key="OnTertiaryContainerColor">#001D36</Color>
      <Color x:Key="ErrorColor">#B3261E</Color>
      <Color x:Key="ErrorContainerColor">#F9DEDC</Color>
      <Color x:Key="OnErrorColor">#FFFFFF</Color>
      <Color x:Key="OnErrorContainerColor">#410E0B</Color>
      <Color x:Key="BackgroundColor">#FCFBFF</Color>
      <Color x:Key="OnBackgroundColor">#1C1B1F</Color>
      <Color x:Key="SurfaceColor">#FFFFFF</Color>
      <Color x:Key="OnSurfaceColor">#1C1B1F</Color>
      <Color x:Key="SurfaceVariantColor">#F2EFF5</Color>
      <Color x:Key="OnSurfaceVariantColor">#8B8494</Color>
      <Color x:Key="OutlineColor">#79747E</Color>
      <Color x:Key="OnSurfaceInverseColor">#F4EFF4</Color>
      <Color x:Key="SurfaceInverseColor">#313033</Color>
      <Color x:Key="PrimaryInverseColor">#C8BFFF</Color>
      <Color x:Key="SurfaceTintColor">#5946D2</Color>
      <Color x:Key="OutlineVariantColor">#C9C5D0</Color>

      	<Color x:Key="NutritionTrackBackgroundColor">#1C1B1F14</Color>
	    <Color x:Key="NutritionProteinValColor">#159BFF</Color>
	    <Color x:Key="NutritionCarbsValColor">#7A67F8</Color>
	    <Color x:Key="NutritionFatValColor">#F85977</Color>
	    <x:String x:Key="JTsLogoWithIcon">ms-appx:///Assets/Icons/chefslogosignature.png</x:String>

    </ResourceDictionary>
    <ResourceDictionary x:Key="Dark">
      <Color x:Key="PrimaryColor">#C7BFFF</Color>
      <Color x:Key="OnPrimaryColor">#2A009F</Color>
      <Color x:Key="PrimaryContainerColor">#4129BA</Color>
      <Color x:Key="OnPrimaryContainerColor">#E4DFFF</Color>
      <Color x:Key="SecondaryColor">#CDC2DC</Color>
      <Color x:Key="OnSecondaryColor">#332D41</Color>
      <Color x:Key="SecondaryContainerColor">#433C52</Color>
      <Color x:Key="OnSecondaryContainerColor">#EDDFFF</Color>
      <Color x:Key="TertiaryColor">#9FCAFF</Color>
      <Color x:Key="OnTertiaryColor">#003258</Color>
      <Color x:Key="TertiaryContainerColor">#00497D</Color>
      <Color x:Key="OnTertiaryContainerColor">#D1E4FF</Color>
      <Color x:Key="ErrorColor">#FFB4AB</Color>
      <Color x:Key="ErrorContainerColor">#93000A</Color>
      <Color x:Key="OnErrorColor">#690005</Color>
      <Color x:Key="OnErrorContainerColor">#FFDAD6</Color>
      <Color x:Key="BackgroundColor">#1C1B1F</Color>
      <Color x:Key="OnBackgroundColor">#E5E1E6</Color>
      <Color x:Key="SurfaceColor">#302D37</Color>
      <Color x:Key="OnSurfaceColor">#E6E1E5</Color>
      <Color x:Key="SurfaceVariantColor">#47464F</Color>
      <Color x:Key="OnSurfaceVariantColor">#C9C5D0</Color>
      <Color x:Key="OutlineColor">#928F99</Color>
      <Color x:Key="OnSurfaceInverseColor">#1C1B1F</Color>
      <Color x:Key="SurfaceInverseColor">#E6E1E5</Color>
      <Color x:Key="PrimaryInverseColor">#2A009F</Color>
      <Color x:Key="SurfaceTintColor">#C7BFFF</Color>
      <Color x:Key="OutlineVariantColor">#57545D</Color>

      	<Color x:Key="NutritionTrackBackgroundColor">#242629</Color>
	    <Color x:Key="NutritionProteinValColor">#159BFF</Color>
	    <Color x:Key="NutritionCarbsValColor">#7A67F8</Color>
	    <Color x:Key="NutritionFatValColor">#F85977</Color>
	    <x:String x:Key="JTsLogoWithIcon">ms-appx:///Assets/Icons/chefslogosignature_dark.png</x:String>

    </ResourceDictionary>
  </ResourceDictionary.ThemeDictionaries>
    
    <Color x:Key="BrandBlackColor">#242424</Color>
    <SolidColorBrush x:Key="BrandBlackBrush"
					 Color="{StaticResource BrandBlackColor}" />
</ResourceDictionary>
