using JobTransferData = JT.Content.Client.Models.JobTransferData;
using TransferRequestData = JT.Content.Client.Models.TransferRequestData;

namespace JT.Business.Models;

public partial record JobTransfer : IJTEntity
{
	internal JobTransfer(JobTransferData jobTransferData)
	{
		Id = jobTransferData.Id ?? Guid.Empty;
		UserId = jobTransferData.UserId ?? Guid.Empty;
		Name = jobTransferData.Name;
        TransferRequests = jobTransferData.TransferRequests?
			.Select(c => new TransferRequest(c))
			.ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
        JobTransferImages = new JobTransferImages(jobTransferData.TransferRequests?.ToImmutableList() ?? ImmutableList<TransferRequestData>.Empty);
	}

	internal JobTransfer() { TransferRequests = ImmutableList<TransferRequest>.Empty; }

	public Guid Id { get; init; }
	public Guid UserId { get; init; }
	public string? Name { get; init; }
	public int PinsNumber => TransferRequests?.Count ?? 0;
	public IImmutableList<TransferRequest> TransferRequests { get; init; }
	public JobTransferImages? JobTransferImages { get; init; }

	internal JobTransferData ToData() => new()
	{
		Id = Id,
		UserId = UserId,
		Name = Name,
        TransferRequests = TransferRequests?
			.Select(c => c.ToData())
			.ToList()
	};

	internal JobTransferData ToData(IImmutableList<TransferRequest>? transferRequests) => new()
	{
		Id = Id,
		UserId = UserId,
		Name = Name,
        TransferRequests = transferRequests is null
			? TransferRequests?
				.Select(r => r.ToData())
				.ToList()
			: transferRequests
                .Select(r => r.ToData())
				.ToList()
	};

	internal static JobTransferData CreateData(Guid userId, string name, IImmutableList<TransferRequest> transferRequests)
	{
		return new JobTransferData
		{
			Id = Guid.NewGuid(),
			Name = name,
			UserId = userId,
            TransferRequests = transferRequests?.Select(r => r.ToData()).ToList()
		};
	}

	internal UpdateJobTransfer UpdateJobTransfer() => new(this);
}
