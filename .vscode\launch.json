{
  // Use IntelliSense to find out which attributes exist for C# debugging
  // Use hover for the description of the existing attributes
  // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Uno Platform Mobile Debug",
      "type": "Uno",
      "request": "launch",
      // any Uno* task will do, this is simply to satisfy vscode requirement when a launch.json is present
      "preLaunchTask": "Uno: android | Debug | android-x64"
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Uno Platform WebAssembly Debug (Chrome)",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:5000",
      "webRoot": "${workspaceFolder}/JT",
      "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}",
      "timeout": 30000,
      "preLaunchTask": "build-wasm",
      "server": {
        "runtimeExecutable": "dotnet",
        "program": "run",
        "args": ["--no-build","-f","net9.0-browserwasm","--launch-profile", "JT (WebAssembly)"],
        "outputCapture": "std",
        "timeout": 30000,
        "cwd": "${workspaceFolder}/JT"
      }
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Uno Platform WebAssembly Debug (Edge)",
      "type": "msedge",
      "request": "launch",
      "url": "http://localhost:5000",
      "webRoot": "${workspaceFolder}/JT",
      "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}",
      "timeout": 30000,
      "preLaunchTask": "build-wasm",
      "server": {
        "runtimeExecutable": "dotnet",
        "program": "run",
        "args": ["--no-build","-f","net9.0-browserwasm","--launch-profile", "JT (WebAssembly)"],
        "outputCapture": "std",
        "timeout": 30000,
        "cwd": "${workspaceFolder}/JT"
      }
    },
    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/OmniSharp/omnisharp-vscode/blob/master/debugger-launchjson.md
      "name": "Uno Platform Desktop Debug",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build-desktop",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/JT/bin/Debug/net9.0-desktop/JT.dll",
      "args": [],
      "launchSettingsProfile": "JT (Desktop)",
      "env": {
        "DOTNET_MODIFIABLE_ASSEMBLIES": "debug"
      },
      "cwd": "${workspaceFolder}/JT",
      // For more information about the 'console' field, see https://aka.ms/VSCode-CS-LaunchJson-Console
      "console": "internalConsole",
      "stopAtEntry": false
    },
  ]
}
