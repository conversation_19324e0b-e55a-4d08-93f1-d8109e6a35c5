// <auto-generated/>
#pragma warning disable CS0618
using JT.Content.Client.Api.TransferRequest.Status.Item;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace JT.Content.Client.Api.TransferRequest.Status
{
    /// <summary>
    /// Builds and executes requests for operations under \api\TransferRequest\status
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class StatusRequestBuilder : BaseRequestBuilder
    {
        /// <summary>Gets an item from the JT.Content.Client.api.TransferRequest.status.item collection</summary>
        /// <param name="position">Unique identifier of the item</param>
        /// <returns>A <see cref="global::JT.Content.Client.Api.TransferRequest.Status.Item.WithStatusItemRequestBuilder"/></returns>
        public global::JT.Content.Client.Api.TransferRequest.Status.Item.WithStatusItemRequestBuilder this[string position]
        {
            get
            {
                var urlTplParams = new Dictionary<string, object>(PathParameters);
                urlTplParams.Add("status", position);
                return new global::JT.Content.Client.Api.TransferRequest.Status.Item.WithStatusItemRequestBuilder(urlTplParams, RequestAdapter);
            }
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.TransferRequest.Status.StatusRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public StatusRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/TransferRequest/status", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.TransferRequest.Status.StatusRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public StatusRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/TransferRequest/status", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
