﻿<Application x:Class="JT.App"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
       xmlns:utum="using:Uno.Toolkit.UI.Material">

    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>

                <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />

                <MaterialToolkitTheme xmlns="using:Uno.Toolkit.UI.Material"
									  ColorOverrideSource="ms-appx:///Styles/ColorPaletteOverride.xaml"
									  FontOverrideSource="ms-appx:///Styles/MaterialFontsOverride.xaml" />

                <ResourceDictionary Source="ms-appx:///Converters/Converters.xaml" />
                <ResourceDictionary Source="ms-appx:///Styles/FeedView.xaml" />
                <ResourceDictionary Source="ms-appx:///Presentation/Templates/ItemTemplates.xaml" />
                <ResourceDictionary Source="ms-appx:///Styles/Strings.xaml" />
                <ResourceDictionary Source="ms-appx:///Styles/CustomFonts.xaml" />
                <ResourceDictionary Source="ms-appx:///Styles/ChartBrushes.xaml" />
                <ResourceDictionary Source="ms-appx:///Styles/Images.xaml" />
                <ResourceDictionary Source="ms-appx:///Styles/Button.xaml" />
                <ResourceDictionary Source="ms-appx:///Styles/TextBox.xaml" />
                <ResourceDictionary Source="ms-appx:///Styles/NavigationBar.xaml" />
                <ResourceDictionary Source="ms-appx:///Styles/Page.xaml" />
                <ResourceDictionary>
                    <ResourceDictionary.ThemeDictionaries>
                        <ResourceDictionary x:Key="Light">
                            <StaticResource x:Key="FabBackground" ResourceKey="PrimaryBrush" />
                            <StaticResource x:Key="FabBackgroundPressed" ResourceKey="PrimaryBrush" />
                            <StaticResource x:Key="FabBackgroundPointerOver" ResourceKey="PrimaryBrush" />
                        </ResourceDictionary>
                        <ResourceDictionary x:Key="Dark">
                            <StaticResource x:Key="FabBackground" ResourceKey="PrimaryBrush" />
                            <StaticResource x:Key="FabBackgroundPressed" ResourceKey="PrimaryBrush" />
                            <StaticResource x:Key="FabBackgroundPointerOver" ResourceKey="PrimaryBrush" />
                        </ResourceDictionary>
                    </ResourceDictionary.ThemeDictionaries>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>

    <!--<Application.Resources>
    <ResourceDictionary>
      <ResourceDictionary.MergedDictionaries>
         --><!--Load WinUI resources--><!-- 
        <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls" />
        <utum:MaterialToolkitTheme
          ColorOverrideSource="ms-appx:///Styles/ColorPaletteOverride.xaml">
           --><!--NOTE: You can override the default Roboto font by providing your font assets here.--><!-- 
           <utum:MaterialToolkitTheme.FontOverrideDictionary>
            <ResourceDictionary>
              <FontFamily x:Key="MaterialLightFontFamily">ms-appx:///Uno.Fonts.Roboto/Fonts/Roboto-Light.ttf#Roboto</FontFamily>
              <FontFamily x:Key="MaterialMediumFontFamily">ms-appx:///Uno.Fonts.Roboto/Fonts/Roboto-Medium.ttf#Roboto</FontFamily>
              <FontFamily x:Key="MaterialRegularFontFamily">ms-appx:///Uno.Fonts.Roboto/Fonts/Roboto-Regular.ttf#Roboto</FontFamily>
            </ResourceDictionary>
          </utum:MaterialToolkitTheme.FontOverrideDictionary> 
        </utum:MaterialToolkitTheme>
      </ResourceDictionary.MergedDictionaries>

       --><!--Add resources here--><!-- 

    </ResourceDictionary>
  </Application.Resources>-->

</Application>
