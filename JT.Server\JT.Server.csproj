﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\JT.DataContracts\JT.DataContracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" />
    <PackageReference Include="Swashbuckle.AspNetCore" />
    <PackageReference Include="Uno.Wasm.Bootstrap.Server" />
  </ItemGroup>
    <Target Name="PostBuild" AfterTargets="PostBuildEvent">
        <Exec Command="dotnet swagger tofile --output ../JT/Specs/JTApiClient.swagger.json $(TargetDir)$(TargetFileName) v1" />
    </Target>
    <ItemGroup>
        <EmbeddedResource Include="..\AppData\*.json" LinkBase="AppData" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Controllers\**" />
      <Content Remove="Controllers\**" />
      <EmbeddedResource Remove="Controllers\**" />
      <None Remove="Controllers\**" />
    </ItemGroup>
</Project>
