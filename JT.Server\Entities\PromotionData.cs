namespace JT.Server.Entities;

public class PromotionData
{
	public string? Id { get; set; }
	public string? CompanyId { get; set; }
	public string? CompanyName { get; set; }
	public string? Title { get; set; }
	public string? Description { get; set; }
	public string? Type { get; set; } // banner, sponsored_content, job_promotion, directory_listing, event
	public string? Category { get; set; } // industry category
	public string? ImageUrl { get; set; }
	public string? VideoUrl { get; set; }
	public string? WebsiteUrl { get; set; }
	public string? ContactEmail { get; set; }
	public string? ContactPhone { get; set; }
	public decimal? BudgetOMR { get; set; }
	public int? DurationDays { get; set; }
	public string? TargetAudience { get; set; }
	public List<string>? Keywords { get; set; }
	public string? Status { get; set; } = "pending"; // pending, approved, active, paused, completed, rejected
	public string? Priority { get; set; } = "normal"; // low, normal, high, premium
	public DateTime? StartDate { get; set; }
	public DateTime? EndDate { get; set; }
	public DateTime? CreatedAt { get; set; }
	public DateTime? ApprovedAt { get; set; }
	public string? ApprovedBy { get; set; }
	public string? RejectionReason { get; set; }
	public int? ViewCount { get; set; }
	public int? ClickCount { get; set; }
	public int? ConversionCount { get; set; }
	public decimal? TotalSpent { get; set; }
	public decimal? CostPerClick { get; set; }
	public decimal? CostPerView { get; set; }
}
