using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AnalyticsController : ChefsControllerBase
{
	private readonly ILogger<AnalyticsController> _logger;

	public AnalyticsController(ILogger<AnalyticsController> logger)
	{
		_logger = logger;
	}

	[HttpGet]
	public async Task<ActionResult<IEnumerable<AnalyticsData>>> GetAnalytics()
	{
		try
		{
			var analytics = await GetMockData<AnalyticsData>("Analytics.json");
			return Ok(analytics);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting analytics");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("{id}")]
	public async Task<ActionResult<AnalyticsData>> GetAnalytic(string id)
	{
		try
		{
			var analytics = await GetMockData<AnalyticsData>("Analytics.json");
			var analytic = analytics.FirstOrDefault(a => a.Id == id);
			
			if (analytic == null)
			{
				return NotFound();
			}

			return Ok(analytic);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting analytic {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("type/{type}")]
	public async Task<ActionResult<AnalyticsData>> GetAnalyticByType(string type)
	{
		try
		{
			var analytics = await GetMockData<AnalyticsData>("Analytics.json");
			var analytic = analytics.FirstOrDefault(a => 
				string.Equals(a.Type, type, StringComparison.OrdinalIgnoreCase));
			
			if (analytic == null)
			{
				return NotFound();
			}

			return Ok(analytic);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting analytic by type {Type}", type);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost]
	public async Task<ActionResult<AnalyticsData>> CreateOrUpdateAnalytic(AnalyticsData analytic)
	{
		try
		{
			var analytics = await GetMockData<AnalyticsData>("Analytics.json");
			
			// Check if analytic with this type already exists
			var existingIndex = analytics.FindIndex(a => a.Type == analytic.Type);
			
			if (existingIndex >= 0)
			{
				// Update existing
				analytic.Id = analytics[existingIndex].Id;
				analytic.LastUpdated = DateTime.UtcNow;
				analytics[existingIndex] = analytic;
			}
			else
			{
				// Create new
				analytic.Id = $"analytics-{analytic.Type}";
				analytic.LastUpdated = DateTime.UtcNow;
				analytics.Add(analytic);
			}

			await SaveMockData("Analytics.json", analytics);

			return Ok(analytic);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error creating/updating analytic");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("popular-locations")]
	public async Task<ActionResult<object>> GetPopularLocations()
	{
		try
		{
			var analytics = await GetMockData<AnalyticsData>("Analytics.json");
			var locationAnalytic = analytics.FirstOrDefault(a => a.Type == "popular_locations");
			
			if (locationAnalytic?.Data == null)
			{
				return NotFound();
			}

			return Ok(locationAnalytic.Data);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting popular locations");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("trending-skills")]
	public async Task<ActionResult<object>> GetTrendingSkills()
	{
		try
		{
			var analytics = await GetMockData<AnalyticsData>("Analytics.json");
			var skillAnalytic = analytics.FirstOrDefault(a => a.Type == "trending_skills");
			
			if (skillAnalytic?.Data == null)
			{
				return NotFound();
			}

			return Ok(skillAnalytic.Data);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting trending skills");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("salary-trends")]
	public async Task<ActionResult<object>> GetSalaryTrends()
	{
		try
		{
			var analytics = await GetMockData<AnalyticsData>("Analytics.json");
			var salaryAnalytic = analytics.FirstOrDefault(a => a.Type == "salary_trends");
			
			if (salaryAnalytic?.Data == null)
			{
				return NotFound();
			}

			return Ok(salaryAnalytic.Data);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting salary trends");
			return StatusCode(500, "Internal server error");
		}
	}
}
