namespace JT.Business.Services.Promotions;

public interface IPromotionPackageService
{
	// Package Management
	ValueTask<IImmutableList<PromotionPackage>> GetAllPackages(CancellationToken ct = default);
	ValueTask<IImmutableList<PromotionPackage>> GetActivePackages(CancellationToken ct = default);
	ValueTask<IImmutableList<PromotionPackage>> GetPackagesByType(string type, CancellationToken ct = default);
	ValueTask<PromotionPackage?> GetPackageById(string packageId, CancellationToken ct = default);
	ValueTask<PromotionPackage> CreatePackage(PromotionPackage package, CancellationToken ct = default);
	ValueTask<PromotionPackage> UpdatePackage(PromotionPackage package, CancellationToken ct = default);
	ValueTask<bool> DeletePackage(string packageId, CancellationToken ct = default);
	ValueTask<bool> ActivatePackage(string packageId, CancellationToken ct = default);
	ValueTask<bool> DeactivatePackage(string packageId, CancellationToken ct = default);

	// Package Selection & Recommendations
	ValueTask<IImmutableList<PromotionPackage>> GetRecommendedPackages(string companyId, CancellationToken ct = default);
	ValueTask<IImmutableList<PromotionPackage>> GetPopularPackages(CancellationToken ct = default);
	ValueTask<IImmutableList<PromotionPackage>> GetPackagesByPriceRange(decimal minPrice, decimal maxPrice, CancellationToken ct = default);
	ValueTask<PromotionPackage?> GetBestValuePackage(string type, CancellationToken ct = default);

	// Package Analytics
	ValueTask<int> GetPackagePurchaseCount(string packageId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken ct = default);
	ValueTask<decimal> GetPackageRevenue(string packageId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken ct = default);
	ValueTask<IImmutableList<PromotionPackage>> GetTopSellingPackages(int count = 5, CancellationToken ct = default);

	// Pricing & Discounts
	ValueTask<decimal> CalculatePackagePrice(string packageId, int quantity = 1, string? discountCode = null, CancellationToken ct = default);
	ValueTask<bool> ValidateDiscountCode(string discountCode, string packageId, CancellationToken ct = default);
	ValueTask<decimal> GetDiscountAmount(string discountCode, string packageId, CancellationToken ct = default);
}
