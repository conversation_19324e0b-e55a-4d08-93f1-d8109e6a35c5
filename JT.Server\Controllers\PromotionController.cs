using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PromotionController : ChefsControllerBase
{
	private readonly ILogger<PromotionController> _logger;

	public PromotionController(ILogger<PromotionController> logger)
	{
		_logger = logger;
	}

	[HttpGet]
	public async Task<ActionResult<IEnumerable<PromotionData>>> GetPromotions()
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			return Ok(promotions);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting promotions");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("active")]
	public async Task<ActionResult<IEnumerable<PromotionData>>> GetActivePromotions()
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var activePromotions = promotions.Where(p => p.Status == "active").ToList();
			return Ok(activePromotions);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting active promotions");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("{id}")]
	public async Task<ActionResult<PromotionData>> GetPromotion(string id)
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var promotion = promotions.FirstOrDefault(p => p.Id == id);
			
			if (promotion == null)
			{
				return NotFound();
			}

			return Ok(promotion);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting promotion {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost]
	public async Task<ActionResult<PromotionData>> CreatePromotion(PromotionData promotion)
	{
		try
		{
			promotion.Id = Guid.NewGuid().ToString();
			promotion.CreatedAt = DateTime.UtcNow;
			promotion.Status = "pending";
			promotion.ViewCount = 0;
			promotion.ClickCount = 0;
			promotion.ConversionCount = 0;
			promotion.TotalSpent = 0;

			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			promotions.Add(promotion);
			await SaveMockData("Promotions.json", promotions);

			return CreatedAtAction(nameof(GetPromotion), new { id = promotion.Id }, promotion);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error creating promotion");
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPut("{id}")]
	public async Task<ActionResult<PromotionData>> UpdatePromotion(string id, PromotionData promotion)
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var existingIndex = promotions.FindIndex(p => p.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			promotion.Id = id;
			promotions[existingIndex] = promotion;
			await SaveMockData("Promotions.json", promotions);

			return Ok(promotion);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error updating promotion {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpDelete("{id}")]
	public async Task<ActionResult> DeletePromotion(string id)
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var existingIndex = promotions.FindIndex(p => p.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			promotions.RemoveAt(existingIndex);
			await SaveMockData("Promotions.json", promotions);

			return NoContent();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error deleting promotion {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("company/{companyId}")]
	public async Task<ActionResult<IEnumerable<PromotionData>>> GetPromotionsByCompany(string companyId)
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var companyPromotions = promotions.Where(p => p.CompanyId == companyId).ToList();
			return Ok(companyPromotions);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting promotions for company {CompanyId}", companyId);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("type/{type}")]
	public async Task<ActionResult<IEnumerable<PromotionData>>> GetPromotionsByType(string type)
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var typePromotions = promotions.Where(p => 
				string.Equals(p.Type, type, StringComparison.OrdinalIgnoreCase)).ToList();
			return Ok(typePromotions);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting promotions by type {Type}", type);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("category/{category}")]
	public async Task<ActionResult<IEnumerable<PromotionData>>> GetPromotionsByCategory(string category)
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var categoryPromotions = promotions.Where(p => 
				string.Equals(p.Category, category, StringComparison.OrdinalIgnoreCase)).ToList();
			return Ok(categoryPromotions);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting promotions by category {Category}", category);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("{id}/approve")]
	public async Task<ActionResult> ApprovePromotion(string id, [FromBody] ApprovalRequest request)
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var existingIndex = promotions.FindIndex(p => p.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			promotions[existingIndex].Status = "approved";
			promotions[existingIndex].ApprovedAt = DateTime.UtcNow;
			promotions[existingIndex].ApprovedBy = request.ApprovedBy;
			await SaveMockData("Promotions.json", promotions);

			return Ok();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error approving promotion {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("{id}/reject")]
	public async Task<ActionResult> RejectPromotion(string id, [FromBody] RejectionRequest request)
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var existingIndex = promotions.FindIndex(p => p.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			promotions[existingIndex].Status = "rejected";
			promotions[existingIndex].RejectionReason = request.Reason;
			await SaveMockData("Promotions.json", promotions);

			return Ok();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error rejecting promotion {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("{id}/track/view")]
	public async Task<ActionResult> TrackView(string id, [FromBody] TrackingRequest? request = null)
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var existingIndex = promotions.FindIndex(p => p.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			promotions[existingIndex].ViewCount = (promotions[existingIndex].ViewCount ?? 0) + 1;
			await SaveMockData("Promotions.json", promotions);

			return Ok();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error tracking view for promotion {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("{id}/track/click")]
	public async Task<ActionResult> TrackClick(string id, [FromBody] TrackingRequest? request = null)
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var existingIndex = promotions.FindIndex(p => p.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			promotions[existingIndex].ClickCount = (promotions[existingIndex].ClickCount ?? 0) + 1;
			await SaveMockData("Promotions.json", promotions);

			return Ok();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error tracking click for promotion {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("{id}/track/conversion")]
	public async Task<ActionResult> TrackConversion(string id, [FromBody] ConversionTrackingRequest request)
	{
		try
		{
			var promotions = await GetMockData<List<PromotionData>>("Promotions.json");
			var existingIndex = promotions.FindIndex(p => p.Id == id);
			
			if (existingIndex == -1)
			{
				return NotFound();
			}

			promotions[existingIndex].ConversionCount = (promotions[existingIndex].ConversionCount ?? 0) + 1;
			await SaveMockData("Promotions.json", promotions);

			return Ok();
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error tracking conversion for promotion {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("{id}/analytics")]
	public async Task<ActionResult<PromotionAnalyticsData>> GetPromotionAnalytics(string id)
	{
		try
		{
			var analytics = await GetMockData<List<PromotionAnalyticsData>>("PromotionAnalytics.json");
			var promotionAnalytics = analytics.FirstOrDefault(a => a.PromotionId == id);
			
			if (promotionAnalytics == null)
			{
				return NotFound();
			}

			return Ok(promotionAnalytics);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting analytics for promotion {Id}", id);
			return StatusCode(500, "Internal server error");
		}
	}
}

public class ApprovalRequest
{
	public string ApprovedBy { get; set; } = string.Empty;
}

public class RejectionRequest
{
	public string Reason { get; set; } = string.Empty;
}

public class TrackingRequest
{
	public string? UserId { get; set; }
}

public class ConversionTrackingRequest : TrackingRequest
{
	public decimal? Revenue { get; set; }
}
