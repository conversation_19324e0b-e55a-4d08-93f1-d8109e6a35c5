using JT.Business.Services.TransferRequests;
using JT.Business.Services.Users;

namespace JT.Presentation;

public partial record TransferRequestModel
{
	private readonly INavigator _navigator;
	private readonly ITransferRequestService _transferRequestService;
	private readonly IUserService _userService;
	private readonly IMessenger _messenger;

	public TransferRequestModel(
        TransferRequest transferRequest,
        INavigator navigator,
		ITransferRequestService transferRequestService,
		IUserService userService,
		IMessenger messenger)
	{
		_navigator = navigator;
		_transferRequestService = transferRequestService;
		_userService = userService;
		_messenger = messenger;

        TransferRequest = transferRequest;
    }
    public TransferRequest TransferRequest { get; }
    public IState<bool> IsFavorited => State.Value(this, () => TransferRequest.IsFavorite);
    public IState<User> User => State.Async(this, async ct => await _userService.GetById(TransferRequest.UserId, ct))
    .Observe(_messenger, u => u.Id);
    public IFeed<User> CurrentUser => Feed.Async(_userService.GetCurrent);
    //public IListFeed<Ingredient> Ingredients => ListFeed.Async(async ct => await _transferRequestService.GetIngredients(TransferRequest.Id, ct));
    public IListFeed<Step> Steps => ListFeed.Async(async ct => await _transferRequestService.GetSteps(TransferRequest.Id, ct));
    //public IListState<Review> Reviews => ListState
    //.Async(this, async ct => await _transferRequestService.GetReviews(TransferRequest.Id, ct))
    //.Observe(_messenger, r => r.Id);
    //public async ValueTask Like(Review review, CancellationToken ct) =>
    //await _transferRequestService.LikeReview(review, ct);

    //public async ValueTask Dislike(Review review, CancellationToken ct) =>
    //    await _transferRequestService.DislikeReview(review, ct);

    public async ValueTask LiveJobTransfering(IImmutableList<Step> steps) =>
        await _navigator.NavigateRouteAsync(this, "LiveJobTransfering", data: new LiveJobTransferingParameter(TransferRequest, steps));

    public async ValueTask Favorite(CancellationToken ct)
    {
        await _transferRequestService.Favorite(TransferRequest, ct);
        await IsFavorited.UpdateAsync(s => !s);
    }

    //public async Task Share(CancellationToken ct)
    //{
    //    await _shareService.ShareTransfer(TransferRequest, await Steps, ct);
    //}











  //  public string? TransferRequestId { get; set; }
  //  public TransferRequest transferRequest { get; }

  //  public IState<TransferRequest?> TransferRequest => State
		//.Async(this, GetTransferRequest);

	//public IFeed<User> CurrentUser => _userService.User;
    //public IListFeed<Step> Steps => ListFeed.Async(async ct => await _transferRequestService.GetSteps(transferRequest.Id, ct));

 //   private async ValueTask<TransferRequest?> GetTransferRequest(CancellationToken ct)
	//{
	//	if (string.IsNullOrEmpty(TransferRequestId) || !Guid.TryParse(TransferRequestId, out var id))
	//	{
	//		return null;
	//	}

	//	return await _transferRequestService.GetById(id, ct);
	//}

	//public async ValueTask ApplyForTransfer(CancellationToken ct)
	//{
	//	// This would open an application form or contact the employer
	//	await _navigator.NavigateRouteAsync(this, route: "/Main/-/Apply", data: new { TransferRequestId }, cancellation: ct);
	//}

	//public async ValueTask ContactEmployer(string employerId, CancellationToken ct)
	//{
	//	// This would open a messaging interface
	//	await _navigator.NavigateRouteAsync(this, route: "/Main/-/Messages", data: new { EmployerId = employerId }, cancellation: ct);
	//}

	//public ValueTask SaveTransferRequest(CancellationToken ct)
	//{
	//	// Save to user's saved transfers
	//	// Implementation would depend on having a SavedTransfersService
	//	return ValueTask.CompletedTask;
	//}

	//public async ValueTask ShareTransferRequest(CancellationToken ct)
	//{
	//	var transferRequest = await GetTransferRequest(ct);
	//	if (transferRequest != null)
	//	{
	//		// var shareText = $"Check out this job transfer opportunity: {transferRequest.RequestTitle} in {transferRequest.DestinationLocation.City}";
	//		// Use the existing share service
	//		// await _shareService.ShareText(shareText, ct);
	//	}
	//}

	//public async ValueTask ReportTransferRequest(CancellationToken ct)
	//{
	//	// Report inappropriate content
	//	await _navigator.NavigateRouteAsync(this, route: "/Main/-/Report", data: new { TransferRequestId }, cancellation: ct);
	//}

	//public async ValueTask GoBack(CancellationToken ct) =>
	//	await _navigator.NavigateBackAsync(this, cancellation: ct);
}
