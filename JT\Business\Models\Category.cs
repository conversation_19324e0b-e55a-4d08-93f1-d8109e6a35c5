using CategoryData = JT.Content.Client.Models.CategoryData;

namespace JT.Business.Models;

public partial record Category
{
	internal Category(CategoryData CategoryData)
	{
		Id = CategoryData.Id ?? 0;
		Name = CategoryData.Name;
		UrlIcon = CategoryData.UrlIcon;
		Color = CategoryData.Color;
		Description = CategoryData.Description;
	}

	public int Id { get; init; }
	public string? Name { get; init; }
	public string? UrlIcon { get; init; }
	public string? Color { get; init; }
	public string? Description { get; init; }

	// Computed properties
	public string DisplayName => Name ?? "Unknown Category";
	public string IconPath => UrlIcon ?? "ms-appx:///Assets/Categories/default.png";
	public string CategoryColor => Color ?? "#6C757D";

	internal CategoryData ToData() => new()
	{
		Id = Id,
		Name = Name,
		UrlIcon = UrlIcon,
		Color = Color,
		Description = Description
	};
}
