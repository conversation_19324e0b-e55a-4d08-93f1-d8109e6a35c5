using JT.Client.Api;

namespace JT.Business.Services.Promotions;

public class PromotionPackageService : IPromotionPackageService
{
	private readonly JTServiceClient api;

	public PromotionPackageService(JTServiceClient api)
	{
		this.api = api;
	}

	public async ValueTask<IImmutableList<PromotionPackage>> GetAllPackages(CancellationToken ct = default)
	{
		try
		{
			var packagesData = await api.Api.PromotionPackage.GetAsync(cancellationToken: ct);
			return packagesData?.Select(p => new PromotionPackage(p)).ToImmutableList() ?? ImmutableList<PromotionPackage>.Empty;
		}
		catch
		{
			return ImmutableList<PromotionPackage>.Empty;
		}
	}

	public async ValueTask<IImmutableList<PromotionPackage>> GetActivePackages(CancellationToken ct = default)
	{
		try
		{
			var packagesData = await api.Api.PromotionPackage.Active.GetAsync(cancellationToken: ct);
			return packagesData?.Select(p => new PromotionPackage(p)).ToImmutableList() ?? ImmutableList<PromotionPackage>.Empty;
		}
		catch
		{
			return ImmutableList<PromotionPackage>.Empty;
		}
	}

	public async ValueTask<IImmutableList<PromotionPackage>> GetPackagesByType(string type, CancellationToken ct = default)
	{
		try
		{
			var packagesData = await api.Api.PromotionPackage.Type[type].GetAsync(cancellationToken: ct);
			return packagesData?.Select(p => new PromotionPackage(p)).ToImmutableList() ?? ImmutableList<PromotionPackage>.Empty;
		}
		catch
		{
			return ImmutableList<PromotionPackage>.Empty;
		}
	}

	public async ValueTask<PromotionPackage?> GetPackageById(string packageId, CancellationToken ct = default)
	{
		try
		{
			var packageData = await api.Api.PromotionPackage[packageId].GetAsync(cancellationToken: ct);
			return packageData != null ? new PromotionPackage(packageData) : null;
		}
		catch
		{
			return null;
		}
	}

	public async ValueTask<PromotionPackage> CreatePackage(PromotionPackage package, CancellationToken ct = default)
	{
		try
		{
			var packageData = package.ToData();
			var createdData = await api.Api.PromotionPackage.PostAsync(packageData, cancellationToken: ct);
			return new PromotionPackage(createdData);
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Failed to create promotion package: {ex.Message}", ex);
		}
	}

	public async ValueTask<PromotionPackage> UpdatePackage(PromotionPackage package, CancellationToken ct = default)
	{
		try
		{
			var packageData = package.ToData();
			var updatedData = await api.Api.PromotionPackage[package.Id].PutAsync(packageData, cancellationToken: ct);
			return new PromotionPackage(updatedData);
		}
		catch (Exception ex)
		{
			throw new InvalidOperationException($"Failed to update promotion package: {ex.Message}", ex);
		}
	}

	public async ValueTask<bool> DeletePackage(string packageId, CancellationToken ct = default)
	{
		try
		{
			await api.Api.PromotionPackage[packageId].DeleteAsync(cancellationToken: ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> ActivatePackage(string packageId, CancellationToken ct = default)
	{
		try
		{
			await api.Api.PromotionPackage[packageId].Activate.PostAsync(cancellationToken: ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> DeactivatePackage(string packageId, CancellationToken ct = default)
	{
		try
		{
			await api.Api.PromotionPackage[packageId].Deactivate.PostAsync(cancellationToken: ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<IImmutableList<PromotionPackage>> GetRecommendedPackages(string companyId, CancellationToken ct = default)
	{
		// Simple recommendation logic - in production this would be more sophisticated
		var allPackages = await GetActivePackages(ct);
		return allPackages.Where(p => p.IsPopular || p.Type == "banner").Take(3).ToImmutableList();
	}

	public async ValueTask<IImmutableList<PromotionPackage>> GetPopularPackages(CancellationToken ct = default)
	{
		try
		{
			var packagesData = await api.Api.PromotionPackage.Popular.GetAsync(cancellationToken: ct);
			return packagesData?.Select(p => new PromotionPackage(p)).ToImmutableList() ?? ImmutableList<PromotionPackage>.Empty;
		}
		catch
		{
			return ImmutableList<PromotionPackage>.Empty;
		}
	}

	public async ValueTask<IImmutableList<PromotionPackage>> GetPackagesByPriceRange(decimal minPrice, decimal maxPrice, CancellationToken ct = default)
	{
		try
		{
			var packagesData = await api.Api.PromotionPackage.PriceRange.GetAsync(
				queryParameters: new { minPrice, maxPrice }, 
				cancellationToken: ct);
			return packagesData?.Select(p => new PromotionPackage(p)).ToImmutableList() ?? ImmutableList<PromotionPackage>.Empty;
		}
		catch
		{
			return ImmutableList<PromotionPackage>.Empty;
		}
	}

	public async ValueTask<PromotionPackage?> GetBestValuePackage(string type, CancellationToken ct = default)
	{
		var typePackages = await GetPackagesByType(type, ct);
		return typePackages
			.Where(p => p.IsActive)
			.OrderBy(p => p.CostPerDay)
			.FirstOrDefault();
	}

	public async ValueTask<int> GetPackagePurchaseCount(string packageId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken ct = default)
	{
		// This would be implemented with proper analytics tracking
		// For now, return a mock value
		return await Task.FromResult(Random.Shared.Next(10, 100));
	}

	public async ValueTask<decimal> GetPackageRevenue(string packageId, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken ct = default)
	{
		// This would be implemented with proper revenue tracking
		var package = await GetPackageById(packageId, ct);
		var purchaseCount = await GetPackagePurchaseCount(packageId, fromDate, toDate, ct);
		return package?.PriceOMR * purchaseCount ?? 0;
	}

	public async ValueTask<IImmutableList<PromotionPackage>> GetTopSellingPackages(int count = 5, CancellationToken ct = default)
	{
		var allPackages = await GetActivePackages(ct);
		// Simple logic - in production this would use actual sales data
		return allPackages
			.Where(p => p.IsPopular)
			.OrderByDescending(p => p.PriceOMR)
			.Take(count)
			.ToImmutableList();
	}

	public async ValueTask<decimal> CalculatePackagePrice(string packageId, int quantity = 1, string? discountCode = null, CancellationToken ct = default)
	{
		try
		{
			var request = new { Quantity = quantity, DiscountCode = discountCode };
			var response = await api.Api.PromotionPackage[packageId].CalculatePrice.PostAsync(request, cancellationToken: ct);
			return response?.Total ?? 0;
		}
		catch
		{
			var package = await GetPackageById(packageId, ct);
			return package?.PriceOMR * quantity ?? 0;
		}
	}

	public async ValueTask<bool> ValidateDiscountCode(string discountCode, string packageId, CancellationToken ct = default)
	{
		// Simple validation logic - in production this would check a discount database
		return discountCode.ToLower() switch
		{
			"welcome10" => true,
			"bulk20" => true,
			"newclient15" => true,
			_ => false
		};
	}

	public async ValueTask<decimal> GetDiscountAmount(string discountCode, string packageId, CancellationToken ct = default)
	{
		var package = await GetPackageById(packageId, ct);
		if (package == null) return 0;

		return discountCode.ToLower() switch
		{
			"welcome10" => package.PriceOMR * 0.10m,
			"bulk20" => package.PriceOMR * 0.20m,
			"newclient15" => package.PriceOMR * 0.15m,
			_ => 0m
		};
	}
}
