[
  {
    "Id": "9a16d616-c7a2-4517-966b-a8133141d8fb",
    "Name": "Breakfast",
    "UserId": "550e8400-e29b-41d4-a716-446655440002",
    "TransferRequests": [
      {
        "Name": "Fresh Salad Thaid",
        "UserId": "550e8400-e29b-41d4-a716-446655440001",
        "Id": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52",
        "Steps": [
          {
            "Name": "Getting started",
            "TransferTime": {
              "hours": 0,
              "minutes": 3,
              "seconds": 0
            },
            "Transferware": [ "Knife", "Dish" ],
            "Description": "Cut all your vegetables to size.\n\nYou can also use a bag of pre-shredded coleslaw.\n\nI diced the English cucumbers and cut the red bell peppers into thin strips.\n\nYou may also use julienne carrots, lettuce, etc.",
            "Ingredients": [ "Carrot" ],
            "Number": 1,
            "UrlVideo": ""
          },
          {
            "Name": "Second step",
            "TransferTime": {
              "hours": 0,
              "minutes": 6,
              "seconds": 0
            },
            "Transferware": [ "Jar", "Skillet", "Knife", "Cutting Boards" ],
            "Description": "Place the dressing ingredients in a jar and shake together until well combined.\n\nAdd the mix salad greens first, then strategically place the carrot, onion, red pepper and cucumber around the mixed greens.",
            "Ingredients": [ "Tamari Soy", "Carrot", "Onion", "Red Pepper", "Cucumber" ],
            "Number": 2,
            "UrlVideo": ""
          },
          {
            "Name": "Final step",
            "TransferTime": {
              "hours": 0,
              "minutes": 1,
              "seconds": 0
            },
            "Transferware": [ "Fork", "Knife", "Bowl" ],
            "Description": "Finish up with the dressing.\n\nAdd the dressing just before you want to serve the salad otherwise it'll go soggy.",
            "Ingredients": [ "Lime Juice", "Sesame Oil", "Red Chilli" ],
            "Number": 3,
            "UrlVideo": ""
          }
        ],
        "ImageUrl": "ms-appx:///Assets/Transfers/fresh_salad_thaid.png",
        "Serves": 1,
        "TransferTime": {
          "hours": 0,
          "minutes": 10,
          "seconds": 0
        },
        "Difficulty": 0,
        "Ingredients": [
          {
            "UrlIcon": "ms-appx:///Assets/Icons/lemon.png",
            "Name": "Lime Juice",
            "Quantity": "80 ml"
          },
          {
            "UrlIcon": "ms-appx:///Assets/Icons/cucumber.png",
            "Name": "Cucumber",
            "Quantity": "50 ml"
          },
          {
            "UrlIcon": "ms-appx:///Assets/Icons/carrot.png",
            "Name": "Carrot",
            "Quantity": "50 g"
          }
        ],
        "Calories": "350 kcal",
        "Reviews": [
          {
            "Id": "bd95eeef-643e-4eb0-bbd8-1456791040f8",
            "TransferRequestId": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52",
            "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png",
            "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c",
            "PublisherName": "Niki Samantha",
            "Date": "2022-07-12T00:00:00Z",
            "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum",
            "Likes": [ "550e8400-e29b-41d4-a716-446655440002", "550e8400-e29b-41d4-a716-446655440003", "550e8400-e29b-41d4-a716-44665544004" ],
            "Dislikes": [ "550e8400-e29b-41d4-a716-446655440005", "550e8400-e29b-41d4-a716-446655440006" ]
          }
        ],
        "Details": "Fresh, healthy and tasty",
        "Creator": {
          "Id": "550e8400-e29b-41d4-a716-446655440002",
          "UrlProfileImage": "ms-appx:///Assets/Profiles/roberta_any.png",
          "FullName": "roberta any",
          "Description": "Passionate about food and life",
          "Email": "<EMAIL>",
          "PhoneNumber": "",
          "Password": "123",
          "Followers": 450,
          "Following": 124,
          "TransferRequest": 0
        },
        "Category": {
          "Id": 2,
          "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png",
          "Name": "Lunch",
          "Color": "#507FF7"
        },
        "Date": "2022-10-18T00:00:00Z",
        "Save": true
      },
      {
        "Name": "Fresh Salad Thaid",
        "UserId": "550e8400-e29b-41d4-a716-446655440001",
        "Id": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52",
        "Steps": [
          {
            "Name": "Getting started",
            "TransferTime": {
              "hours": 0,
              "minutes": 3,
              "seconds": 0
            },
            "Transferware": [ "Knife", "Dish" ],
            "Description": "Cut all your vegetables to size.\n\nYou can also use a bag of pre-shredded coleslaw.\n\nI diced the English cucumbers and cut the red bell peppers into thin strips.\n\nYou may also use julienne carrots, lettuce, etc.",
            "Ingredients": [ "Carrot" ],
            "Number": 1,
            "UrlVideo": ""
          },
          {
            "Name": "Second step",
            "TransferTime": {
              "hours": 0,
              "minutes": 6,
              "seconds": 0
            },
            "Transferware": [ "Jar", "Skillet", "Knife", "Cutting Boards" ],
            "Description": "Place the dressing ingredients in a jar and shake together until well combined.\n\nAdd the mix salad greens first, then strategically place the carrot, onion, red pepper and cucumber around the mixed greens.",
            "Ingredients": [ "Tamari Soy", "Carrot", "Onion", "Red Pepper", "Cucumber" ],
            "Number": 2,
            "UrlVideo": ""
          },
          {
            "Name": "Final step",
            "TransferTime": {
              "hours": 0,
              "minutes": 1,
              "seconds": 0
            },
            "Transferware": [ "Fork", "Knife", "Bowl" ],
            "Description": "Finish up with the dressing.\n\nAdd the dressing just before you want to serve the salad otherwise it'll go soggy.",
            "Ingredients": [ "Lime Juice", "Sesame Oil", "Red Chilli" ],
            "Number": 3,
            "UrlVideo": ""
          }
        ],
        "ImageUrl": "ms-appx:///Assets/Transfers/fresh_salad_thaid.png",
        "Serves": 1,
        "TransferTime": {
          "hours": 0,
          "minutes": 10,
          "seconds": 0
        },
        "Difficulty": 0,
        "Ingredients": [
          {
            "UrlIcon": "ms-appx:///Assets/Icons/lemon.png",
            "Name": "Lime Juice",
            "Quantity": "80 ml"
          },
          {
            "UrlIcon": "ms-appx:///Assets/Icons/cucumber.png",
            "Name": "Cucumber",
            "Quantity": "50 ml"
          },
          {
            "UrlIcon": "ms-appx:///Assets/Icons/carrot.png",
            "Name": "Carrot",
            "Quantity": "50 g"
          }
        ],
        "Calories": "350 kcal",
        "Reviews": [
          {
            "Id": "bd95eeef-643e-4eb0-bbd8-1456791040f8",
            "TransferRequestId": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52",
            "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png",
            "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c",
            "PublisherName": "Niki Samantha",
            "Date": "2022-07-12T00:00:00Z",
            "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum",
            "Likes": [ "550e8400-e29b-41d4-a716-446655440002", "550e8400-e29b-41d4-a716-446655440003", "550e8400-e29b-41d4-a716-44665544004" ],
            "Dislikes": [ "550e8400-e29b-41d4-a716-446655440005", "550e8400-e29b-41d4-a716-446655440006" ]
          }
        ],
        "Details": "Fresh, healthy and tasty",
        "Creator": {
          "Id": "550e8400-e29b-41d4-a716-446655440002",
          "UrlProfileImage": "ms-appx:///Assets/Profiles/roberta_any.png",
          "FullName": "roberta any",
          "Description": "Passionate about food and life",
          "Email": "<EMAIL>",
          "PhoneNumber": "",
          "Password": "123",
          "Followers": 450,
          "Following": 124,
          "TransferRequest": 0
        },
        "Category": {
          "Id": 2,
          "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png",
          "Name": "Lunch",
          "Color": "#507FF7"
        },
        "Date": "2022-10-18T00:00:00Z",
        "Save": true
      },
      {
        "Name": "Fresh Salad Thaid",
        "UserId": "550e8400-e29b-41d4-a716-446655440002",
        "Id": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52",
        "Steps": [
          {
            "Name": "Getting started",
            "TransferTime": {
              "hours": 0,
              "minutes": 3,
              "seconds": 0
            },
            "Transferware": [ "Knife", "Dish" ],
            "Description": "Cut all your vegetables to size.\n\nYou can also use a bag of pre-shredded coleslaw.\n\nI diced the English cucumbers and cut the red bell peppers into thin strips.\n\nYou may also use julienne carrots, lettuce, etc.",
            "Ingredients": [ "Carrot" ],
            "Number": 1,
            "UrlVideo": ""
          },
          {
            "Name": "Second step",
            "TransferTime": {
              "hours": 0,
              "minutes": 6,
              "seconds": 0
            },
            "Transferware": [ "Jar", "Skillet", "Knife", "Cutting Boards" ],
            "Description": "Place the dressing ingredients in a jar and shake together until well combined.\n\nAdd the mix salad greens first, then strategically place the carrot, onion, red pepper and cucumber around the mixed greens.",
            "Ingredients": [ "Tamari Soy", "Carrot", "Onion", "Red Pepper", "Cucumber" ],
            "Number": 2,
            "UrlVideo": ""
          },
          {
            "Name": "Final step",
            "TransferTime": {
              "hours": 0,
              "minutes": 1,
              "seconds": 0
            },
            "Transferware": [ "Fork", "Knife", "Bowl" ],
            "Description": "Finish up with the dressing.\n\nAdd the dressing just before you want to serve the salad otherwise it'll go soggy.",
            "Ingredients": [ "Lime Juice", "Sesame Oil", "Red Chilli" ],
            "Number": 3,
            "UrlVideo": ""
          }
        ],
        "ImageUrl": "ms-appx:///Assets/Transfers/fresh_salad_thaid.png",
        "Serves": 1,
        "TransferTime": {
          "hours": 0,
          "minutes": 10,
          "seconds": 0
        },
        "Difficulty": 0,
        "Ingredients": [
          {
            "UrlIcon": "ms-appx:///Assets/Icons/lemon.png",
            "Name": "Lime Juice",
            "Quantity": "80 ml"
          },
          {
            "UrlIcon": "ms-appx:///Assets/Icons/cucumber.png",
            "Name": "Cucumber",
            "Quantity": "50 ml"
          },
          {
            "UrlIcon": "ms-appx:///Assets/Icons/carrot.png",
            "Name": "Carrot",
            "Quantity": "50 g"
          }
        ],
        "Calories": "350 kcal",
        "Reviews": [
          {
            "Id": "bd95eeef-643e-4eb0-bbd8-1456791040f8",
            "TransferRequestId": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52",
            "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png",
            "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c",
            "PublisherName": "Niki Samantha",
            "Date": "2022-07-12T00:00:00Z",
            "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum",
            "Likes": [ "550e8400-e29b-41d4-a716-446655440002", "550e8400-e29b-41d4-a716-446655440003", "550e8400-e29b-41d4-a716-44665544004" ],
            "Dislikes": [ "550e8400-e29b-41d4-a716-446655440005", "550e8400-e29b-41d4-a716-446655440006" ]
          }
        ],
        "Details": "Fresh, healthy and tasty",
        "Creator": {
          "Id": "550e8400-e29b-41d4-a716-446655440001",
          "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png",
          "FullName": "James Bondi",
          "Description": "Passionate about food and life",
          "Email": "<EMAIL>",
          "PhoneNumber": "",
          "Password": "123",
          "Followers": 450,
          "Following": 124,
          "TransferRequest": 0
        },
        "Category": {
          "Id": 2,
          "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png",
          "Name": "Lunch",
          "Color": "#507FF7"
        },
        "Date": "2022-10-18T00:00:00Z",
        "Save": true
      }
    ]
  },
  {
    "Id": "a91c1918-f025-422a-9361-30f678b2bc3d",
    "Name": "Lunch Specials",
    "UserId": "550e8400-e29b-41d4-a716-446655440001",
    "TransferRequests": [
      {
        "Name": "Spicy Chicken Wrap",
        "UserId": "550e8400-e29b-41d4-a716-446655440003",
        "Id": "12345678-1234-5678-1234-567812345678",
        "Steps": [
          {
            "Name": "Prepare Ingredients",
            "TransferTime": {
              "hours": 0,
              "minutes": 5,
              "seconds": 0
            },
            "Transferware": [ "Knife", "Bowl" ],
            "Description": "Chop the vegetables and prepare the chicken.",
            "Ingredients": [ "Chicken Breast", "Lettuce", "Tomato" ],
            "Number": 1,
            "UrlVideo": ""
          },
          {
            "Name": "Cook Chicken",
            "TransferTime": {
              "hours": 0,
              "minutes": 10,
              "seconds": 0
            },
            "Transferware": [ "Pan", "Spatula" ],
            "Description": "Cook the chicken in a pan until golden brown.",
            "Ingredients": [],
            "Number": 2,
            "UrlVideo": ""
          },
          {
            "Name": "Assemble Wrap",
            "TransferTime": {
              "hours": 0,
              "minutes": 2,
              "seconds": 0
            },
            "Transferware": [],
            "Description": "",
            "Ingredients": [],
            "Number": 3,
            "UrlVideo": ""
          }
        ]
        // Additional properties can be added here as needed
      }
    ]
  },
  {
    "Id": "f0d70ed4-a126-45a6-96a8-60b8b93dcd89",
    "Name": "Breakfast",
    "UserId": "550e8400-e29b-41d4-a716-446655440002",
    "TransferRequests": [
      {
        "Name": "Fresh Salad Thaid",
        "UserId": "550e8400-e29b-41d4-a716-446655440002",
        "Id": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52",
        "Steps": [
          {
            "Name": "Getting started",
            "TransferTime": {
              "hours": 0,
              "minutes": 3,
              "seconds": 0
            },
            "Transferware": [ "Knife", "Dish" ],
            "Description": "Cut all your vegetables to size.\n\nYou can also use a bag of pre-shredded coleslaw.\n\nI diced the English cucumbers and cut the red bell peppers into thin strips.\n\nYou may also use julienne carrots, lettuce, etc.",
            "Ingredients": [ "Carrot" ],
            "Number": 1,
            "UrlVideo": ""
          },
          {
            "Name": "Second step",
            "TransferTime": {
              "hours": 0,
              "minutes": 6,
              "seconds": 0
            },
            "Transferware": [ "Jar", "Skillet", "Knife", "Cutting Boards" ],
            "Description": "Place the dressing ingredients in a jar and shake together until well combined.\n\nAdd the mix salad greens first, then strategically place the carrot, onion, red pepper and cucumber around the mixed greens.",
            "Ingredients": [ "Tamari Soy", "Carrot", "Onion", "Red Pepper", "Cucumber" ],
            "Number": 2,
            "UrlVideo": ""
          },
          {
            "Name": "Final step",
            "TransferTime": {
              "hours": 0,
              "minutes": 1,
              "seconds": 0
            },
            "Transferware": [ "Fork", "Knife", "Bowl" ],
            "Description": "Finish up with the dressing.\n\nAdd the dressing just before you want to serve the salad otherwise it'll go soggy.",
            "Ingredients": [ "Lime Juice", "Sesame Oil", "Red Chilli" ],
            "Number": 3,
            "UrlVideo": ""
          }
        ],
        "ImageUrl": "ms-appx:///Assets/Transfers/fresh_salad_thaid.png",
        "Serves": 1,
        "TransferTime": {
          "hours": 0,
          "minutes": 10,
          "seconds": 0
        },
        "Difficulty": 0,
        "Ingredients": [
          {
            "UrlIcon": "ms-appx:///Assets/Icons/lemon.png",
            "Name": "Lime Juice",
            "Quantity": "80 ml"
          },
          {
            "UrlIcon": "ms-appx:///Assets/Icons/cucumber.png",
            "Name": "Cucumber",
            "Quantity": "50 ml"
          },
          {
            "UrlIcon": "ms-appx:///Assets/Icons/carrot.png",
            "Name": "Carrot",
            "Quantity": "50 g"
          }
        ],
        "Calories": "350 kcal",
        "Reviews": [
          {
            "Id": "bd95eeef-643e-4eb0-bbd8-1456791040f8",
            "TransferRequestId": "0007e65f-3d5c-4ca3-842f-134b7f4a8b52",
            "UrlAuthorImage": "ms-appx:///Assets/Profiles/niki_samantha.png",
            "CreatedBy": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c",
            "PublisherName": "Niki Samantha",
            "Date": "2022-07-12T00:00:00Z",
            "Description": "Lorem Ipsum tempor incididunt ut labore et dolore,inise voluptate velit esse cillum",
            "Likes": [ "550e8400-e29b-41d4-a716-446655440002", "550e8400-e29b-41d4-a716-446655440003", "550e8400-e29b-41d4-a716-44665544004" ],
            "Dislikes": [ "550e8400-e29b-41d4-a716-446655440005", "550e8400-e29b-41d4-a716-446655440006" ]
          }
        ],
        "Details": "Fresh, healthy and tasty",
        "Creator": {
          "Id": "550e8400-e29b-41d4-a716-446655440001",
          "UrlProfileImage": "ms-appx:///Assets/Profiles/james_bondi.png",
          "FullName": "James Bondi",
          "Description": "Passionate about food and life",
          "Email": "<EMAIL>",
          "PhoneNumber": "",
          "Password": "123",
          "Followers": 450,
          "Following": 124,
          "TransferRequest": 0
        },
        "Category": {
          "Id": 2,
          "UrlIcon": "ms-appx:///Assets/Icons/hamburger.png",
          "Name": "Lunch",
          "Color": "#507FF7"
        },
        "Date": "2022-10-18T00:00:00Z",
        "Save": true
      }
    ]
  }
]
