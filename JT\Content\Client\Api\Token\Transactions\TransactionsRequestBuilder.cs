// <auto-generated/>
#pragma warning disable CS0618
using JT.Content.Client.Api.Token.Transactions.TypeNamespace;
using Microsoft.Kiota.Abstractions.Extensions;
using Microsoft.Kiota.Abstractions;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
namespace JT.Content.Client.Api.Token.Transactions
{
    /// <summary>
    /// Builds and executes requests for operations under \api\Token\transactions
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCode("Kiota", "1.0.0")]
    public partial class TransactionsRequestBuilder : BaseRequestBuilder
    {
        /// <summary>The type property</summary>
        public global::JT.Content.Client.Api.Token.Transactions.TypeNamespace.TypeRequestBuilder Type
        {
            get => new global::JT.Content.Client.Api.Token.Transactions.TypeNamespace.TypeRequestBuilder(PathParameters, RequestAdapter);
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.Token.Transactions.TransactionsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="pathParameters">Path parameters for the request</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public TransactionsRequestBuilder(Dictionary<string, object> pathParameters, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Token/transactions", pathParameters)
        {
        }
        /// <summary>
        /// Instantiates a new <see cref="global::JT.Content.Client.Api.Token.Transactions.TransactionsRequestBuilder"/> and sets the default values.
        /// </summary>
        /// <param name="rawUrl">The raw URL to use for the request builder.</param>
        /// <param name="requestAdapter">The request adapter to use to execute the requests.</param>
        public TransactionsRequestBuilder(string rawUrl, IRequestAdapter requestAdapter) : base(requestAdapter, "{+baseurl}/api/Token/transactions", rawUrl)
        {
        }
    }
}
#pragma warning restore CS0618
