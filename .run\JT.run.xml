<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="JT (Desktop)" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
    <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/JT/JT.csproj" />
    <option name="LAUNCH_PROFILE_TFM" value="net9.0-desktop" />
    <option name="LAUNCH_PROFILE_NAME" value="JT (Desktop)" />
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
    <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
    <option name="SEND_DEBUG_REQUEST" value="1" />
    <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
  <configuration default="false" name="JT (WebAssembly IIS Express)" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
    <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/JT/JT.csproj" />
    <option name="LAUNCH_PROFILE_TFM" value="net9.0-browserwasm" />
    <option name="LAUNCH_PROFILE_NAME" value="JT (WebAssembly IIS Express)" />
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
    <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
    <option name="SEND_DEBUG_REQUEST" value="1" />
    <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
  <configuration default="false" name="JT (WebAssembly)" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
    <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/JT/JT.csproj" />
    <option name="LAUNCH_PROFILE_TFM" value="net9.0-browserwasm" />
    <option name="LAUNCH_PROFILE_NAME" value="JT (WebAssembly)" />
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
    <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
    <option name="SEND_DEBUG_REQUEST" value="1" />
    <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
  <configuration default="false" name="JT (WinAppSDK Unpackaged)" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
    <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/JT/JT.csproj" />
    <option name="LAUNCH_PROFILE_TFM" value="net9.0-windows10.0.26100.0" />
    <option name="LAUNCH_PROFILE_NAME" value="JT (WinAppSDK Unpackaged)" />
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
    <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
    <option name="SEND_DEBUG_REQUEST" value="1" />
    <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
  <configuration default="false" name="JT.Server (Server IIS Express)" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
    <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/JT.Server/JT.Server.csproj" />
    <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
    <option name="LAUNCH_PROFILE_NAME" value="JT.Server (Server IIS Express)" />
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
    <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
    <option name="SEND_DEBUG_REQUEST" value="1" />
    <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
  <configuration default="false" name="JT.Server (Server)" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
    <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/JT.Server/JT.Server.csproj" />
    <option name="LAUNCH_PROFILE_TFM" value="net9.0" />
    <option name="LAUNCH_PROFILE_NAME" value="JT.Server (Server)" />
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
    <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
    <option name="SEND_DEBUG_REQUEST" value="1" />
    <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>
