namespace JT.Business.Services.Payments;

public interface IPaymentService
{
	ValueTask<IImmutableList<PaymentTransaction>> GetUserPayments(Guid userId, CancellationToken ct = default);
	ValueTask<PaymentTransaction?> GetPaymentById(string paymentId, CancellationToken ct = default);
	ValueTask<PaymentTransaction> CreatePayment(Guid userId, string subscriptionPlanId, string paymentMethod, CancellationToken ct = default);
	ValueTask<PaymentTransaction> ProcessPayment(string paymentId, object gatewayResponse, CancellationToken ct = default);
	ValueTask<PaymentTransaction> UpdatePaymentStatus(string paymentId, string status, string? errorMessage = null, CancellationToken ct = default);
	ValueTask<bool> RefundPayment(string paymentId, CancellationToken ct = default);
	ValueTask<IImmutableList<PaymentTransaction>> GetPaymentsByStatus(string status, CancellationToken ct = default);
	ValueTask<decimal> GetTotalRevenue(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken ct = default);
}
