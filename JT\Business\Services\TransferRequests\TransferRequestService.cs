using CommunityToolkit.Mvvm.Messaging;
using JT.Business.Models;
using JT.Business.Services.Users;
using JT.Content.Client;

namespace JT.Business.Services.TransferRequests;

public class TransferRequestService(
    JTApiClient api, 
    IUserService userService, 
    IWritableOptions<SearchHistory> searchOptions,
    IMessenger messenger) 
    : ITransferRequestService
{
    private int _lastTextLength;
    
    public async ValueTask<IImmutableList<TransferRequest>> GetAll(CancellationToken ct = default)
	{
		var transferRequestsData = await api.Api.TransferRequest.GetAsync(cancellationToken: ct);
		return transferRequestsData?.Select(r => new TransferRequest(r)).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetByUser(Guid userId, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => r.UserId == userId).ToImmutableList();
	}

	public async ValueTask<TransferRequest?> GetById(Guid id, CancellationToken ct = default)
	{
		try
		{
			var transferRequestData = await api.Api.TransferRequest[id].GetAsync(cancellationToken: ct);
			return transferRequestData != null ? new TransferRequest(transferRequestData) : null;
		}
		catch
		{
			return null;
		}
	}

    public IListState<TransferRequest> FavoritedTransferRequests => ListState<TransferRequest>.Async(this, GetFavorited);

    public async ValueTask<IImmutableList<TransferRequest>> GetFavoritedWithPagination(uint pageSize, uint firstItemIndex, CancellationToken ct)
    {
        var favoritedTransferRequests = await GetFavorited(ct);
        return favoritedTransferRequests
            .Skip((int)firstItemIndex)
            .Take((int)pageSize)
            .ToImmutableList();
    }

    public IImmutableList<string> GetSearchHistory()
    => searchOptions.Value.Searches.Take(3).ToImmutableList();

    public async ValueTask Favorite(TransferRequest transferRequest, CancellationToken ct)
    {
        var currentUser = await userService.GetCurrent(ct);
        var updatedTransferRequest = transferRequest with { IsFavorite = !transferRequest.IsFavorite };

        // Note: This would typically be a POST to add/remove favorite, but the API structure suggests GET only
        // This is a placeholder implementation - the actual API call would depend on the server implementation
        await api.Api.TransferRequest.Favorited.PostAsync(q =>
        {
            q.QueryParameters.TransferRequestId = updatedTransferRequest.Id;
            q.QueryParameters.UserId = currentUser.Id;
        }, cancellationToken: ct);

        if (updatedTransferRequest.IsFavorite)
        {
            await FavoritedTransferRequests.AddAsync(updatedTransferRequest, ct: ct);
        }
        else
        {
            await FavoritedTransferRequests.RemoveAllAsync(r => r.Id == updatedTransferRequest.Id, ct: ct);
        }

        // Note: EntityMessage and EntityChange types need to be defined
        // For now, commenting out the messaging until these types are created
        messenger.Send(new EntityMessage<TransferRequest>(EntityChange.Updated, updatedTransferRequest));
    }
    public async ValueTask<IImmutableList<TransferRequest>> GetByCategory(int categoryId, CancellationToken ct)
    {
        var transferRequestData = await api.Api.TransferRequest.GetAsync(cancellationToken: ct);
        return transferRequestData?.Where(r => r.Category?.Id == categoryId).Select(r => new TransferRequest(r)).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
    }

    public async ValueTask<IImmutableList<Category>> GetCategories(CancellationToken ct)
    {
        var categoriesData = await api.Api.TransferRequest.Categories.GetAsync(cancellationToken: ct);
        return categoriesData?.Select(c => new Category(c)).ToImmutableList() ?? ImmutableList<Category>.Empty;
    }

    public async ValueTask<IImmutableList<CategoryWithCount>> GetCategoriesWithCount(CancellationToken ct)
    {
        var categories = await GetCategories(ct);
        var tasks = categories.Select(async category =>
        {
            var transferRequestByCategory = await GetByCategory(category.Id, ct);
            return new CategoryWithCount(transferRequestByCategory.Count, category);
        });

        var categoriesWithCount = await Task.WhenAll(tasks);
        return categoriesWithCount.ToImmutableList();
    }
    
    public async ValueTask<IImmutableList<TransferRequest>> GetByStatus(string status, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => string.Equals(r.Status, status, StringComparison.OrdinalIgnoreCase)).ToImmutableList();
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetByLocation(string location, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => 
			r.DestinationLocation.City?.Contains(location, StringComparison.OrdinalIgnoreCase) == true ||
			r.DestinationLocation.State?.Contains(location, StringComparison.OrdinalIgnoreCase) == true ||
			r.DestinationLocation.Country?.Contains(location, StringComparison.OrdinalIgnoreCase) == true
		).ToImmutableList();
	}

	public async ValueTask<IImmutableList<TransferRequest>> GetByIndustry(string industry, CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests.Where(r => string.Equals(r.Industry, industry, StringComparison.OrdinalIgnoreCase)).ToImmutableList();
	}

    public async ValueTask<IImmutableList<TransferRequest>> GetPopular(CancellationToken ct)
    {
        var popularTransfersData = await api.Api.TransferRequest.Popular.GetAsync(cancellationToken: ct);
        return popularTransfersData?.Select(r => new TransferRequest(r)).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
    }
    public async ValueTask<IImmutableList<TransferRequest>> GetRecommended(CancellationToken ct)
    {
        var transfersData = await api.Api.TransferRequest.GetAsync(cancellationToken: ct);
        return transfersData?.Select(r => new TransferRequest(r)).OrderBy(_ => Guid.NewGuid()).Take(4).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
    }

    public async ValueTask<IImmutableList<TransferRequest>> GetFromJTs(CancellationToken ct)
    {
        var transfersData = await api.Api.TransferRequest.GetAsync(cancellationToken: ct);
        return transfersData?.Select(r => new TransferRequest(r)).OrderBy(_ => Guid.NewGuid()).Take(4).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
    }
    public async ValueTask<IImmutableList<TransferRequest>> Search(string term, SearchFilter filter, CancellationToken ct)
    {
        var transfersToSearch = filter.FilterGroup switch
        {
            FilterGroup.Popular => await GetPopular(ct),
            FilterGroup.Trending => await GetTrending(ct),
            FilterGroup.Recent => await GetRecent(ct),
            _ => await GetAll(ct)
        };

        if (string.IsNullOrWhiteSpace(term))
        {
            _lastTextLength = 0;
            return transfersToSearch;
        }
        else
        {
            await SaveSearchHistory(term);
            return GetTransfersByText(transfersToSearch, term);
        }
    }
    //public async ValueTask<IImmutableList<TransferRequest>> Search(string query, CancellationToken ct = default)
    //{
    //	var allRequests = await GetAll(ct);
    //	return allRequests.Where(r =>
    //		r.RequestTitle?.Contains(query, StringComparison.OrdinalIgnoreCase) == true ||
    //		r.Industry?.Contains(query, StringComparison.OrdinalIgnoreCase) == true ||
    //		r.Category?.Name?.Contains(query, StringComparison.OrdinalIgnoreCase) == true ||
    //		r.RequiredSkills.Any(skill => skill.Contains(query, StringComparison.OrdinalIgnoreCase))
    //	).ToImmutableList();
    //}

    public async ValueTask<TransferRequest> Create(TransferRequest transferRequest, CancellationToken ct = default)
	{
		var transferRequestData = transferRequest.ToData();
		var createdData = await api.Api.TransferRequest.CreatetransferRequest.PostAsync(transferRequestData, cancellationToken: ct);
		return new TransferRequest(createdData ?? throw new InvalidOperationException("Failed to create transfer request"));
	}

	public async ValueTask<TransferRequest> Update(TransferRequest transferRequest, CancellationToken ct = default)
	{
		var transferRequestData = transferRequest.ToData();
		var updatedData = await api.Api.TransferRequest[transferRequest.Id].PutAsync(transferRequestData, cancellationToken: ct);
		return new TransferRequest(updatedData ?? throw new InvalidOperationException("Failed to update transfer request"));
	}

	public async ValueTask<bool> Delete(Guid id, CancellationToken ct = default)
	{
		try
		{
			await api.Api.TransferRequest[id].DeleteAsync(cancellationToken: ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> UpdateStatus(Guid id, string status, CancellationToken ct = default)
	{
		try
		{
			var transferRequest = await GetById(id, ct);
			if (transferRequest == null) return false;

			var updatedRequest = transferRequest with { Status = status };
			await Update(updatedRequest, ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

	public async ValueTask<bool> IncrementViewCount(Guid id, CancellationToken ct = default)
	{
		try
		{
			var transferRequest = await GetById(id, ct);
			if (transferRequest == null) return false;

			var updatedRequest = transferRequest with { ViewCount = transferRequest.ViewCount + 1 };
			await Update(updatedRequest, ct);
			return true;
		}
		catch
		{
			return false;
		}
	}

    public async ValueTask<IImmutableList<TransferRequest>> GetTrending(CancellationToken ct)
    {
        var trendingTransfersData = await api.Api.TransferRequest.Trending.GetAsync(cancellationToken: ct);
        return trendingTransfersData?.Select(r => new TransferRequest(r)).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
    }
 //   public async ValueTask<IImmutableList<TransferRequest>> GetTrending(CancellationToken ct = default)
	//{
	//	var allRequests = await GetAll(ct);
	//	return allRequests
	//		.Where(r => r.IsActive)
	//		.OrderByDescending(r => r.ViewCount)
	//		.ThenByDescending(r => r.InterestedEmployers)
	//		.Take(10)
	//		.ToImmutableList();
	//}

	public async ValueTask<IImmutableList<TransferRequest>> GetExpiringSoon(CancellationToken ct = default)
	{
		var allRequests = await GetAll(ct);
		return allRequests
			.Where(r => r.IsActive && r.DaysUntilExpiration <= 7)
			.OrderBy(r => r.DaysUntilExpiration)
			.ToImmutableList();
	}

	//public async ValueTask<IImmutableList<Category>> GetCategories(CancellationToken ct = default)
	//{
	//	var categoriesData = await api.Api.JobCategory.Categories.GetAsync(cancellationToken: ct);
	//	return categoriesData?.Select(c => new Category(c)).ToImmutableList() ?? ImmutableList<Category>.Empty;
	//}

	private async ValueTask<IImmutableList<TransferRequest>> GetFavorited(CancellationToken ct)
	{
		var currentUser = await userService.GetCurrent(ct);
		var favoritedTransferRequestsData = await api.Api.TransferRequest.Favorited.GetAsync(config => config.QueryParameters.UserId = currentUser.Id, cancellationToken: ct);
		return favoritedTransferRequestsData?.Select(r => new TransferRequest(r)).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
	}
   
    public async ValueTask<IImmutableList<TransferRequest>> GetRecent(CancellationToken ct)
    {
        var transferRequestsData = await api.Api.TransferRequest.GetAsync(cancellationToken: ct);
        return transferRequestsData?.Select(r => new TransferRequest(r)).OrderByDescending(x => x.Date).Take(7).ToImmutableList() ?? ImmutableList<TransferRequest>.Empty;
    }
    public async ValueTask<IImmutableList<Step>> GetSteps(Guid transferId, CancellationToken ct)
    {
        var stepsData = await api.Api.TransferRequest[transferId].Steps.GetAsync(cancellationToken: ct);
        return stepsData?.Select(x => new Step(x)).ToImmutableList() ?? ImmutableList<Step>.Empty;
    }

    private async Task SaveSearchHistory(string text)
    {
        if (_lastTextLength <= text.Length) _lastTextLength = text.Length;

        var searchHistory = searchOptions.Value.Searches;
        if (!string.IsNullOrWhiteSpace(text))
        {
            if (searchHistory.Count == 0 || _lastTextLength == 1)
            {
                await searchOptions.UpdateAsync(h => h with { Searches = searchHistory.Prepend(text).ToList() });
            }
            else if (searchHistory.FirstOrDefault() is { } latestTerm
                     && (text.Contains(latestTerm) || latestTerm.Contains(text))
                     && _lastTextLength == text.Length)
            {
                await searchOptions.UpdateAsync(h => h with
                {
                    Searches = searchHistory.Skip(1).Prepend(text).ToList(),
                });
            }
        }
    }

    private IImmutableList<TransferRequest> GetTransfersByText(IEnumerable<TransferRequest> transfers, string text)
    => transfers
        .Where(r => r.Name?.Contains(text, StringComparison.OrdinalIgnoreCase) == true
                    || r.Category?.Name?.Contains(text, StringComparison.OrdinalIgnoreCase) == true)
        .ToImmutableList();
}
