using System.Text.Json.Serialization;
using System.Xml;

namespace JT.Server.Serialization;

/// <summary>
/// Converts a <see cref="TimeSpan"/> to and from a string in the format of <c>"c"</c> (constant).
/// </summary>
public class TimeSpanStringConverter : JsonConverter<TimeSpan>
{
    /// <inheritdoc/>
    public override TimeSpan Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.Null)
        { 
            return TimeSpan.Zero;
        }

        if (reader.TokenType == JsonTokenType.String)
        {
            var s = reader.GetString();
            return string.IsNullOrEmpty(s) ? TimeSpan.Zero : XmlConvert.ToTimeSpan(s);
        }

        throw new JsonException("Unexpected token type for TimeSpan");
    }

    /// <inheritdoc/>
    public override void Write(Utf8JsonWriter writer, TimeSpan value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(XmlConvert.ToString(value));
    }
}