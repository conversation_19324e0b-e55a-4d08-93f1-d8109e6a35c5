//using Foundation;
using Uno.Resizetizer;
using System.Diagnostics;
using System.Text.Json;

#if __IOS__
using Foundation;
#endif
//using LiveChartsCore;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Authentication;
using Microsoft.Kiota.Http.HttpClientLibrary;
using Uno.Extensions.Http.Kiota;
namespace JT;

public partial class App : Application
{
    /// <summary>
    /// Initializes the singleton application object. This is the first line of authored code
    /// executed, and as such is the logical equivalent of main() or WinMain().
    /// </summary>
    public App()
    {
        this.InitializeComponent();
    }

    public static Window? MainWindow;

    public static ShellControl? Shell;
    public static IHost? Host { get; private set; }

    protected override async void OnLaunched(LaunchActivatedEventArgs args)
    {
#if __IOS__ && !__MACCATALYST__ && USE_UITESTS && HAS_TESTCLOUD_AGENT
		Xamarin.Calabash.Start();
#endif

        var builder = this.CreateBuilder(args);
        ConfigureAppBuilder(builder);
        MainWindow = builder.Window;

#if DEBUG
        MainWindow.UseStudio();
#endif

        Host = await builder.NavigateAsync<ShellControl>();
        Shell = MainWindow.Content as ShellControl;
    }

    /// <summary>
    /// Configures global Uno Platform logging
    /// </summary>
    public static void InitializeLogging()
    {
        //-:cnd:noEmit
#if true // DEBUG
        // Logging is disabled by default for release builds, as it incurs a significant
        // initialization cost from Microsoft.Extensions.Logging setup. If startup performance
        // is a concern for your application, keep this disabled. If you're running on the web or
        // desktop targets, you can use URL or command line parameters to enable it.
        //
        // For more performance documentation: https://platform.uno/docs/articles/Uno-UI-Performance.html

        var factory = LoggerFactory.Create(builder =>
        {
#if __WASM__
			builder.AddProvider(new global::Uno.Extensions.Logging.WebAssembly.WebAssemblyConsoleLoggerProvider());
#elif __IOS__ || __MACCATALYST__
            builder.AddProvider(new global::Uno.Extensions.Logging.OSLogLoggerProvider());
            builder.AddConsole();
#else
			builder.AddConsole();
#endif

            // Exclude logs below this level
            builder.SetMinimumLevel(LogLevel.Information);

            // Default filters for Uno Platform namespaces
            builder.AddFilter("Uno", LogLevel.Information);
            builder.AddFilter("Windows", LogLevel.Information);
            builder.AddFilter("Microsoft", LogLevel.Information);

            // Generic Xaml events
            // builder.AddFilter("Microsoft.UI.Xaml", LogLevel.Debug );
            // builder.AddFilter("Microsoft.UI.Xaml.VisualStateGroup", LogLevel.Debug );
            // builder.AddFilter("Microsoft.UI.Xaml.StateTriggerBase", LogLevel.Debug );
            // builder.AddFilter("Microsoft.UI.Xaml.UIElement", LogLevel.Debug );
            // builder.AddFilter("Microsoft.UI.Xaml.FrameworkElement", LogLevel.Trace );

            // Layouter specific messages
            // builder.AddFilter("Microsoft.UI.Xaml.Controls", LogLevel.Debug );
            // builder.AddFilter("Microsoft.UI.Xaml.Controls.Layouter", LogLevel.Debug );
            // builder.AddFilter("Microsoft.UI.Xaml.Controls.Panel", LogLevel.Debug );

            // builder.AddFilter("Windows.Storage", LogLevel.Debug );

            // Binding related messages
            // builder.AddFilter("Microsoft.UI.Xaml.Data", LogLevel.Debug );
            // builder.AddFilter("Microsoft.UI.Xaml.Data", LogLevel.Debug );

            // Binder memory references tracking
            // builder.AddFilter("Uno.UI.DataBinding.BinderReferenceHolder", LogLevel.Debug );

            // DevServer and HotReload related
            // builder.AddFilter("Uno.UI.RemoteControl", LogLevel.Information);

            // Debug JS interop
            // builder.AddFilter("Uno.Foundation.WebAssemblyRuntime", LogLevel.Debug );
        });

        global::Uno.Extensions.LogExtensionPoint.AmbientLoggerFactory = factory;

#if HAS_UNO
        global::Uno.UI.Adapter.Microsoft.Extensions.Logging.LoggingAdapter.Initialize();
#endif
#endif
        //+:cnd:noEmit
    }
#if USE_UITESTS

#if __IOS__
    [Export("getCurrentPage:")]
    public NSString GetCurrentPageBackdoor(NSString value) => new NSString(App.GetCurrentPage());
#endif

#if __WASM__
	[System.Runtime.InteropServices.JavaScript.JSExport]
#endif
    public static string GetCurrentPage() => Shell?.RootFrame?.CurrentSourcePageType.ToString() ?? string.Empty;
#endif
}



//public partial class App : Application
//{
//    /// <summary>
//    /// Initializes the singleton application object. This is the first line of authored code
//    /// executed, and as such is the logical equivalent of main() or WinMain().
//    /// </summary>
//    public App()
//    {
//        this.InitializeComponent();
//    }

//    protected Window? MainWindow { get; private set; }
//    protected IHost? Host { get; private set; }

//    protected async override void OnLaunched(LaunchActivatedEventArgs args)
//    {
//        var builder = this.CreateBuilder(args)
//            // Add navigation support for toolkit controls such as TabBar and NavigationView
//            .UseToolkitNavigation()
//            .Configure(host => host
//#if DEBUG
//                // Switch to Development environment when running in DEBUG
//                .UseEnvironment(Environments.Development)
//#endif
//                .UseLogging(configure: (context, logBuilder) =>
//                {
//                    // Configure log levels for different categories of logging
//                    logBuilder
//                        .SetMinimumLevel(
//                            context.HostingEnvironment.IsDevelopment() ?
//                                LogLevel.Information :
//                                LogLevel.Warning)

//                        // Default filters for core Uno Platform namespaces
//                        .CoreLogLevel(LogLevel.Warning);

//                    // Uno Platform namespace filter groups
//                    // Uncomment individual methods to see more detailed logging
//                    //// Generic Xaml events
//                    //logBuilder.XamlLogLevel(LogLevel.Debug);
//                    //// Layout specific messages
//                    //logBuilder.XamlLayoutLogLevel(LogLevel.Debug);
//                    //// Storage messages
//                    //logBuilder.StorageLogLevel(LogLevel.Debug);
//                    //// Binding related messages
//                    //logBuilder.XamlBindingLogLevel(LogLevel.Debug);
//                    //// Binder memory references tracking
//                    //logBuilder.BinderMemoryReferenceLogLevel(LogLevel.Debug);
//                    //// DevServer and HotReload related
//                    //logBuilder.HotReloadCoreLogLevel(LogLevel.Information);
//                    //// Debug JS interop
//                    //logBuilder.WebAssemblyLogLevel(LogLevel.Debug);

//                }, enableUnoLogging: true)
//                .UseConfiguration(configure: configBuilder =>
//                    configBuilder
//                        .EmbeddedSource<App>()
//                        .Section<AppConfig>()
//                )
//                // Enable localization (see appsettings.json for supported languages)
//                .UseLocalization()
//                // Register Json serializers (ISerializer and ISerializer)
//                .UseSerialization((context, services) => services
//                    .AddContentSerializer(context)
//                    .AddJsonTypeInfo(WeatherForecastContext.Default.IImmutableListWeatherForecast))
//                .UseHttp((context, services) =>
//                {
//#if DEBUG
//                // DelegatingHandler will be automatically injected
//                services.AddTransient<DelegatingHandler, DebugHttpHandler>();
//#endif
//                    services.AddSingleton<IWeatherCache, WeatherCache>();
//                    services.AddKiotaClient<WeatherServiceClient>(
//                    context,
//                    options: new EndpointOptions { Url = context.Configuration["ApiClient:Url"]! }
//                    );

//                })
//                .UseAuthentication(auth =>
//    auth.AddOidc(name: "OidcAuthentication")
//                )
//                .ConfigureServices((context, services) =>
//                {
//                    // TODO: Register your services
//                    //services.AddSingleton<IMyService, MyService>();
//                })
//                .UseNavigation(ReactiveViewModelMappings.ViewModelMappings, RegisterRoutes)
//            );
//        MainWindow = builder.Window;

//#if DEBUG
//        MainWindow.UseStudio();
//#endif
//        MainWindow.SetWindowIcon();

//        Host = await builder.NavigateAsync<Shell>
//            (initialNavigate: async (services, navigator) =>
//            {
//                var auth = services.GetRequiredService<IAuthenticationService>();
//                var authenticated = await auth.RefreshAsync();
//                if (authenticated)
//                {
//                    await navigator.NavigateViewModelAsync<MainModel>(this, qualifier: Qualifiers.Nested);
//                }
//                else
//                {
//                    await navigator.NavigateViewModelAsync<LoginModel>(this, qualifier: Qualifiers.Nested);
//                }
//            });
//    }

//    private static void RegisterRoutes(IViewRegistry views, IRouteRegistry routes)
//    {
//        views.Register(
//            new ViewMap(ViewModel: typeof(ShellModel)),
//            new ViewMap<LoginPage, LoginModel>(),
//            new ViewMap<MainPage, MainModel>(),
//            new DataViewMap<SecondPage, SecondModel, Entity>()
//        );

//        routes.Register(
//            new RouteMap("", View: views.FindByViewModel<ShellModel>(),
//                Nested:
//                [
//                    new ("Login", View: views.FindByViewModel<LoginModel>()),
//                    new ("Main", View: views.FindByViewModel<MainModel>(), IsDefault:true),
//                    new ("Second", View: views.FindByViewModel<SecondModel>()),
//                ]
//            )
//        );
//    }
//}
