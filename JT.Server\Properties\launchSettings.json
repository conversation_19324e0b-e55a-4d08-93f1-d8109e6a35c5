{"profiles": {"JT.Server": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}", "applicationUrl": "http://localhost:5001;https://localhost:5002"}, "IIS Express": {"commandName": "IISExpress", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}"}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:5001", "sslPort": 5002}}}