using System.Reflection;

namespace JT.Server.Apis;

/// <summary>
/// Base controller providing utility methods for loading and saving mock data.
/// </summary>
public abstract class JTControllerBase : ControllerBase
{
    //load data from embedded resource (legacy method for backward compatibility)
    /// <summary>
    /// Loads data from an embedded resource.
    /// </summary>
    /// <typeparam name="T">The type of the data to load.</typeparam>
    /// <param name="fileName">The name of the embedded resource file.</param>
    /// <returns>The deserialized data of type <typeparamref name="T"/>.</returns>
    protected T LoadData<T>(string fileName)
    {
        var assembly = Assembly.GetExecutingAssembly();
        var resourceName = $"JT.Server.AppData.{fileName}";
        using var stream = assembly.GetManifestResourceStream(resourceName);
        if (stream == null) throw new FileNotFoundException($"Resource '{resourceName}' not found.");
        using var reader = new StreamReader(stream);
        var json = reader.ReadToEnd();
        return JsonSerializer.Deserialize<T>(json) ?? throw new JsonException($"Failed to deserialize JSON from '{resourceName}'.");
    }

    //load data from embedded resource or file system
    /// <summary>
    /// Asynchronously loads mock data from the file system or an embedded resource.
    /// </summary>
    /// <typeparam name="T">The type of the data to load.</typeparam>
    /// <param name="fileName">The name of the file or embedded resource.</param>
    /// <returns>A task representing the asynchronous operation, with the deserialized data of type <typeparamref name="T"/>.</returns>
    protected async Task<T> GetMockData<T>(string fileName)
    {
        // First try to load from file system (for development) - relative to project root
        var filePath = System.IO.Path.Combine("..", "AppData", fileName); // Explicitly use System.IO.Path
        if (System.IO.File.Exists(filePath)) // Explicitly use System.IO.File
        {
            var json = await System.IO.File.ReadAllTextAsync(filePath); // Explicitly use System.IO.File
            return JsonSerializer.Deserialize<T>(json) ?? throw new JsonException($"Failed to deserialize JSON from '{filePath}'.");
        }

        // Fallback to embedded resource (configured in .csproj as LinkBase="AppData")
        var assembly = Assembly.GetExecutingAssembly();
        var resourceName = $"JT.Server.AppData.{fileName}";
        using var stream = assembly.GetManifestResourceStream(resourceName);
        if (stream == null) throw new FileNotFoundException($"Resource '{resourceName}' not found.");
        using var reader = new StreamReader(stream);
        var jsonContent = await reader.ReadToEndAsync();
        return JsonSerializer.Deserialize<T>(jsonContent) ?? throw new JsonException($"Failed to deserialize JSON from '{resourceName}'.");
    }

    /// <summary>
    /// Asynchronously saves mock data to the file system.
    /// </summary>
    /// <typeparam name="T">The type of the data to save.</typeparam>
    /// <param name="fileName">The name of the file to save the data to.</param>
    /// <param name="data">The data to save.</param>
    /// <returns>A task representing the asynchronous save operation.</returns>
    protected async Task SaveMockData<T>(string fileName, T data)
    {
        // Save to the root AppData folder (relative to project root)
        var filePath = System.IO.Path.Combine("..", "AppData", fileName); // Explicitly use System.IO.Path
        var directory = System.IO.Path.GetDirectoryName(filePath); // Explicitly use System.IO.Path
        if (!string.IsNullOrEmpty(directory) && !System.IO.Directory.Exists(directory)) // Explicitly use System.IO.Directory
        {
            System.IO.Directory.CreateDirectory(directory); // Explicitly use System.IO.Directory
        }

        var json = JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
        await System.IO.File.WriteAllTextAsync(filePath, json); // Explicitly use System.IO.File
    }
}
