﻿<Flyout x:Class="JT.Presentation.Flyouts.ResponsiveDrawerFlyout"
		xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
		xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
		xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
		xmlns:local="using:JT.Presentation"
		xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
		xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
		xmlns:toolkit="using:Uno.UI.Toolkit"
		xmlns:uen="using:Uno.Extensions.Navigation.UI"
		xmlns:uer="using:Uno.Extensions.Reactive.UI"
		xmlns:ut="using:Uno.Themes"
		xmlns:utu="using:Uno.Toolkit.UI"
		x:Name="Root"
		Opening="OnOpening"
		Placement="Full"
		FlyoutPresenterStyle="{StaticResource BottomDrawerFlyoutPresenterStyle}"
		mc:Ignorable="d">

	<Grid x:Name="MainLayout"
		  uen:Region.Attached="True">
		<Frame HorizontalAlignment="Stretch"
			   VerticalAlignment="Stretch"
			   HorizontalContentAlignment="Stretch"
			   VerticalContentAlignment="Stretch"
			   uen:Region.Attached="true" />
	</Grid>

</Flyout>
