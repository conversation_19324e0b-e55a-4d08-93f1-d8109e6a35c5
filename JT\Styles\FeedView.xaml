﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
					xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
					xmlns:uer="using:Uno.Extensions.Reactive.UI"
					xmlns:winui="using:Microsoft.UI.Xaml.Controls"
					xmlns:skia="http://platform.uno/skia"
					xmlns:not_skia="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
					xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
					xmlns:utu="using:Uno.Toolkit.UI"
					mc:Ignorable="skia">

	<Style TargetType="uer:FeedView"
		   BasedOn="{StaticResource DefaultFeedViewStyle}" />


	<Style TargetType="uer:FeedView"
		   x:Key="DefaultFeedViewStyle">
		<Setter Property="VerticalAlignment" Value="Stretch" />
		<Setter Property="HorizontalAlignment" Value="Stretch" />
		<Setter Property="VerticalContentAlignment" Value="Stretch" />
		<Setter Property="HorizontalContentAlignment" Value="Stretch" />
		<Setter Property="ProgressTemplate">
			<Setter.Value>
				<DataTemplate>
					<Grid>
						<ProgressRing IsActive="{Binding}"
									  Width="50"
									  Height="50"
									  VerticalAlignment="Center"
									  HorizontalAlignment="Center" />
					</Grid>
				</DataTemplate>
			</Setter.Value>
		</Setter>
		<Setter Property="ErrorTemplate">
			<Setter.Value>
				<DataTemplate>
					<Grid>
						<TextBlock Text="An error occurred."
								   VerticalAlignment="Center"
								   HorizontalAlignment="Center" />
					</Grid>
				</DataTemplate>
			</Setter.Value>
		</Setter>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="uer:FeedView">
					<Grid VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
						  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}">
						<VisualStateManager.CustomVisualStateManager>
							<uer:SmoothVisualStateManager>
								<uer:SmoothVisualStateRule MinDuration="0:0:0.500" />
								<uer:SmoothVisualStateRule Group="ProgressGroup"
														   To="Indeterminate"
														   Delay="0:0:0.250" />
							</uer:SmoothVisualStateManager>
						</VisualStateManager.CustomVisualStateManager>
						<VisualStateManager.VisualStateGroups>
							<VisualStateGroup x:Name="DataGroup">
								<VisualState x:Name="Undefined" />
								<VisualState x:Name="None">
									<VisualState.Setters>
										<Setter Target="UndefinedPresenter.Visibility" Value="Collapsed" />
										<Setter Target="NonePresenter.Visibility" Value="Visible" />
										<Setter Target="SomePresenter.Visibility" Value="Collapsed" />
									</VisualState.Setters>
								</VisualState>
								<VisualState x:Name="Some">
									<VisualState.Setters>
										<Setter Target="UndefinedPresenter.Visibility" Value="Collapsed" />
										<Setter Target="NonePresenter.Visibility" Value="Collapsed" />
										<Setter Target="SomePresenter.Visibility" Value="Visible" />
									</VisualState.Setters>
								</VisualState>
							</VisualStateGroup>
							<VisualStateGroup x:Name="ErrorGroup">
								<VisualState x:Name="NoError" />
								<VisualState x:Name="Error">
									<VisualState.Setters>
										<Setter Target="DataRoot.Visibility" Value="Collapsed" />
										<Setter Target="ErrorRoot.Visibility" Value="Visible" />
									</VisualState.Setters>
								</VisualState>
							</VisualStateGroup>
							<VisualStateGroup x:Name="ProgressGroup">
								<VisualStateGroup.Transitions>
									<VisualTransition To="Indeterminate">
										<Storyboard>
											<ObjectAnimationUsingKeyFrames Storyboard.TargetName="ProgressRoot"
																		   Storyboard.TargetProperty="Visibility">
												<DiscreteObjectKeyFrame KeyTime="0:0:0"
																		Value="Visible" />
											</ObjectAnimationUsingKeyFrames>
											<DoubleAnimation Storyboard.TargetName="ProgressPresenter"
															 Storyboard.TargetProperty="Opacity"
															 From="0"
															 To="1"
															 Duration="0:0:0.100" />
											<DoubleAnimation Storyboard.TargetName="DataRoot"
															 Storyboard.TargetProperty="Opacity"
															 To="0"
															 Duration="0:0:0.100" />
											<DoubleAnimation Storyboard.TargetName="ErrorRoot"
															 Storyboard.TargetProperty="Opacity"
															 To="0"
															 Duration="0:0:0.100" />
										</Storyboard>
									</VisualTransition>
									<VisualTransition From="Indeterminate">
										<Storyboard>
											<DoubleAnimation Storyboard.TargetName="ProgressPresenter"
															 Storyboard.TargetProperty="Opacity"
															 From="1"
															 To="0"
															 Duration="0:0:0.100" />
										</Storyboard>
									</VisualTransition>
								</VisualStateGroup.Transitions>
								<VisualState x:Name="NoProgress" />
								<VisualState x:Name="Indeterminate">
									<VisualState.Setters>
										<Setter Target="DataRoot.Visibility" Value="Collapsed" />
										<Setter Target="ErrorRoot.Visibility" Value="Collapsed" />
										<Setter Target="ProgressRoot.Visibility" Value="Visible" />
									</VisualState.Setters>
								</VisualState>
							</VisualStateGroup>
						</VisualStateManager.VisualStateGroups>

						<Grid x:Name="DataRoot">
							<ContentPresenter x:Name="UndefinedPresenter"
											  Content="{TemplateBinding Undefined}"
											  ContentTemplate="{TemplateBinding UndefinedTemplate}"
											  VerticalAlignment="Stretch"
											  HorizontalAlignment="Stretch"
											  VerticalContentAlignment="Stretch"
											  HorizontalContentAlignment="Stretch"
											  Visibility="Visible" />
							<ContentPresenter x:Name="NonePresenter"
											  Content="{TemplateBinding None}"
											  ContentTemplate="{TemplateBinding NoneTemplate}"
											  VerticalAlignment="Stretch"
											  HorizontalAlignment="Stretch"
											  VerticalContentAlignment="Stretch"
											  HorizontalContentAlignment="Stretch"
											  Visibility="Collapsed" />
							<ContentPresenter x:Name="SomePresenter"
											  Content="{TemplateBinding State}"
											  ContentTemplate="{TemplateBinding ValueTemplate}"
											  VerticalAlignment="Stretch"
											  HorizontalAlignment="Stretch"
											  VerticalContentAlignment="Stretch"
											  HorizontalContentAlignment="Stretch"
											  Visibility="Collapsed" />
						</Grid>

						<Grid x:Name="ErrorRoot"
							  Visibility="Collapsed">
							<ContentPresenter x:Name="ErrorPresenter"
											  Content="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=State.Error}"
											  ContentTemplate="{TemplateBinding ErrorTemplate}"
											  HorizontalAlignment="Stretch"
											  VerticalAlignment="Stretch"
											  VerticalContentAlignment="Stretch"
											  HorizontalContentAlignment="Stretch" />
						</Grid>

						<Grid x:Name="ProgressRoot"
							  Visibility="Collapsed">
							<ContentPresenter x:Name="ProgressPresenter"
											  Content="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=State.Progress}"
											  ContentTemplate="{TemplateBinding ProgressTemplate}"
											  HorizontalAlignment="Stretch"
											  VerticalAlignment="Stretch"
											  VerticalContentAlignment="Stretch"
											  HorizontalContentAlignment="Stretch" />
						</Grid>
					</Grid>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>

	<!--
		We need to redefine this as packages cannot ship multiple styles at once by default.
		We need to take a similar approach to what we did for material to pack multiple styles.
	-->
	<Style TargetType="uer:FeedView"
		   x:Key="CompositeFeedViewStyle">
		<Setter Property="VerticalAlignment" Value="Stretch" />
		<Setter Property="HorizontalAlignment" Value="Stretch" />
		<Setter Property="VerticalContentAlignment" Value="Stretch" />
		<Setter Property="HorizontalContentAlignment" Value="Stretch" />
		<Setter Property="ProgressTemplate">
			<Setter.Value>
				<DataTemplate>
					<Grid Background="#33FFFFFF"
						  Width="75"
						  Height="75"
						  HorizontalAlignment="Center"
						  VerticalAlignment="Center">
						<ProgressRing IsActive="{Binding}"
									  Width="50"
									  Height="50"
									  VerticalAlignment="Center"
									  HorizontalAlignment="Center" />
					</Grid>
				</DataTemplate>
			</Setter.Value>
		</Setter>
		<Setter Property="ErrorTemplate">
			<Setter.Value>
				<DataTemplate>
					<Grid Background="Red">
						<TextBlock Text="An error occurred."
								   VerticalAlignment="Center"
								   HorizontalAlignment="Center" />
					</Grid>
				</DataTemplate>
			</Setter.Value>
		</Setter>
		<Setter Property="Template">
			<Setter.Value>
				<ControlTemplate TargetType="uer:FeedView">
					<Grid VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
						  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}">
						<VisualStateManager.CustomVisualStateManager>
							<uer:SmoothVisualStateManager>
								<uer:SmoothVisualStateRule MinDuration="0:0:0.500" />
								<uer:SmoothVisualStateRule Group="ProgressGroup"
														   To="Indeterminate"
														   Delay="0:0:0.250" />
							</uer:SmoothVisualStateManager>
						</VisualStateManager.CustomVisualStateManager>
						<VisualStateManager.VisualStateGroups>
							<VisualStateGroup x:Name="DataGroup">
								<VisualState x:Name="Undefined" />
								<VisualState x:Name="None">
									<VisualState.Setters>
										<Setter Target="UndefinedPresenter.Visibility" Value="Collapsed" />
										<Setter Target="NonePresenter.Visibility" Value="Visible" />
										<Setter Target="SomePresenter.Visibility" Value="Collapsed" />
									</VisualState.Setters>
								</VisualState>
								<VisualState x:Name="Some">
									<VisualState.Setters>
										<Setter Target="UndefinedPresenter.Visibility" Value="Collapsed" />
										<Setter Target="NonePresenter.Visibility" Value="Collapsed" />
										<Setter Target="SomePresenter.Visibility" Value="Visible" />
									</VisualState.Setters>
								</VisualState>
							</VisualStateGroup>
							<VisualStateGroup x:Name="ErrorGroup">
								<VisualStateGroup.Transitions>
									<VisualTransition To="Error">
										<Storyboard>
											<ObjectAnimationUsingKeyFrames Storyboard.TargetName="ErrorRoot"
																		   Storyboard.TargetProperty="Visibility">
												<DiscreteObjectKeyFrame KeyTime="0:0:0"
																		Value="Visible" />
											</ObjectAnimationUsingKeyFrames>
											<DoubleAnimation Storyboard.TargetName="ErrorPresenterTransform"
															 Storyboard.TargetProperty="Y"
															 From="-10"
															 To="0"
															 Duration="0:0:.5" />
											<DoubleAnimation Storyboard.TargetName="ErrorRoot"
															 Storyboard.TargetProperty="Opacity"
															 From="0"
															 To="1"
															 Duration="0:0:.5" />
										</Storyboard>
									</VisualTransition>
								</VisualStateGroup.Transitions>
								<VisualState x:Name="NoError" />
								<VisualState x:Name="Error">
									<VisualState.Setters>
										<Setter Target="ErrorRoot.Visibility" Value="Visible" />
									</VisualState.Setters>
									<Storyboard>
										<DoubleAnimation Storyboard.TargetName="ErrorRoot"
														 Storyboard.TargetProperty="Opacity"
														 To="0"
														 Duration="0:0:1"
														 BeginTime="0:0:10" />
									</Storyboard>
								</VisualState>
							</VisualStateGroup>
							<VisualStateGroup x:Name="ProgressGroup">
								<VisualState x:Name="NoProgress" />
								<VisualState x:Name="Indeterminate">
									<VisualState.Setters>
										<Setter Target="ProgressPresenter.Visibility" Value="Visible" />
									</VisualState.Setters>
								</VisualState>
							</VisualStateGroup>
						</VisualStateManager.VisualStateGroups>

						<ContentPresenter x:Name="UndefinedPresenter"
										  Content="{TemplateBinding Undefined}"
										  ContentTemplate="{TemplateBinding UndefinedTemplate}"
										  VerticalAlignment="Stretch"
										  HorizontalAlignment="Stretch"
										  VerticalContentAlignment="Stretch"
										  HorizontalContentAlignment="Stretch"
										  Visibility="Visible" />
						<ContentPresenter x:Name="NonePresenter"
										  Content="{TemplateBinding None}"
										  ContentTemplate="{TemplateBinding NoneTemplate}"
										  VerticalAlignment="Stretch"
										  HorizontalAlignment="Stretch"
										  VerticalContentAlignment="Stretch"
										  HorizontalContentAlignment="Stretch"
										  Visibility="Collapsed" />
						<ContentPresenter x:Name="SomePresenter"
										  Content="{TemplateBinding State}"
										  ContentTemplate="{TemplateBinding ValueTemplate}"
										  VerticalAlignment="Stretch"
										  HorizontalAlignment="Stretch"
										  VerticalContentAlignment="Stretch"
										  HorizontalContentAlignment="Stretch"
										  Visibility="Collapsed" />

						<Border x:Name="ErrorRoot"
								HorizontalAlignment="Center"
								VerticalAlignment="Top"
								Visibility="Collapsed"
								Padding="20">
							<Border.RenderTransform>
								<TranslateTransform x:Name="ErrorPresenterTransform" />
							</Border.RenderTransform>
							<ContentPresenter Content="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=State.Error}"
											  ContentTemplate="{TemplateBinding ErrorTemplate}"
											  HorizontalAlignment="Stretch"
											  VerticalAlignment="Stretch"
											  VerticalContentAlignment="Stretch"
											  HorizontalContentAlignment="Stretch" />
						</Border>

						<ContentPresenter x:Name="ProgressPresenter"
										  Content="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=State.Progress}"
										  ContentTemplate="{TemplateBinding ProgressTemplate}"
										  HorizontalAlignment="Stretch"
										  VerticalAlignment="Stretch"
										  VerticalContentAlignment="Stretch"
										  HorizontalContentAlignment="Stretch"
										  Visibility="Collapsed"
										  Opacity="1" />
					</Grid>
				</ControlTemplate>
			</Setter.Value>
		</Setter>
	</Style>
</ResourceDictionary>
