namespace JT.Server.Entities;

public class NotificationData
{
	public string? Id { get; set; }
	public Guid? UserId { get; set; }
	public string? Title { get; set; }
	public string? Body { get; set; }
	public string? Description { get; set; } // For backward compatibility
	public string? Type { get; set; }
	public string? Priority { get; set; } = "normal"; // low, normal, high, critical
	public DateTime? Timestamp { get; set; }
	public DateTime? Date { get; set; } // For backward compatibility
	public bool? Read { get; set; }
	public bool? IsRead { get; set; } // For backward compatibility
	public DateTime? ReadAt { get; set; }
	public string? ActionUrl { get; set; }
	public string? ImageUrl { get; set; }
}
