name: CI

on:
  push:
    branches:
      - main
      - release/**

  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - main
      - release/**
env:
  STEP_TIMEOUT_MINUTES: 60

jobs:
  smoke_test:
    name: Smoke Test (Debug Build of JT)
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Install Dependencies
        timeout-minutes: ${{ fromJSON(env.STEP_TIMEOUT_MINUTES) }}
        uses: "./.github/steps/install_dependencies"

      # Add  MSBuild to the PATH: https://github.com/microsoft/setup-msbuild
      - name: Setup MSBuild
        uses: microsoft/setup-msbuild@v1.3.1

      - name: Build JT (Debug)
        shell: pwsh
        run: msbuild ./JT/JT.csproj /r

  unit_test:
    name: Unit Tests
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Install Dependencies
        timeout-minutes: ${{ fromJSON(env.STEP_TIMEOUT_MINUTES) }}
        uses: "./.github/steps/install_dependencies"

      # Add  MSBuild to the PATH: https://github.com/microsoft/setup-msbuild
      - name: Setup MSBuild
        uses: microsoft/setup-msbuild@v1.3.1

      - name: Build JT.Tests (Release)
        shell: pwsh
        run: msbuild ./JT.Tests/JT.Tests.csproj /p:Configuration=Release /p:OverrideTargetFramework=net9.0 /r

      - name: Run Unit Tests
        shell: pwsh
        run: dotnet test ./JT.Tests/JT.Tests.csproj --no-build -c Release --logger GitHubActions --blame-crash --collect:"XPlat Code Coverage" -- DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.Format=opencover
