using System;
using System.Linq; // Added for All(char.IsDigit)
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Distributed; // Added for IDistributedCache
using Microsoft.Extensions.Logging; // Added for ILogger
using System.Text; // Added for Encoding

namespace JT.Identity.Services;

public class LocalSmsSender : ISmsSender
{
    private readonly Random _random = new();
    private readonly IDistributedCache _cache;
    private readonly ILogger<LocalSmsSender> _logger;
    private readonly TimeSpan _codeExpiration = TimeSpan.FromMinutes(5); // Default expiration

    // Updated constructor
    public LocalSmsSender(IDistributedCache cache, ILogger<LocalSmsSender> logger)
    {
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    private string GetCacheKey(string phoneNumber) => $"local_sms_code_{phoneNumber}";

    public Task<bool> ValidateCodeAsync(string phoneNumber, string code)
    {
        // Simple format validation - can be kept or removed if VerifyCodeAsync is sufficient
        _logger.LogDebug("Performing basic format validation for code for {PhoneNumber}", phoneNumber);
        return Task.FromResult(!string.IsNullOrEmpty(code) && code.Length == 6 && code.All(char.IsDigit));
    }

    public async Task<string> SendSmsAsync(string phoneNumber)
    {
        var code = _random.Next(100000, 999999).ToString();
        var cacheKey = GetCacheKey(phoneNumber);

        try
        {
            await _cache.SetStringAsync(cacheKey, code, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _codeExpiration
            });
            _logger.LogInformation("Generated and cached local SMS code {Code} for {PhoneNumber}. Key: {CacheKey}", code, phoneNumber, cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cache local SMS code for {PhoneNumber}. Key: {CacheKey}", phoneNumber, cacheKey);
            // Depending on requirements, you might still return the code or throw
        }
        
        // For local testing, we still return the code to be displayed in the UI
        return code;
    }

    public Task<string> GenerateTokenAsync(string phoneNumber)
    {
        // This method is not directly related to SMS code verification logic
        // but is part of the ISmsSender interface.
        _logger.LogDebug("Generating generic token for {PhoneNumber}", phoneNumber);
        return Task.FromResult(Guid.NewGuid().ToString("N"));
    }

    public async Task<bool> VerifyCodeAsync(string phoneNumber, string code)
    {
        if (string.IsNullOrEmpty(phoneNumber) || string.IsNullOrEmpty(code))
        {
            _logger.LogWarning("VerifyCodeAsync called with null or empty phoneNumber or code.");
            return false;
        }

        var cacheKey = GetCacheKey(phoneNumber);
        string? cachedCode = null;

        try
        {
            cachedCode = await _cache.GetStringAsync(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve local SMS code from cache for {PhoneNumber}. Key: {CacheKey}", phoneNumber, cacheKey);
            return false; // Fail verification if cache is inaccessible
        }

        if (cachedCode == null)
        {
            _logger.LogWarning("No cached SMS code found for {PhoneNumber} or it expired. Key: {CacheKey}", phoneNumber, cacheKey);
            return false;
        }

        if (cachedCode == code)
        {
            _logger.LogInformation("Local SMS code verified successfully for {PhoneNumber}. Key: {CacheKey}", phoneNumber, cacheKey);
            // Remove the code from cache after successful verification to prevent reuse
            try
            {
                await _cache.RemoveAsync(cacheKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to remove verified local SMS code from cache for {PhoneNumber}. Key: {CacheKey}", phoneNumber, cacheKey);
                // Continue, as verification was successful
            }
            return true;
        }
        else
        {
            _logger.LogWarning("Invalid local SMS code provided for {PhoneNumber}. Expected {ExpectedCode}, Got {ActualCode}. Key: {CacheKey}", phoneNumber, cachedCode, code, cacheKey);
            return false;
        }
    }
}
