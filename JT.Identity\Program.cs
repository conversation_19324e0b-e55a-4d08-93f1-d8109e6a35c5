// Copyright (c) Brock <PERSON> & <PERSON>. All rights reserved.
// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.

using IdentityServer4.Services;
using IdentityServer4.Validation;
using JT.Identity;
using JT.Identity.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.SystemConsole.Themes;
using System;

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
    .MinimumLevel.Override("Microsoft.Hosting.Lifetime", LogEventLevel.Information)
    .MinimumLevel.Override("System", LogEventLevel.Warning)
    .MinimumLevel.Override("Microsoft.AspNetCore.Authentication", LogEventLevel.Information)
    .Enrich.FromLogContext()
    // uncomment to write to Azure diagnostics stream
    //.WriteTo.File(
    //    @"D:\home\LogFiles\Application\identityserver.txt",
    //    fileSizeLimitBytes: 1_000_000,
    //    rollOnFileSizeLimit: true,
    //    shared: true,
    //    flushToDiskInterval: TimeSpan.FromSeconds(1))
    .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level}] {SourceContext}{NewLine}{Message:lj}{NewLine}{Exception}{NewLine}", theme: AnsiConsoleTheme.Code)
    .CreateLogger();

try
{
    Log.Information("Starting host...");
    
    var builder = WebApplication.CreateBuilder(args);
    
    // Add services to the container
    builder.Host.UseSerilog();
    
    // Configure services
    // http://docs.identityserver.io/en/latest/topics/cors.html#mixing-identityserver-s-cors-policy-with-asp-net-core-s-cors-policies
    // uncomment, if you want to add an MVC-based UI
    //builder.Services.AddControllersWithViews();
    
    builder.Services.AddSingleton<ICorsPolicyService>((container) => {
        var logger = container.GetRequiredService<ILogger<DefaultCorsPolicyService>>();
        return new DefaultCorsPolicyService(logger)
        {
            AllowedOrigins = { "http://localhost:5002", "https://localhost:5001", "https://localhost:5001/identity" },
        };
    });

    builder.Services.AddIdentityServer(options =>
    {
        // see https://identityserver4.readthedocs.io/en/latest/topics/resources.html
        options.EmitStaticAudienceClaim = true;
    })
        .AddDeveloperSigningCredential()
        .AddInMemoryApiScopes(Config.ApiScopes)
        .AddInMemoryClients(Config.Clients);
    
    builder.Services.AddTransient<IExtensionGrantValidator, SmsAuthService>();
    builder.Services.AddSingleton<ISmsSender, LocalSmsSender>();
    builder.Services.AddSingleton<IUserRepository, UserRepository>(); // Add this line
    var app = builder.Build();
    
    // Configure the HTTP request pipeline
    if (app.Environment.IsDevelopment())
    {
        app.UseDeveloperExceptionPage();
    }

    // uncomment if you want to add MVC
    //app.UseStaticFiles();
    //app.UseRouting();
    
    app.UseIdentityServer();

    // uncomment, if you want to add MVC
    //app.UseAuthorization();
    //app.UseEndpoints(endpoints =>
    //{
    //    endpoints.MapDefaultControllerRoute();
    //});
    
    app.Run();
    return 0;
}
catch (Exception ex)
{
    Log.Fatal(ex, "Host terminated unexpectedly.");
    return 1;
}
finally
{
    Log.CloseAndFlush();
}
