//using JT.Business.Services.JobTransfers;
using JT.Business.Services.TransferRequests;

namespace JT.Presentation;

public partial record FavoriteTransferRequestModel
{
	private readonly INavigator _navigator;
    private readonly ITransferRequestService _transferRequestService;
    private readonly IJobTransferService _jobTransfersService;
    private readonly IMessenger _messenger;


	public FavoriteTransferRequestModel(
		INavigator navigator,
        ITransferRequestService transferRequestService,
        IJobTransferService jobTransfersService,
        IMessenger messenger)
	{
		_navigator = navigator;
        _transferRequestService = transferRequestService;
        _jobTransfersService = jobTransfersService;
        _messenger = messenger;
	}

    public IListState<JobTransfer> SavedJobTransfers => ListState
        .Async(this, _jobTransfersService.GetSaved)
        .Observe(_messenger, cb => cb.Id);

    public IListState<TransferRequest> FavoriteTransferRequest => _transferRequestService.FavoritedTransferRequests;
}
