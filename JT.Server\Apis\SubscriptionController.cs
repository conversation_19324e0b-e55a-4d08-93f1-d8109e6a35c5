namespace JT.Server.Apis;

/// <summary>
/// Controller for managing subscription-related operations.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SubscriptionController : JTControllerBase
{
    private readonly string _subscriptionPlansFilePath = "SubscriptionPlans.json";

    /// <summary>
    /// Logger for the SubscriptionController.
    /// </summary>
    private readonly ILogger<SubscriptionController> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="SubscriptionController"/> class.
    /// </summary>
    /// <param name="logger">The logger instance for logging operations.</param>
    public SubscriptionController(ILogger<SubscriptionController> logger) => _logger = logger;

    /// <summary>
    /// Retrieves a list of subscription plans.
    /// </summary>
    /// <returns>A list of <see cref="SubscriptionPlanData"/> objects.</returns>
    [HttpGet("plans")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<SubscriptionPlanData>), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<SubscriptionPlanData>> GetSubscriptionPlans()
    {
        try
        {
            var plans = LoadData<List<SubscriptionPlanData>>(_subscriptionPlansFilePath);
            return Ok(plans.ToImmutableList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription plans");
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves a specific subscription plan by its ID.
    /// </summary>
    /// <param name="planId">The ID of the subscription plan.</param>
    /// <returns>The <see cref="SubscriptionPlanData"/> object if found; otherwise, a 404 status code.</returns>
    [HttpGet("plans/{planId}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<SubscriptionPlanData>), 200)]
    [ProducesResponseType(404)]
    public ActionResult<SubscriptionPlanData> GetSubscriptionPlan(string planId)
    {
        try
        {
            var plans = LoadData<List<SubscriptionPlanData>>(_subscriptionPlansFilePath);
            var plan = plans.FirstOrDefault(p => p.Id == planId);

            if (plan == null)
            {
                return NotFound();
            }

            return Ok(plan);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting subscription plan {PlanId}", planId);
            return StatusCode(500, "Internal server error");
        }
    }
}
