﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
					xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

	<FontFamily x:Key="MaterialIconFontFamily">ms-appx:///Assets/Fonts/MaterialIcons-Regular.ttf#Material Symbols Outlined</FontFamily>
	<FontFamily x:Key="FontAwesomeRegularFontFamily">ms-appx:///Assets/Fonts/FontAwesome-Regular.otf#Font Awesome 6 Free</FontFamily>
	<FontFamily x:Key="FontAwesomeSolidFontFamily">ms-appx:///Assets/Fonts/FontAwesome-Solid.otf#Font Awesome 6 Free Solid</FontFamily>
	<FontFamily x:Key="FontAwesomeBrandsFontFamily">ms-appx:///Assets/Fonts/FontAwesome-Brands.otf#Font Awesome 6 Brands</FontFamily>

	<Style x:Key="DefaultFontIconStyle"
		   TargetType="FontIcon">
		<Setter Property="FontFamily" Value="{StaticResource MaterialIconFontFamily}" />
		<Setter Property="FontSize" Value="24" />
	</Style>

	<Style x:Key="FontAwesomeRegularFontIconStyle"
		   BasedOn="{StaticResource DefaultFontIconStyle}"
		   TargetType="FontIcon">
		<Setter Property="FontFamily" Value="{StaticResource FontAwesomeRegularFontFamily}" />
	</Style>

	<Style x:Key="FontAwesomeSolidFontIconStyle"
		   BasedOn="{StaticResource DefaultFontIconStyle}"
		   TargetType="FontIcon">
		<Setter Property="FontFamily" Value="{StaticResource FontAwesomeSolidFontFamily}" />
		<Setter Property="FontWeight" Value="Bold" />
	</Style>

	<Style x:Key="FontAwesomeBrandsFontIconStyle"
		   BasedOn="{StaticResource DefaultFontIconStyle}"
		   TargetType="FontIcon">
		<Setter Property="FontFamily" Value="{StaticResource FontAwesomeBrandsFontFamily}" />
	</Style>

	<Style BasedOn="{StaticResource DefaultFontIconStyle}"
		   TargetType="FontIcon" />

</ResourceDictionary>
