# Technical Context - JT Job Transfer Application

## Technology Stack (Production-Ready)

### Core Framework Stack
- **.NET 9.0**: Latest .NET framework with modern C# features
- **Uno Platform 6.0.110**: Cross-platform UI framework (Windows, Android, iOS, WebAssembly, Desktop)
- **C# 12**: Latest language features, nullable reference types, implicit usings
- **XAML**: Declarative UI markup for cross-platform consistency

### Client-Side Technologies (Complete Implementation)
- **Uno.Sdk**: Single project multi-platform development
- **MVVM Architecture**: Complete implementation with CommunityToolkit.Mvvm
- **Kiota-Generated API Client**: Strongly-typed HTTP API client from OpenAPI specs
- **Microsoft.Extensions.Hosting**: Dependency injection and configuration
- **Route-Based Navigation**: Professional navigation system with data flow

### Server-Side Technologies (Production-Ready)
- **ASP.NET Core 9.0**: Web API framework with complete REST implementation
- **Custom Authentication**: Phone/password authentication with JWT tokens
- **OpenAPI 3.0.4**: Complete API documentation with Swagger
- **JSON Storage**: Production-ready for moderate scale (ready for database migration)
- **Comprehensive Validation**: Input validation with data annotations

### Business Implementation Technologies
- **Token Economy**: Advanced token system with caching and pagination
- **Payment Processing**: Abstract payment service ready for gateway integration
- **Commercial Promotions**: Complete advertising system with analytics
- **Subscription Management**: Tier-based subscription system (Bronze/Silver/Gold/Diamond)
- **Real-time Notifications**: Notification system with read/unread status

### Development Tools
- **Visual Studio 2022**: Primary IDE with full debugging support
- **GitHub Actions**: CI/CD pipeline for automated builds
- **Kiota**: API client generation from OpenAPI specifications
- **EditorConfig**: Code formatting standards

## Development Setup

### Prerequisites
- **Visual Studio 2022** (17.14+ recommended)
- **.NET 9.0 SDK**
- **Uno Platform workloads** (installed via uno-check)
- **Windows SDK 19041+** (for Windows targets)
- **Android SDK** (for Android targets)
- **Xcode** (for iOS targets, macOS only)

### Platform-Specific Requirements

#### Windows Development
- Windows 10/11 with latest updates
- Visual Studio with UWP and .NET workloads
- Windows SDK for target Windows versions

#### Android Development
- Android SDK 34+
- Android emulator or physical device
- Java 17+ for build tools

#### iOS Development (macOS only)
- Xcode 15+
- iOS Simulator or physical device
- Apple Developer account for device deployment

#### WebAssembly Development
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Local web server for testing

### Build Configuration
```xml
<!-- Multi-target framework support -->
<TargetFrameworks>
  net9.0-android;
  net9.0-ios;
  net9.0-windows10.0.26100;
  net9.0-browserwasm;
  net9.0-desktop;
  net9.0
</TargetFrameworks>
```

### Environment Setup Commands
```bash
# Install Uno Platform tools
dotnet tool install -g uno.check

# Verify and install workloads
uno-check --fix

# Restore packages
dotnet restore

# Build for specific platform
dotnet build -f net9.0-windows10.0.26100
```

## Technical Constraints

### Platform Limitations
- **iOS**: Requires macOS for development and deployment
- **WebAssembly**: Limited file system access, performance considerations
- **Android**: Various screen sizes and Android versions to support
- **Windows**: Multiple Windows versions and form factors

### Framework Constraints
- **Uno Platform**: Some WinUI features not available on all platforms
- **IdentityServer4**: Deprecated, migration required
- **JSON Data**: Not suitable for production, database needed

### Performance Constraints
- **WebAssembly**: Slower startup time, limited threading
- **Mobile**: Battery and memory usage considerations
- **Cross-Platform**: Lowest common denominator for some features

## Dependencies

### Production Dependencies
```xml
<!-- Core Uno Platform (implicit via Uno.Sdk) -->
<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
<PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
<PackageReference Include="Abp.ZeroCore.IdentityServer4" Version="8.4.0" />
<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
```

### Development Dependencies
```xml
<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.9.0" />
<PackageReference Include="NUnit" Version="4.1.0" />
<PackageReference Include="FluentAssertions" Version="6.12.0" />
<PackageReference Include="coverlet.collector" Version="6.0.2" />
```

### Pre-Release Dependencies (Risk)
```xml
<PackageReference Include="LiveChartsCore.SkiaSharpView.Uno.WinUI" Version="2.0.0-rc4.5" />
<PackageReference Include="Mapsui.Uno.WinUI" Version="5.0.0-beta.4" />
<PackageReference Include="Uno.UITest.Helpers" Version="1.1.0-dev.70" />
```

## Tool Usage Patterns

### Development Workflow
1. **Code**: Visual Studio 2022 with Uno Platform extensions
2. **Build**: MSBuild with multi-target support
3. **Test**: NUnit runner with Visual Studio Test Explorer
4. **Debug**: Platform-specific debugging (VS debugger, browser dev tools)
5. **Deploy**: Platform-specific deployment tools

### CI/CD Pipeline
```yaml
# GitHub Actions workflow
- Build: MSBuild for all target frameworks
- Test: Run unit tests with coverage
- Package: Create platform-specific packages
- Deploy: Automated deployment to test environments
```

### Package Management
- **Central Package Management**: Directory.Packages.props
- **Version Control**: Dependabot for automated updates
- **Security**: Regular security scanning of dependencies

### Code Quality Tools
- **EditorConfig**: Consistent code formatting
- **Nullable Reference Types**: Compile-time null safety
- **Code Analysis**: Built-in .NET analyzers
- **Test Coverage**: Coverlet with GitHub Actions integration

## Configuration Management

### Environment Configuration
```json
// appsettings.json structure
{
  "AppConfig": { "Environment": "Production" },
  "ApiClient": { "UseNativeHandler": true },
  "OidcAuthentication": { /* OAuth settings */ },
  "LocalizationConfiguration": { "Cultures": ["es", "fr", "pt-BR", "en"] }
}
```

### Build Configuration
- **Debug**: Full debugging, UI automation enabled
- **Release**: Optimized builds, minimal logging
- **Platform-Specific**: Conditional compilation for platform features

### Feature Flags
```csharp
#if USE_MOCKS
    // Mock implementations for development
#endif

#if USE_UITESTS
    // UI automation support
#endif
```

## Known Technical Issues

### Critical Issues
1. **Service Registration**: Many services commented out in DI container
2. **Authentication**: IdentityServer4 deprecated, needs Duende migration
3. **Data Layer**: JSON files not suitable for production
4. **Error Handling**: Inconsistent patterns throughout codebase

### Performance Issues
1. **WebAssembly**: Slow initial load times
2. **Mobile**: Potential memory leaks in ViewModels
3. **API**: No caching strategy implemented

### Security Concerns
1. **Deprecated Dependencies**: IdentityServer4 security updates ended
2. **Input Validation**: Missing validation in API controllers
3. **HTTPS**: Development certificates need proper management

## Migration Roadmap

### Immediate (Next 2 weeks)
- Complete service registration in DI container
- Implement basic error handling patterns
- Add input validation to API controllers

### Short-term (1-2 months)
- Migrate from IdentityServer4 to Duende IdentityServer
- Replace JSON data storage with proper database
- Implement comprehensive testing suite

### Long-term (3-6 months)
- Add caching strategies for performance
- Implement offline-first capabilities
- Optimize WebAssembly performance
