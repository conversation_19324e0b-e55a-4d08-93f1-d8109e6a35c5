[{"Id": "pay-001", "UserId": "bb708644-d5cd-45ad-b565-07a0c4d0b320", "SubscriptionPlanId": "diamond", "Amount": 50.0, "Currency": "OMR", "PaymentMethod": "<PERSON>awani", "PaymentGateway": "<PERSON><PERSON><PERSON>", "Status": "completed", "TransactionId": "thw_12345678", "GatewayResponse": {"payment_id": "thw_12345678", "status": "paid", "amount": 5000, "currency": "OMR"}, "ServiceFee": 2.5, "GatewayFee": 1.0, "NetAmount": 46.5, "CreatedAt": "2024-01-20T16:20:00Z", "CompletedAt": "2024-01-20T16:21:15Z", "ExpiresAt": "2024-02-20T16:20:00Z"}, {"Id": "pay-002", "UserId": "61c8aae0-e41d-4ac0-aa10-eabe72ea1fbe", "SubscriptionPlanId": "silver", "Amount": 15.0, "Currency": "OMR", "PaymentMethod": "zaincash", "PaymentGateway": "ZainCash", "Status": "completed", "TransactionId": "zc_87654321", "GatewayResponse": {"transaction_id": "zc_87654321", "status": "success", "amount": 1500, "currency": "OMR"}, "ServiceFee": 0.75, "GatewayFee": 0.3, "NetAmount": 13.95, "CreatedAt": "2024-01-15T11:30:00Z", "CompletedAt": "2024-01-15T11:32:45Z", "ExpiresAt": "2024-02-15T11:30:00Z"}, {"Id": "pay-003", "UserId": "3c896419-e280-40e7-8552-240635566fed", "SubscriptionPlanId": "gold", "Amount": 30.0, "Currency": "OMR", "PaymentMethod": "<PERSON>awani", "PaymentGateway": "<PERSON><PERSON><PERSON>", "Status": "pending", "TransactionId": "thw_pending123", "ServiceFee": 1.5, "GatewayFee": 0.6, "NetAmount": 27.9, "CreatedAt": "2024-01-22T09:15:00Z", "ExpiresAt": "2024-02-22T09:15:00Z"}, {"Id": "pay-004", "UserId": "02cbd790-905f-4f07-bd67-bfe3a6a8c56c", "SubscriptionPlanId": "gold", "Amount": 30.0, "Currency": "OMR", "PaymentMethod": "<PERSON>awani", "PaymentGateway": "<PERSON><PERSON><PERSON>", "Status": "failed", "TransactionId": "thw_failed456", "ErrorMessage": "Insufficient funds", "ServiceFee": 1.5, "GatewayFee": 0.6, "NetAmount": 27.9, "CreatedAt": "2024-01-18T14:20:00Z", "FailedAt": "2024-01-18T14:22:30Z"}]